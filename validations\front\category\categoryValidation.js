const Joi = require('joi');
const { categoryTypes } = require('../../../config/component.constant');

class CategoryValidation {
    listCategory(params) {
        const schema = Joi.object({
            category_type: Joi.string().trim().valid(...Object.values(categoryTypes)).required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new CategoryValidation();