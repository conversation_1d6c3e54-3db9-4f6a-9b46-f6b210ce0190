const { default: slugify } = require('slugify');
const { slugReplacements } = require('../config/component.constant');


function createSlug(input) {

    // Replace special symbols based on the mapping
    Object.keys(slugReplacements).forEach((symbol) => {
        const regex = new RegExp(`\\${symbol}`, 'g'); // Escape special chars for regex
        input = input.replace(regex, slugReplacements[symbol]);
    });

    return slugify(input, {
        lower: true,    // Convert to lowercase
        strict: true,   // Remove characters that do not match the whitelist
        locale: 'en',   // Specify the locale for character conversions
        replacement: '-', // Replace spaces with dashes
        remove: /[*+~.()'"!:@]/g // Remove special characters
    });
}

module.exports = createSlug;