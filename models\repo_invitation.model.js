const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { gitlabAccessLevel, invitationStatus } = require('../config/gitlab.constant');

const RepoInvitationSchema = new Schema({
    token: {
        type: String,
        require: true
    },
    invited_email: {
        type: String,
        require: true
    },
    repo_id: {
        type: mongoose.Types.ObjectId,
        ref: 'gitlab_repository',
        required: true
    },
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    },
    gitlab_user_id: {
        type: Number, // This will store the original GitLab user ID as a Number
        required: true
    },
    access_level: {
        type: String,
        enum: Object.keys(gitlabAccessLevel),
        default: 'DEVELOPER',
        required: true
    },
    status: {
        type: String,
        enum: Object.keys(invitationStatus),
        default: invitationStatus.PENDING,
        required: true
    },
    is_used: {
        type: Boolean,
        default: true
    },
    expires_at: {
        type: Date
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const RepoInvitation = mongoose.model('repo_invitation', RepoInvitationSchema);

module.exports = {
    RepoInvitation
};
