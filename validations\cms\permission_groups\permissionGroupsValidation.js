const Joi = require('joi');
class PermissionValidation {
    createPermissionGroup(value) {


        const schema = Joi.object({
            permission_group: Joi.array().items(Joi.object({
                group_name: Joi.string().trim().required(),
                module_name: Joi.string().trim().required(),
                permission_list: Joi.array().items(Joi.string().trim()).min(1).unique().required(),
                is_active: Joi.boolean().optional()
            })).required()
        });
        return schema.validate(value);
    }

    updatePermissionGroup(value) {
        const schema = Joi.object({
            group_name: Joi.string().trim().optional(),
            module_name: Joi.string().trim().optional(),
            permission_list: Joi.array().items(Joi.string().trim()).min(1).unique().optional(),
            is_active: Joi.boolean().optional()
        });
        return schema.validate(value);
    }

    updatePermissionGroupStatus(value) {
        const schema = Joi.object({
            is_active: Joi.boolean().required()
        });
        return schema.validate(value);
    }
}

module.exports = new PermissionValidation();