const logger = require('../config/logger');

async function verifyGitLabSignature(req, res, next) {
    try {
        const gitlabToken = req.headers['x-gitlab-token'];
        const secretToken = process.env.GIT_LAB_WEB_HOOK_TOKEN;

        if (!gitlabToken || gitlabToken !== secretToken) {
            return res.status(401).json({ message: 'Unauthorized - Invalid Token' });
        }
        next();
    } catch (error) {
        // Catch any errors that occur within the try block
        logger.error(`Error in middleware function verifyGitLabSignature ${error}`);
    }
};

module.exports = {
    verifyGitLabSignature
};

