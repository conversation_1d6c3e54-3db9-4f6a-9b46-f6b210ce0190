const express = require('express');
const router = express.Router();

const { createElementPlaceHolder, updateElement, getDraftElementDetails, publishDraftElements, createElementVariant, downloadElementCode, makePrivatePublishedElement } = require('../../controller/front/element.controller');

router.post('/create/placeholder', createElementPlaceHolder);
router.put('/update/:id', updateElement);
router.get('/get-element/draft/:id', getDraftElementDetails);
router.post('/:id/publish', publishDraftElements);
router.post('/:element_id/create-variant', createElementVariant);
router.get('/:element_id/download', downloadElementCode);
router.put('/:id/unpublish', makePrivatePublishedElement);

module.exports = router;