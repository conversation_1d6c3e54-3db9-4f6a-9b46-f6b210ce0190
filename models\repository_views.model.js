const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const GitlabRepository = require('../models/gitlab_repository.model').GitlabRepository;
const logger = require('../config/logger');

const repositoryViewSchema = new Schema({
    repo_id: {
        type: mongoose.Types.ObjectId,
        ref: 'gitlab_repository',
        required: true
    },
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

// Post-save hook to update statistics
repositoryViewSchema.post('save', async function () {
    try {
        const viewDoc = this;

        if (viewDoc.repo_id) {
            // Update main repository view count
            await GitlabRepository.updateOne({
                _id: viewDoc.repo_id
            }, {
                $inc: {
                    total_views: 1
                }
            });
        }
    } catch (error) {
        logger.error(`Error in view post-save hook: ${error}`);
    }
});

const RepositoryView = mongoose.model('repository_view', repositoryViewSchema);

module.exports = {
    RepositoryView
};