const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { fiatActivity } = require('./../config/component.constant');

const UserFiatHistorySchema = new Schema({
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    },
    amount: {
        type: Number,
        required: true,
        default: 0
    },
    activity: {
        type: String,
        enum: Object.values(fiatActivity),
        required: true
    },
    description: {
        type: String
    },
    transaction_id: {
        type: String
    },
    meta_data: {
        type: Object
    },
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const UserFiatHistory = mongoose.model('user_fiat_history', UserFiatHistorySchema);

module.exports = {
    UserFiatHistory
};