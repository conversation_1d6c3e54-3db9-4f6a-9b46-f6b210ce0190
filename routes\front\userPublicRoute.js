const express = require('express');
const router = express.Router();

const { getUserDetails, getProfileStatistics, getCreatedComponents, getPublicCodeSpaces, getUserOTP } = require('../../controller/front/userPublic.controller');

router.get('/:username/get-details', getUserDetails);
router.get('/:username/get-profile-statistics', getProfileStatistics);
router.post('/:username/get-created-components', getCreatedComponents);
router.post('/:username/public/code-spaces', getPublicCodeSpaces);
router.get('/get/last/otp', getUserOTP);

module.exports = router;