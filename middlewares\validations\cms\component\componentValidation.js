const { createComponent, updateComponent, updateComponentStatus, createComponentRepository, linkCodeSpacesToComponent } = require('../../../../validations/cms/component/componentValidation');
class ComponentValidationMiddleware {
    createComponentValidation(req, res, next) {
        const { value, error } = createComponent(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createComponentRepositoryValidation(req, res, next) {
        const { value, error } = createComponentRepository(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateComponentValidation(req, res, next) {
        const { value, error } = updateComponent(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateComponentStatusValidation(req, res, next) {
        const { value, error } = updateComponentStatus(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    linkCodeSpacesToComponentValidation(req, res, next) {
        const { value, error } = linkCodeSpacesToComponent(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new ComponentValidationMiddleware();