const { componentState, componentType, categoryTypes } = require('../../config/component.constant');
const constants = require('../../config/constants');
const logger = require('../../config/logger');
const { ReS, escapeRegex, buildTreeFromArray } = require('../../services/general.helper');
const Category = require('../../models/category.model').Category;
const Components = require('../../models/component.model').Components;

async function getAllCategory(req, res) {
    try {
        const { category_type } = req.body;

        // Determine the project type based on the selected category type.
        const projectType = category_type === categoryTypes.WEB_ELEMENTS ? componentType.ELEMENTS : componentType.MOBILE;
        // Define filter conditions to fetch only published elements
        const elementCondition = {
            component_type: projectType,
            component_state: componentState.PUBLISHED
        };
        // Fetch all distinct category IDs linked to active elements
        const activeCategoryIds = await Components.distinct('category_id', elementCondition).lean();

        // Fetch all categories (including their parent categories) in a single query
        const categories = await Category.find(
            { _id: { $in: activeCategoryIds }, is_deleted: false, category_type: category_type }
        ).populate({
            path: 'parent_id',
            select: 'category_name category_slug image_url short_description is_active',
            match: { is_deleted: false } // Only fetch active parent categories
        }).lean();

        // Use a Map to store unique categories (avoids duplicates)
        const flatCategory = new Map();

        categories.forEach((category) => {
            // Add the current category to the map
            flatCategory.set(category._id.toString(), {
                _id: category._id,
                category_name: category.category_name,
                category_slug: category.category_slug,
                image_url: category.image_url,
                is_active: category.is_active,
                short_description: category.short_description,
                created_at: category.created_at,
                parent_id: category.parent_id?._id || undefined // Store parent_id if available
            });

            // Add the parent category (if it exists) to the map to ensure it is included
            if (category.parent_id) {
                flatCategory.set(category.parent_id._id.toString(), {
                    _id: category.parent_id._id,
                    category_name: category.parent_id.category_name,
                    category_slug: category.parent_id.category_slug,
                    image_url: category.parent_id.image_url,
                    is_active: category.parent_id.is_active,
                    short_description: category.parent_id.short_description,
                    created_at: category.parent_id.created_at
                });
            }
        });

        // Convert the Map values into an array and build the category tree (with sorting)
        const categoryTree = buildTreeFromArray([...flatCategory.values()]);

        // Send the response with the formatted category tree
        return ReS(res, constants.success_code, 'Data Fetched', categoryTree);
    } catch (err) {
        // Log the error for debugging
        logger.error(`Error at Front Controller getAllCategory: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllCategoryAlongWithParent(req, res) {
    try {
        const { is_active, category_name } = req.body;

        // Set default sort
        const sort = {
            'category_name': 1
        };

        // Set default conditions
        const conditions = {
            is_deleted: false,
            is_active: true
        };

        if (is_active != undefined) {
            conditions['is_active'] = is_active;
        }

        if (category_name) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(category_name);
            conditions['$or'] = [{
                'category_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        const pipelines = [{
            $match: conditions
        },
        {
            $lookup: {
                from: 'components',
                let: {
                    category_id: '$_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$category_id', '$$category_id']
                            }, {
                                $eq: ['$is_active', true]
                            }]
                        }
                    }
                }, {
                    $project: {
                        title: 1
                    }
                }],
                as: 'components'
            }
        }, {
            $redact: {
                $cond: {
                    if: { $eq: [{ $size: '$components' }, 0] },
                    then: '$$PRUNE',
                    else: '$$KEEP'
                }
            }
        }, {
            $project: {
                category_name: 1,
                category_slug: 1
            }
        }, {
            $sort: sort
        }];

        const categoryList = await Category.aggregate(pipelines);

        return ReS(res, constants.success_code, 'Data Fetched', categoryList);
    } catch (err) {
        logger.error(`Error at Front Controller getAllCategoryAlongWithParent${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllCategoryWithComponents(req, res) {
    try {
        const totalDocuments = await Category.countDocuments();
        // Set default conditions
        const conditions = {
            'is_deleted': false,
            'is_active': true
        };
        // Set default sort
        const sort = {
            'total_views': -1,
            'created_at': -1
        };
        const limit = (req.body.limit) ? req.body.limit : 4;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const pipelines = [{
            $match: conditions
        },
        {
            $lookup: {
                from: 'components',
                let: {
                    category_id: '$_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$category_id', '$$category_id']
                            },
                            {
                                $eq: ['$component_state', componentState.PUBLISHED]
                            },
                            {
                                $eq: ['$component_type', componentType.ELEMENTS]
                            }]
                        }
                    }
                }, {
                    $project: {
                        title: 1,
                        slug: 1,
                        image_url: 1,
                        thumbnail_url: 1,
                        is_paid: 1,
                        description: 1,
                        created_at: 1,
                        views: 1,
                        likes: 1,
                        bookmarks: 1
                    }
                }, {
                    $sort: {
                        views: -1
                    }
                }],
                as: 'components'
            }
        },
        {
            $redact: {
                $cond: {
                    if: { $eq: [{ $size: '$components' }, 0] },
                    then: '$$PRUNE',
                    else: '$$KEEP'
                }
            }
        }];
        // Calculate filtered documents after $redact check
        const filterDocuments = await Category.aggregate(pipelines);
        pipelines.push({
            $project: {
                category_name: 1,
                category_slug: 1,
                short_description: 1,
                image_url: 1,
                created_at: 1,
                has_more: {
                    $cond: {
                        if: { $gt: [{ $size: '$components' }, 3] },
                        then: true,
                        else: false
                    }
                },
                components: { $slice: ['$components', 3] },
                is_active: 1
            }
        }, {
            $addFields: {
                total_views: {
                    $sum: {
                        $map: {
                            'input': '$components',
                            'as': 'component',
                            'in': '$$component.views'
                        }
                    }
                }
            }
        });
        pipelines.push({
            '$sort': sort
        });
        pipelines.push({
            '$skip': skip
        });
        pipelines.push({
            '$limit': limit
        });
        const categoryList = await Category.aggregate(pipelines);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments.length,
            list: categoryList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllCategoryWithComponents${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

module.exports = {
    getAllCategory,
    getAllCategoryAlongWithParent,
    getAllCategoryWithComponents
};