const { OAuth2Client } = require('google-auth-library');
const logger = require('../config/logger');

const getUserProfile = async (idToken) => {
    try {
        const googleClientId = process.env.GOOGLE_CLIENT_ID;
        const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET;
        const client = new OAuth2Client(googleClientId, googleClientSecret, '');
        return client.verifyIdToken({ idToken: idToken, audience: googleClientId }).then((login) => {
            const payload = login.getPayload();
            const audience = payload.aud;
            if (audience !== googleClientId) {
                throw new Error('Error while authenticating audience mismatch found');
            }
            return payload;
        }).catch((err) => {
            throw err;
        });
    } catch (err) {
        logger.error(`Error at google helper service getUserProfile${ err}`);
    }
};

module.exports = {
    getUserProfile
};