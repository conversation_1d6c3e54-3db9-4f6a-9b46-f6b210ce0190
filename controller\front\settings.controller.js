const constants = require('../../config/constants');
const { ReS, sendError } = require('../../services/general.helper');


const Settings = require('../../models/settings.model').Settings;

async function getSettingDetails(req, res) {
    try {
        const settingDetails = await Settings.findOne({
            'setting_slug': req.params.slug
        }).lean();
        return ReS(res, constants.success_code, 'Data Fetched', settingDetails);
    } catch (err) {
        console.log('Error at Front Controller getSettingDetails', err);
        return sendError(res, err);
    }
}

module.exports = {
    getSettingDetails
};