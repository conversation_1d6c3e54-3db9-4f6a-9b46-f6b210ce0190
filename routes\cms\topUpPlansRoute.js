const express = require('express');
const router = express.Router();

const { createPlanValidation, updatePlanStatusValidation, updatePlanValidation } = require('../../middlewares/validations/cms/top_up_plans/topUpPlansValidation');

const { createTopUpPlan, updatePlanStatus, getAllPlansSortList, getAllPlans, getPlanDetails, updateTopUpPlan, deletePlan } = require('../../controller/cms/topUpPlans.controller');

router.post('/create', createPlanValidation, createTopUpPlan);
router.put('/update/:id', updatePlanValidation, updateTopUpPlan);
router.delete('/delete/:id', deletePlan);
router.post('/list', getAllPlans);
router.get('/details/:id', getPlanDetails);
router.put('/update/status/:id', updatePlanStatusValidation, updatePlanStatus);
router.get('/sort-list', getAllPlansSortList);

module.exports = router;