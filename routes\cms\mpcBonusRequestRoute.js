const express = require('express');
const router = express.Router();
const { approveOrRejectRequest, listPendingRequests, getBonusRequestDetails } = require('../../controller/cms/mpcBonusRequest.controller');
const { approveOrRejectRequestValidation } = require('../../middlewares/validations/cms/mpc_bonus_point_request/mpcBonusPointRequestValidation');


router.post('/list', listPendingRequests);
router.get('/details/:id', getBonusRequestDetails);
router.put('/update-status/:id', approveOrRejectRequestValidation, approveOrRejectRequest);


module.exports = router;