const express = require('express');
const router = express.Router();

const { createPermissionValidation, updatePermissionStatusValidation } = require('../../middlewares/validations/cms/permissions/permissionsValidation');

const { createPermission, getAllPermissions, updatePermissionStatus, getAllPermissionSortList } = require('../../controller/cms/adminPermission.controller');

router.post('/create', createPermissionValidation, createPermission);
router.post('/list', getAllPermissions);
router.put('/update/status/:id', updatePermissionStatusValidation, updatePermissionStatus);
router.get('/sort-list', getAllPermissionSortList);

module.exports = router;