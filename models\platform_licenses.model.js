const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { licenseType } = require('../config/component.constant');

const platformLicenseSchema = new Schema({
    title: {
        type: String,
        required: true
    },
    slug: {
        type: String,
        required: true,
        unique: true
    },
    category: {
        type: String,
        required: true
    },
    license_type: [{
        type: String,
        enum: Object.values(licenseType)
    }],
    description: {
        type: String,
        required: true
    },
    dynamic_values: {
        type: String
    },
    is_active: {
        type: Boolean,
        default: true
    },
    is_default: {
        type: Boolean,
        default: false
    },
    version: {
        type: Number
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

platformLicenseSchema.set('autoIndex', true);

const PlatformLicense = mongoose.model('platform_licenses', platformLicenseSchema);

module.exports = {
    PlatformLicense
};