const Redis = require('ioredis');
require('dotenv').config();
const logger = require('./logger');

const host = process.env.REDIS_HOST || '127.0.0.1';
const port = process.env.REDIS_PORT || '6379';
// const password = process.env.REDIS_PASSWORD || '';

const redis = new Redis({
    port,
    host,
    // password,
    maxRetriesPerRequest: null, // Required for BullMQ
    connectTimeout: 10000,
    retryStrategy(times) {
        const delay = Math.min(times * 50, 2000);
        return delay;
    }
});

redis.on('connect', () => {
    logger.info(`Redis Client Connected at ${host}:${port}`);
});

redis.on('ready', () => {
    logger.info('Redis Client Ready.');
});

redis.on('error', (error) => {
    logger.error('Redis connection failed..', error);
});

module.exports = redis;
