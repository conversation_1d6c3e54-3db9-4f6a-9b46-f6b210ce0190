const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const repositoryStructureSchema = new Schema({
    repository_id: {
        type: mongoose.Types.ObjectId,
        ref: 'gitlab_repository',
        index: true
    },
    files_path: {
        type: [mongoose.Schema.Types.Mixed],
        default: []
    },
    files_tree: {
        type: [mongoose.Schema.Types.Mixed],
        default: []
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

repositoryStructureSchema.set('autoIndex', true);

const RepositoryStructure = mongoose.model('repository_structure', repositoryStructureSchema);

module.exports = {
    RepositoryStructure
};