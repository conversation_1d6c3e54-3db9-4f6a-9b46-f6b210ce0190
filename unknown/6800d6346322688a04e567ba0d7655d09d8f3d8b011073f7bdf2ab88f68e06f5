const { createRepository, fetchAllCodeSpace, createRepositoryFork, updateCodeSpace } = require('../../../../validations/front/code_space/codeSpaceValidation');

class CodeSpaceValidationMiddleware {
    createRepositoryValidation(req, res, next) {
        const { value, error } = createRepository(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    fetchAllCodeSpaceValidation(req, res, next) {
        const { value, error } = fetchAllCodeSpace(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createRepositoryForkValidation(req, res, next) {
        const { value, error } = createRepositoryFork(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateCodeSpaceValidation(req, res, next) {
        const { value, error } = updateCodeSpace(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new CodeSpaceValidationMiddleware();