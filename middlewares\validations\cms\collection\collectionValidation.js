const { createOrEditCollection } = require('../../../../validations/cms/collection/collectionValidation');

class CollectionValidationMiddleware {
    createOrEditCollectionValidation(req, res, next) {
        const { value, error } = createOrEditCollection(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new CollectionValidationMiddleware();