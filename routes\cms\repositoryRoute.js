const express = require('express');
const router = express.Router();

const { checkRepositoryNameAvailability, createRepositoryCommit, downloadRepositoryContent, getRepositoryBranches, getRepositoryCommitInfo, getRepositoryContent, getRepositoryFileContent, fetchRepositoryDetails, uploadFilesToRepository, downloadRepositoryFileContent, getRepositoryCommitDiff, getRepositoryContentRecursively, uploadMultipleFilesToRepository } = require('../../controller/cms/repository.controller');

const { createCommitValidation, createCommitWithFilesValidation } = require('../../middlewares/validations/cms/repository/repositoryValidation');

router.get('/check-name-availability', checkRepositoryNameAvailability);
router.get('/get-content/:id', getRepositoryContent);
router.get('/get-commits/:id', getRepositoryCommitInfo);
router.get('/get-file-content/:id/:path/raw', getRepositoryFileContent);
router.get('/download/:id', downloadRepositoryContent);
router.get('/get-branches/:id', getRepositoryBranches);
router.post('/submit-commit/:id', createCommitValidation, createRepositoryCommit);

router.get('/get-details/:id', fetchRepositoryDetails);
router.post('/upload-files/:id', uploadFilesToRepository);
router.get('/download/file/:id', downloadRepositoryFileContent);
router.get('/:id/commit/:sha/diff', getRepositoryCommitDiff);
router.get('/get-content/recursive/:id', getRepositoryContentRecursively);
router.post('/upload-files/multiple/:id', createCommitWithFilesValidation, uploadMultipleFilesToRepository);

module.exports = router;