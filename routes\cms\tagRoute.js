const express = require('express');
const router = express.Router();

const { createTagValidation, updateTagStatusValidation, updateTagValidation } = require('../../middlewares/validations/cms/tags/tagValidation');

const { createTag, deleteTags, getAllTags, getTagDetails, updateTag, updateTagStatus, getAllTagsSortList } = require('../../controller/cms/tags.controller');

router.post('/create-tags', createTagValidation, createTag);
router.put('/update-tags/:id', updateTagValidation, updateTag);
router.delete('/delete-tags/:id', deleteTags);
router.post('/get-all-tags', getAllTags);
router.get('/get-tags-details/:id', getTagDetails);
router.put('/update-tags/status/:id', updateTagStatusValidation, updateTagStatus);
router.get('/get-all-tags/sort-list', getAllTagsSortList);

module.exports = router;