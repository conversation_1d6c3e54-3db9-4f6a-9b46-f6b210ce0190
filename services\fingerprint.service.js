/* eslint-disable no-undef */
const { FingerprintJsServerApiClient, Region } = require('@fingerprintjs/fingerprintjs-pro-server-api');
const logger = require('../config/logger');

const client = new FingerprintJsServerApiClient({
    apiKey: process.env.FINGERPRINT_API_KEY,
    region: Region.AP
});

// Get visit history of a specific visitor
const getVisitorHistory = (visitorId) => {
    return new Promise((resolve, reject) => {
        logger.info(`Fingerprint service getVisitorHistory function called for visitorId: ${visitorId}`);
        client.getVisitorHistory(visitorId).then((visitorHistory) => {
            logger.info(visitorHistory);
            resolve(visitorHistory);
        }).catch((err) => {
            reject(err);
        });
    });
};

// Get a specific identification event
const getEvent = (requestId) => {
    return new Promise((resolve, reject) => {
        // Get visit history of a specific visitor
        logger.info(`Fingerprint service getEvent function called for requestId: ${requestId}`);
        client.getEvent(requestId).then((event) => {
            resolve(event.products);
        }).catch((err) => {
            reject(err);
        });
    });
};

module.exports = {
    getVisitorHistory,
    getEvent
};