const logger = require('../../config/logger');

exports.validationErrorHandler = function (err, req, res, next) {
    if (err) {
        if (err.name === 'ValidationError') {
            if (process.env.is_server == 'production') {
                logger.info(JSON.stringify({ key: err.name, message: err.message, api: req.originalUrl, method: req.method }));
                return res.status(400).json({ status: 400, message: err.message });
            }
            return res.status(400).json({ status: 400, message: err.message, data: err.name });
        }
        return next(err);
    }
    next();
};