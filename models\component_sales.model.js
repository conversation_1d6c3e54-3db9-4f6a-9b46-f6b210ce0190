const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const componentSalesSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    creator_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    purchaser_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    mpn_parity: {
        type: Number,
        default: 0
    },
    purchase_price: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    item_price: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    buyer_fee: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    total_earning: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const ComponentSales = mongoose.model('component_sales', componentSalesSchema);

module.exports = {
    ComponentSales
};