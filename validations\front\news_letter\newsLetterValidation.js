const Joi = require('joi');

class NewsLetterValidation {
    subscribeNewsLetter(value) {
        const schema = Joi.object({
            email: Joi.string().trim().email().required(),
            topic: Joi.string().trim().optional()
        });
        return schema.validate(value);
    }

    verifyNewsLetterSubscription(value) {
        const schema = Joi.object({
            subscribe_token: Joi.string().trim().required()
        });
        return schema.validate(value);
    }

    unSubscribeNewsLetter(value) {
        const schema = Joi.object({
            unsubscribe_token: Joi.string().trim().required()
        });
        return schema.validate(value);
    }
}

module.exports = new NewsLetterValidation();