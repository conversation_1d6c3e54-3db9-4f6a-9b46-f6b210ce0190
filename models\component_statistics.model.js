const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const componentStatisticsSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    likes: {
        total_likes: {
            type: Number,
            default: 0
        },
        countries: {
            type: mongoose.Schema.Types.Mixed
        }
    },
    views: {
        total_views: {
            type: Number,
            default: 0
        },
        countries: {
            type: mongoose.Schema.Types.Mixed
        }
    },
    bookmarks: {
        total_bookmarks: {
            type: Number,
            default: 0
        },
        countries: {
            type: mongoose.Schema.Types.Mixed
        }
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const ComponentStatistics = mongoose.model('component_statistics', componentStatisticsSchema);

module.exports = {
    ComponentStatistics
};