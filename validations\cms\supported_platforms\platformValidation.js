const Joi = require('joi');
class PlatformValidation {
    createPlatform(params) {
        const schema = Joi.object({
            title: Joi.string().required(),
            image_url: Joi.string().required(),
            is_active: Joi.boolean().optional(),
            description: Joi.string().optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updatePlatform(params) {
        const schema = Joi.object({
            title: Joi.string().optional(),
            is_active: Joi.boolean().optional(),
            description: Joi.string().optional(),
            image_url: Joi.string().optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updatePlatformStatus(params) {
        const schema = Joi.object({
            is_active: Joi.boolean().required()
        });
        return schema.validate(params);
    }
}

module.exports = new PlatformValidation();