const express = require('express');
const router = express.Router();

const { createStaticPage, getSingleStaticPage, getStaticPages, updateStaticPage, deleteStaticPage, updateStaticPageStatus } = require('../../controller/cms/staticPages.controller');
const { createStaticPageValidation, updateStaticPageValidation, updateStaticPageStatusValidation } = require('../../middlewares/validations/cms/static_page/staticPageValidation');


router.post('/create', createStaticPageValidation, createStaticPage);
router.get('/details/:id', getSingleStaticPage);
router.post('/list', getStaticPages);
router.put('/update/:id', updateStaticPageValidation, updateStaticPage);
router.delete('/delete/:id', deleteStaticPage);
router.put('/update/status/:id', updateStaticPageStatusValidation, updateStaticPageStatus);


module.exports = router;