const constants = require('../config/constants');
const logger = require('../config/logger');
const mongoose = require('mongoose');

const ComponentUnlockHistory = require('../models/component_unlock_history.model').ComponentUnlockHistory;
const GitlabRepository = require('../models/gitlab_repository.model').GitlabRepository;
const Components = require('../models/component.model').Components;
const GitlabUsers = require('../models/gitlab_users.model').GitlabUsers;

const { repositoryState, publishState } = require('../config/gitlab.constant');

async function checkIsAccessible(req, res, next) {
    try {
        // Extract the repository ID from request parameters
        const { id, path } = req.params;

        // Proceed if 'path' exists and includes '.md' or '.markdown' (case-insensitive)
        if (path && (path.toLowerCase().includes('.md') || path.toLowerCase().includes('.markdown'))) {
            return next(); // Move to the next middleware or function
        }

        if (!mongoose.isValidObjectId(id)) {
            return res.status(constants.bad_request_code).json({
                status: constants.bad_request_code,
                message: 'Invalid code-space ID provided.'
            });
        }
        // Find the repository in the GitLabRepository collection by its ID
        const repository = await GitlabRepository.findOne(
            { _id: id },
            'project_id component_id gitlab_user_id state published_state'
        ).lean();

        // If the repository is not found, return a 404 error
        if (!repository) {
            return res.status(constants.resource_not_found).json({
                status: constants.resource_not_found,
                message: 'Oops! code-space not found.'
            });
        }

        // Check if the repository public & published for end-user
        if (repository?.state == repositoryState.PUBLIC && repository?.published_state == publishState.PUBLISHED) {
            return next();
        }

        // Fetch the GitLab user associated with the current session user ID
        const gitlabUser = await GitlabUsers.findOne({
            user_id: req.session._id
        }).lean();

        // Check if the repository is not linked to any component
        if (!repository.component_id) {
            // If the current GitLab user does not match the repository's GitLab user, deny access
            if (gitlabUser?._id?.toString() == repository?.gitlab_user_id?.toString()) {
                return next();
            } else {
                return res.status(constants.bad_request_code).json({
                    status: constants.bad_request_code,
                    message: 'You do not have access to this code-space.'
                });
            }
        }

        // Fetch the associated component data using the component_id from the repository
        const componentData = await Components.findOne(
            { _id: repository.component_id },
            'component_type is_paid created_by_user'
        ).lean();

        // If the component is not found, return a 404 error
        if (!componentData) {
            return res.status(constants.resource_not_found).json({
                status: constants.resource_not_found,
                message: 'Oops! Component not found.'
            });
        }

        // Check if the component is paid and the current user is not the creator
        if (componentData?.is_paid && componentData?.created_by_user?.toString() !== req.session._id) {
            // Find an active unlock history for this component by the current user
            const unlockHistory = await ComponentUnlockHistory.findOne({
                component_id: componentData._id,
                unlock_by: req.session._id,
                is_active: true
            });

            // If no unlock history is found, return a 400 error indicating restricted access
            if (!unlockHistory) {
                return res.status(constants.bad_request_code).json({
                    status: constants.bad_request_code,
                    message: 'Oops! You do not have access to this code-space.'
                });
            }
        }

        // If all checks pass, move on to the next middleware or route handler
        return next();

    } catch (error) {
        // Catch and log any errors that occur
        logger.error(`Error in middleware function checkIsAccessible: ${error}`);
        return res.status(500).json({
            status: 500,
            message: 'Oops! Internal server error.'
        });
    }
}



module.exports = {
    checkIsAccessible
};