const Joi = require('joi');

class CodeSpaceValidation {
    createRepository(params) {

        const forbiddenNames = [
            'badges', 'blame', 'blob', 'builds', 'commits', 'create', 'create_dir', 'edit',
            'environments/folders', 'files', 'find_file', 'gitlab-lfs/objects', 'info/lfs/objects',
            'new', 'preview', 'raw', 'refs', 'tree', 'update', 'wikis'
        ];

        const regex = /^(?!.*[-_.]{2})(?!.*\.(git|atom)$)(?![-_.])[a-zA-Z0-9][a-zA-Z0-9._-]*[a-zA-Z0-9]$/;

        const schema = Joi.object({
            project_name: Joi.string().invalid(...forbiddenNames).pattern(regex).required(),
            platform_id: Joi.array().items(Joi.string().trim()).required(),
            description: Joi.string().optional().allow(null, ''),
            initialize_with_readme: Joi.boolean().required(),
            component_id: Joi.string().optional()
        });
        return schema.validate(params);
    }
}

module.exports = new CodeSpaceValidation();