const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const sectionSchema = new Schema({
    title: {
        type: String,
        require: true
    },
    slug: {
        type: String,
        required: true
    },
    description: {
        type: String
    },
    image_url: {
        type: String,
        require: true
    },
    editor_type: {
        type: String,
        required: true
    },
    is_active: {
        type: Boolean,
        default: true
    },
    created_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    updated_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    order: {
        type: Number,
        default: 0
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const Sections = mongoose.model('sections', sectionSchema);

module.exports = {
    Sections
};