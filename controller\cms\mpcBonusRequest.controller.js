const constants = require('../../config/constants');

const { ReS, sendError } = require('../../services/general.helper');

const { BonusPointRequest } = require('../../models/mpc_bonus_point_request.model');

const { acceptBonusRequests } = require('../../services/mpc_balance.service');

const { mpcBonusRequestStatus } = require('../../config/component.constant');
const logger = require('../../config/logger');


async function approveOrRejectRequest(req, res) {
    try {
        // Extract request parameters
        const id = req.params.id;
        const { mpc_bonus, status } = req.body;

        // Find the bonus request by ID
        const bonusRequest = await BonusPointRequest.findById(id).lean();

        // If bonus request is not found, return with error message
        if (!bonusRequest) {
            return ReS(res, constants.resource_not_found, 'Oops! Request Not Found.');
        }

        // If request status is not pending, return with error message
        if (bonusRequest.status !== mpcBonusRequestStatus.PENDING) {
            return ReS(res, constants.bad_request_code, 'Oops! Request status already updated');
        }

        // Update the bonus request status based on approval or rejection
        if (status === mpcBonusRequestStatus.REJECTED) {
            await BonusPointRequest.updateOne({ _id: id }, { '$set': { status: status } });
        } else if (status === mpcBonusRequestStatus.ACCEPTED) {
            // If request is accepted, update status and assign bonus points
            await BonusPointRequest.updateOne({ _id: id }, { '$set': { status: status, mpc_bonus: mpc_bonus } });
            // Perform additional actions for accepting bonus requests
            await acceptBonusRequests(bonusRequest.user_id, mpc_bonus);
        }

        // Return success response
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        // Handle errors
        logger.error(`Error at CMS Controller approveOrRejectRequest${err}`);
        return sendError(res, err);
    }
}

async function listPendingRequests(req, res) {
    try {
        // Count total documents in BonusPointRequest collection
        const totalDocuments = await BonusPointRequest.countDocuments();

        // Define sort criteria
        const sort = {
            'created_at': -1
        };

        // Define conditions to filter pending requests
        const conditions = {
            status: mpcBonusRequestStatus.PENDING
        };

        // Set limit and skip values based on request parameters or default to 10 and 0 respectively
        const limit = (req.body.limit !== undefined) ? req.body.limit : 10;
        const skip = (req.body.skip !== undefined) ? req.body.skip : 0;

        // Count documents matching the conditions
        const filterDocuments = await BonusPointRequest.countDocuments(conditions);

        // Define aggregation pipeline to fetch pending requests along with user details
        const query = [
            { $match: conditions },
            {
                $lookup: {
                    from: 'users',
                    let: { user_id: '$user_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ['$_id', '$$user_id'] }]
                                }
                            }
                        },
                        {
                            $project: {
                                email: 1,
                                first_name: 1,
                                last_name: 1
                            }
                        }
                    ],
                    as: 'user_id'
                }
            },
            {
                $project: {
                    status: 1,
                    mpc_bonus: 1,
                    user_id: { $arrayElemAt: ['$user_id', 0] },
                    request_note: 1,
                    created_at: 1
                }
            }
        ];

        // Add sorting, skipping, and limiting stages to the aggregation pipeline
        query.push({ '$sort': sort });
        query.push({ '$skip': skip });
        query.push({ '$limit': limit });

        // Execute the aggregation pipeline
        const mpcBonusRequestList = await BonusPointRequest.aggregate(query);

        // Prepare response object
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: mpcBonusRequestList
        };

        // Respond with success and the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        // Log any errors that occur
        logger.error(`Error at CMS Controller listPendingRequests${err}`);

        // Respond with server error message
        return sendError(res, err);
    }
}

async function getBonusRequestDetails(req, res) {
    // Extract the request ID from request parameters
    const requestId = req.params.id;

    try {
        // Find the bonus point request by ID and populate user details
        const requestData = await BonusPointRequest.findOne({
            _id: requestId
        }).populate('user_id', 'email first_name last_name');

        // If no request data found, return a resource not found error response
        if (requestData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Request Not Found.');
        }

        // Return success response with request data
        return ReS(res, constants.success_code, 'Request fetched Successfully', requestData);
    } catch (err) {
        // Log error and return a server error response
        logger.error(`Error at CMS Controller getPointRequest${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    listPendingRequests,
    approveOrRejectRequest,
    getBonusRequestDetails
};