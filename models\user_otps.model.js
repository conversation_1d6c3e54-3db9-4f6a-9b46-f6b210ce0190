const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { userActions } = require('../config/component.constant');

const UserOTPsSchema = new Schema({
    user_id: {
        type: Schema.Types.ObjectId,
        ref: 'users'
    },
    otp: {
        type: String,
        required: true
    },
    action: {
        type: String,
        enum: Object.values(userActions)
    },
    email: {
        type: String
    },
    is_expired: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const UserOTPs = mongoose.model('user_otps', UserOTPsSchema);

module.exports = {
    UserOTPs
};