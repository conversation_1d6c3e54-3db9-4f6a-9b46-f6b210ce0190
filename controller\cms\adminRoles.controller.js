// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, generateSlug, sendError, escapeRegex } = require('../../services/general.helper');

// Models declaration
const AdminRoles = require('../../models/admin_roles.model').AdminRoles;


async function createAdminRole(req, res) {
    try {
        const {
            role,
            permission_groups,
            is_active
        } = req.body;

        const roleSlug = generateSlug(role);

        await AdminRoles.create({
            role: role,
            permission_groups: permission_groups,
            slug: roleSlug,
            is_active: is_active,
            created_by: req.session._id
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createAdminRole${err}`);
        return sendError(res, err);
    }
}

async function getAllAdminRoles(req, res) {
    try {
        const totalDocuments = await AdminRoles.countDocuments();
        const filter = {};
        if (req.body.is_active != undefined) {
            filter.is_active = req.body.is_active;
        }
        if (req.body.role) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.role);
            filter['$or'] = [{
                'role': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        // Default sort
        const sort = {
            'created_at': -1
        };
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        // Count filtered documents after filter apply
        const filterDocuments = await AdminRoles.countDocuments(filter);
        const userRoleList = await AdminRoles.find(filter, {
            'role': 1,
            'slug': 1,
            'permission_groups': 1,
            'is_active': 1,
            'created_at': 1
        }).sort(sort).skip(skip).limit(limit).lean();

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: userRoleList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllAdminRoles${err}`);
        return sendError(res, err);
    }
}

async function updateAdminRoleStatus(req, res) {
    try {
        const { is_active } = req.body;

        const userRole = await AdminRoles.findOne({
            _id: req.params.id
        }).lean();

        if (userRole == null) {
            return ReS(res, constants.resource_not_found, 'Oops! User Role Not Found.');
        }

        await AdminRoles.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active,
                updated_by: req.session._id
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateAdminRoleStatus${err}`);
        return sendError(res, err);
    }
}

async function updateAdminRole(req, res) {
    try {
        const postData = req.body;

        const userRole = await AdminRoles.findOne({
            _id: req.params.id
        }).lean();

        if (userRole == null) {
            return ReS(res, constants.resource_not_found, 'Oops! User Role Not Found.');
        }

        if (postData && postData.role) {
            postData['slug'] = generateSlug(postData.role);
        }

        // Set the updated_by field to the current user's session ID
        postData['updated_by'] = req.session._id;

        await AdminRoles.updateOne({
            _id: req.params.id
        }, {
            '$set': postData
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateAdminRole${err}`);
        return sendError(res, err);
    }
}

async function getAllAdminRolesSortList(req, res) {
    try {
        const filter = {
            is_active: true
        };
        if (req.body.is_active != undefined) {
            filter.is_active = req.body.is_active;
        }
        if (req.body.role) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.role);
            filter['$or'] = [{
                'role': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        // Default sort
        const sort = {
            'created_at': 1
        };
        const userRoleList = await AdminRoles.find(filter, {
            'role': 1,
            'slug': 1,
            'permission_groups': 1,
            'is_active': 1,
            'created_at': 1
        }).sort(sort).lean();

        return ReS(res, constants.success_code, 'Data Fetched', userRoleList);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllAdminRolesSortList${err}`);
        return sendError(res, err);
    }
}

async function getAdminRoleDetails(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };

        const userRole = await AdminRoles.findOne(filter, {
            'role': 1,
            'slug': 1,
            'permission_groups': 1,
            'is_active': 1,
            'created_at': 1
        }).populate({
            path: 'permission_groups',
            select: 'group_name module_name'
        }).lean();

        if (userRole == null) {
            return ReS(res, constants.resource_not_found, 'Oops! User Role Not Found.');
        }
        return ReS(res, constants.success_code, 'Data Fetched', userRole);
    } catch (err) {
        logger.error(`Error at CMS Controller getAdminRoleDetails${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createAdminRole,
    getAllAdminRoles,
    updateAdminRole,
    updateAdminRoleStatus,
    getAllAdminRolesSortList,
    getAdminRoleDetails
};