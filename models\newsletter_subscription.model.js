const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const NewsletterSubscriptionSchema = new Schema({
    email: {
        type: String,
        required: true,
        lowercase: true,
        trim: true,
        unique: true,
        index: true
    },
    is_email_subscribed: {
        type: Boolean,
        default: false
    },
    is_email_unsubscribed: {
        type: Boolean,
        default: false
    },
    subscribe_token: {
        type: String,
        default: null
    },
    unsubscribe_token: {
        type: String,
        default: null
    },
    topic: {
        type: String,
        default: 'global'
    },
    flag: {
        type: Number,
        default: 0
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

NewsletterSubscriptionSchema.set('autoIndex', true);

const NewsletterSubscription = mongoose.model('newsletter_subscription', NewsletterSubscriptionSchema);

module.exports = {
    NewsletterSubscription
};