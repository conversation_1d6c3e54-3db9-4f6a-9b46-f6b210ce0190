const express = require('express');
const router = express.Router();

const { createUserRoleValidation, updateUserRoleStatusValidation, updateUserRoleValidation } = require('../../middlewares/validations/cms/user_roles/userRolesValidation');

const { createAdminRole, getAdminRoleDetails, getAllAdminRoles, getAllAdminRolesSortList, updateAdminRole, updateAdminRoleStatus } = require('../../controller/cms/adminRoles.controller');

router.post('/create', createUserRoleValidation, createAdminRole);
router.put('/update/:id', updateUserRoleValidation, updateAdminRole);
router.post('/list', getAllAdminRoles);
router.put('/update/status/:id', updateUserRoleStatusValidation, updateAdminRoleStatus);
router.get('/sort-list', getAllAdminRolesSortList);
router.get('/details/:id', getAdminRoleDetails);

module.exports = router;