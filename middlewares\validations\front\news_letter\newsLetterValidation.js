const { subscribeNewsLetter, verifyNewsLetterSubscription, unSubscribeNewsLetter } = require('../../../../validations/front/news_letter/newsLetterValidation');
class NewsLetterValidationMiddleware {
    subscribeNewsLetterValidation(req, res, next) {
        const { value, error } = subscribeNewsLetter(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    verifyNewsLetterSubscriptionValidation(req, res, next) {
        const { value, error } = verifyNewsLetterSubscription(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    unSubscribeNewsLetterValidation(req, res, next) {
        const { value, error } = unSubscribeNewsLetter(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new NewsLetterValidationMiddleware();