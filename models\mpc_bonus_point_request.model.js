const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { mpcBonusRequestStatus } = require('../config/component.constant');

const mpcBonusPointRequestSchema = new Schema({
    user_id: {
        type: Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    status: {
        type: String,
        enum: Object.values(mpcBonusRequestStatus),
        default: mpcBonusRequestStatus.PENDING
    },
    mpc_bonus: {
        type: Number,
        default: 0
    },
    request_note: {
        type: String
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const BonusPointRequest = mongoose.model('mpc_bonus_request', mpcBonusPointRequestSchema);

module.exports = {
    BonusPointRequest
};