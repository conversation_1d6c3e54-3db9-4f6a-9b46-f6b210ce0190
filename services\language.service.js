const { GitlabRepository } = require('../models/gitlab_repository.model');
const { SupportedPlatforms } = require('../models/supported_platforms.model');
const { Components } = require('../models/component.model');
const logger = require('../config/logger');
const { fetchProjectLanguagesMultiple, fetchProjectLanguages } = require('./gitlab.helper');
const { generateSlug } = require('./general.helper');


const scanCodeSpaceLanguages = async () => {
    try {
        // Fetch active, non-deleted repositories, sorted by last language scan and creation date
        const codeSpace = await GitlabRepository.find({
            is_active: true, is_deleted: false
        }, 'project_id').sort({ last_language_scan: 1, created_at: 1 }).limit(10).lean();

        if (codeSpace.length === 0) return;

        // Extract project IDs from the fetched repositories
        const projectIds = codeSpace.map(({ project_id }) => project_id);

        // Fetch programming languages for the retrieved project IDs
        const gitlabLanguages = await fetchProjectLanguagesMultiple(projectIds);

        // Collect unique languages across all projects
        const uniqueLanguages = new Set();
        Object.values(gitlabLanguages).forEach((languages) => {
            Object.keys(languages).forEach((language) => uniqueLanguages.add(language));
        });

        // Update GitlabRepository with fetched languages
        await Promise.all(
            Object.entries(gitlabLanguages).map(async ([key, languages]) => {
                const match = key.match(/projects\/(\d+)/);
                if (match) {
                    const projectId = match[1];
                    await GitlabRepository.updateOne(
                        { project_id: projectId },
                        {
                            $set: {
                                gitlab_languages: languages,
                                last_language_scan: new Date()
                            }
                        }
                    );
                }
            })
        );

        // Check if each language exists in SupportedPlatforms, if not, create it
        await Promise.all(
            [...uniqueLanguages].map(async (technology) => {
                const technologySlug = generateSlug(technology);
                const existingTechnology = await SupportedPlatforms.findOne({ slug: technologySlug }).lean();

                if (!existingTechnology) {
                    console.log(`Creating new technology: ${technology} with slug: ${technologySlug}`);
                    await SupportedPlatforms.create({
                        title: technology,
                        slug: technologySlug,
                        is_active: true,
                        is_default: false,
                        system_generated: true
                    });
                } else {
                    console.log(`Technology already exists: ${existingTechnology.title} (slug: ${existingTechnology.slug})`);
                }
            })
        );
    } catch (error) {
        logger.error(`Error in scanCodeSpaceLanguages: ${error.message}`, error);
    }
};

async function fetchAndUpdateRepositoryLanguages(projectId, source) {
    try {
        console.log(`Fetch and update repository languages for project => ${projectId}`);
        const codeSpace = await GitlabRepository.findOne({
            project_id: projectId,
            is_active: true,
            is_deleted: false
        }, 'project_id component_id').lean();

        if (!codeSpace) return;

        // Fetch programming languages for the retrieved project IDs
        const gitlabLanguages = await fetchProjectLanguages(projectId);

        const onlyLanguages = Object.keys(gitlabLanguages);

        // Check if each language exists in SupportedPlatforms, if not, create it
        const technologyIds = await Promise.all(
            [...onlyLanguages].map(async (technology) => {
                const technologySlug = generateSlug(technology);
                let existingTechnology = await SupportedPlatforms.findOne({ slug: technologySlug }).lean();

                if (!existingTechnology) {
                    console.log(`Creating new technology: ${technology} with slug: ${technologySlug}`);
                    existingTechnology = await SupportedPlatforms.create({
                        title: technology,
                        slug: technologySlug,
                        is_active: true,
                        is_default: false,
                        system_generated: true
                    });
                } else {
                    console.log(`Technology already exists: ${existingTechnology.title} (slug: ${existingTechnology.slug})`);
                }
                return existingTechnology._id; // Collecting _id
            })
        );

        const uniqueTechnologyIds = [...new Set(technologyIds)]; // Removes duplicates

        const postData = {
            gitlab_languages: gitlabLanguages,
            detected_languages: onlyLanguages,
            detected_platforms: uniqueTechnologyIds,
            last_language_scan: new Date()
        };

        if (source == 'webhook') {
            postData['last_pushed_at'] = new Date();
        }
        // Update GitlabRepository with fetched languages
        await GitlabRepository.updateOne({
            project_id: projectId
        }, {
            $set: postData
        });

        // Check if any component associated with this code-space 
        if (codeSpace?.component_id) {
            await Components.updateOne({
                _id: codeSpace.component_id
            }, {
                $addToSet: {
                    detected_technologies: {
                        $each: uniqueTechnologyIds
                    }
                }
            });
        }
    } catch (err) {
        logger.error('Error in fetchAndUpdateRepositoryLanguages:', err);
        throw err; // Throw the error for handling in higher layers
    }
}

const scanCodeSpaceLanguageSequentially = async () => {
    try {
        // Fetch active, non-deleted repositories, sorted by last language scan and creation date
        const codeSpaceList = await GitlabRepository.find({
            is_active: true, is_deleted: false
        }, 'project_id').sort({ last_language_scan: 1, created_at: 1 }).limit(10).lean();

        if (codeSpaceList.length === 0) return;

        for (const codeSpace of codeSpaceList) {
            console.log('Checking gitlab languages for project_id =>', codeSpace?.project_id);
            if (codeSpace?.project_id) {
                await fetchAndUpdateRepositoryLanguages(codeSpace?.project_id, 'cron');
            }
        }
    } catch (error) {
        logger.error(`Error in scanCodeSpaceLanguageSequentially: ${error.message}`, error);
    }
};

module.exports = {
    scanCodeSpaceLanguages,
    fetchAndUpdateRepositoryLanguages,
    scanCodeSpaceLanguageSequentially
};