const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const componentVersionSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    options: {
        type: Object
    },
    platform_data: {
        type: Object
    },
    updated_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    version: {
        type: Number,
        default: 1
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const ComponentVersions = mongoose.model('component_versions', componentVersionSchema);

module.exports = {
    ComponentVersions
};