const express = require('express');
const router = express.Router();

const { createComponentValidation, updateComponentValidation, updateComponentStatusValidation, createComponentRepositoryValidation, linkCodeSpacesToComponentValidation } = require('../../middlewares/validations/cms/component/componentValidation');

const { createComponent, updateComponent, getAllComponents, getComponentsDetails, getChildComponentsDetails, deleteComponent, getComponentVersion, uploadComponentAssets, restoreComponent, updateComponentStatus, getAllComponentList, getAllComponentChangeLogs, createRepositoryComponent, updateRepositoryComponent, detachRepositoryFromComponent, linkCodeSpacesToComponent, createComponentPlaceHolder, publishDraftComponent, markComponentAsFeatured } = require('../../controller/cms/component.controller');


router.post('/create', createComponentValidation, createComponent);
router.put('/update/:id', updateComponentValidation, updateComponent);
router.post('/list', getAllComponents);
router.get('/:id', getComponentsDetails);
router.get('/children/:parent_id', getChildComponentsDetails);
router.delete('/delete/:id', deleteComponent);
router.get('/:id/version/:version', getComponentVersion);
router.post('/upload/assets', uploadComponentAssets);
router.put('/restore/:id', restoreComponent);
router.put('/update/status/:id', updateComponentStatusValidation, updateComponentStatus);
router.post('/get-all-components/list', getAllComponentList);
router.post('/change/logs', getAllComponentChangeLogs);

router.post('/create/repository', createComponentRepositoryValidation, createRepositoryComponent);
router.put('/update/repository/:id', updateRepositoryComponent);
router.put('/:id/detach/:repository_id', detachRepositoryFromComponent);
router.put('/:id/link/code-spaces', linkCodeSpacesToComponentValidation, linkCodeSpacesToComponent);

router.post('/create/placeholder', createComponentPlaceHolder);
router.post('/:id/publish', publishDraftComponent);

router.put('/:id/featured', markComponentAsFeatured);

module.exports = router;