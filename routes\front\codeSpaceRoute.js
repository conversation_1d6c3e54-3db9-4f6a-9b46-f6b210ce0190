const express = require('express');
const router = express.Router();

const { createNewRepository, getAllRepository, getAllRepositorySortList, checkRepositoryNameAvailability, getRepositoryDetails, getRepositoryCommitList, searchRepositoryFiles, getRepositoryLanguages, createRepositoryFork, publishPublicRepository, changeCodeSpaceVisibility, updateCodeSpaceDetails, createForkSyncMergeRequest, fetchCodeSpaceTagSuggestions, fetchRepositoryForksList } = require('../../controller/front/codeSpace.controller');

const { createRepositoryValidation, fetchAllCodeSpaceValidation, createRepositoryForkValidation, updateCodeSpaceValidation } = require('./../../middlewares/validations/front/code_space/codeSpaceValidation');

router.post('/create', createRepositoryValidation, createNewRepository);
router.post('/list', fetchAllCodeSpaceValidation, getAllRepository);
router.get('/sort-list', getAllRepositorySortList);
router.get('/check-name-availability', checkRepositoryNameAvailability);
router.get('/details/:id', getRepositoryDetails);
router.get('/get-commits/:id', getRepositoryCommitList);
router.get('/search-files/:id', searchRepositoryFiles);
router.get('/get-languages/:id', getRepositoryLanguages);
router.post('/:id/fork', createRepositoryForkValidation, createRepositoryFork);
router.post('/:id/public/publish', publishPublicRepository);
router.put('/:id/change/visibility', changeCodeSpaceVisibility);
router.put('/:id/update', updateCodeSpaceValidation, updateCodeSpaceDetails);
router.put('/:id/sync-fork', createForkSyncMergeRequest);
router.get('/:id/tag-suggestions', fetchCodeSpaceTagSuggestions);
router.post('/:id/fork/list', fetchRepositoryForksList);

module.exports = router;