const projectVisibility = {
    PUBLIC: 'public',
    INTERNAL: 'internal',
    PRIVATE: 'private'
};

const gitlabUserScope = ['api', 'read_api', 'read_repository', 'write_repository'];

const gitlabAdminScope = [
    'api',
    'read_api',
    'read_user',
    'create_runner',
    'manage_runner',
    'k8s_proxy',
    'read_repository',
    'write_repository',
    'ai_features'
];

const gitlabDefaultBranch = 'development';

const gitlabProductionBranch = 'main';

const gitlabUserBaseDomain = '@creator.mpn';

const gitlabAccessLevel = {
    MINIMAL_ACCESS: 5,
    GUEST: 10,
    REPORTER: 20,
    DEVELOPER: 30,
    MAINTAINER: 40,
    OWNER: 50
};

const gitlabCommitActions = {
    CREATE: 'create',
    DELETE: 'delete',
    MOVE: 'move',
    UPDATE: 'update',
    CHMOD: 'chmod'
};

const codeSpaceFilterTypes = {
    ALL: 'all',
    LINKED: 'linked',
    DETACHED: 'detached',
    PUBLIC: 'public'
};

const gitlabImportStatus = {
    FINISHED: 'finished',
    SCHEDULED: 'scheduled'
};

const repositoryState = {
    PUBLIC: 'public',
    PRIVATE: 'private'
};

const publishState = {
    PUBLISHED: 'published',
    PRIVATE: 'private'
};

const webhookEvents = {
    PUSH: 'push',
    REPOSITORY_UPDATE: 'repository_update',
    MERGE_REQUEST: 'merge_request'
};

const overrideFileExtension = {
    XML: 'xml',
    MDX: 'mdx'
};

const invitationStatus = {
    PENDING: 'pending',
    ACCEPTED: 'accepted',
    REJECTED: 'rejected',
    EXPIRED: 'expired'
};

const mergeRequestStates = {
    OPENED: 'opened',
    CLOSED: 'closed',
    LOCKED: 'locked',
    MERGED: 'merged'
};

const mergeRequestActivities = {
    COMMENTS: 'comments',
    EVENTS: 'events',
    COMMITS: 'commits'
};

module.exports = {
    projectVisibility,
    gitlabUserScope,
    gitlabDefaultBranch,
    gitlabProductionBranch,
    gitlabAccessLevel,
    gitlabUserBaseDomain,
    gitlabCommitActions,
    gitlabAdminScope,
    codeSpaceFilterTypes,
    gitlabImportStatus,
    repositoryState,
    webhookEvents,
    publishState,
    overrideFileExtension,
    invitationStatus,
    mergeRequestStates,
    mergeRequestActivities
};