const constants = require('../../config/constants');
const logger = require('../../config/logger');

const { ReS, sendError, generateSlug, uploadBufferToS3 } = require('../../services/general.helper');

const Components = require('../../models/component.model').Components;
const ComponentStatistics = require('../../models/component_statistics.model').ComponentStatistics;
const SupportedPlatforms = require('../../models/supported_platforms.model').SupportedPlatforms;
const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;
const Users = require('../../models/users.model').Users;
const Collaborator = require('../../models/collaborator.model').Collaborator;
const GitlabUsers = require('../../models/gitlab_users.model').GitlabUsers;
const RepositoryStructure = require('../../models/repository_structure.model').RepositoryStructure;

const { componentState, componentType } = require('../../config/component.constant');
const { gitlabAccessLevel, repositoryState, publishState, gitlabProductionBranch } = require('../../config/gitlab.constant');

const { html2gifWithInteractions } = require('../../services/elements.service');
const redis = require('../../config/redis');

const { fetchGitlabProjectStatistics } = require('../../services/gitlab.helper');

async function updateComponentsV2(req, res) {
    try {
        const componentList = await Components.find({ 'platform_data': { '$exists': false } }).lean();
        const platformData = await SupportedPlatforms.findOne({ is_default: true }).lean();
        for (const component of componentList) {
            const postData = {
                platform_data: [{
                    platform_id: platformData._id,
                    options: component.options
                }]
            };
            await Components.updateOne({
                _id: component._id
            }, {
                $set: postData,
                $unset: {
                    options: 1
                }
            });
            logger.info(`${component.slug} updated successfully`);
        }
        return ReS(res, constants.success_code, 'Migration Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateComponentsV2${err}`);
        return sendError(res, err);
    }
}

async function updateComponentsModifiers(req, res) {
    try {
        const componentList = await Components.find({ modifier: { $ne: null } }).lean();
        for (const component of componentList) {
            await Components.updateOne({
                _id: component._id
            }, {
                $set: {
                    'platform_data.0.modifier': component.modifier,
                    'platform_data.0.is_overridden_modifier': component.is_overridden_modifier
                },
                $unset: {
                    modifier: 1,
                    is_overridden_modifier: 1
                }
            });
            logger.info(`${component.slug} updated successfully`);
        }
        return ReS(res, constants.success_code, 'Migration Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateComponentsModifiers${err}`);
        return sendError(res, err);
    }
}

async function updateComponentsModifiersToArray(req, res) {
    try {
        const updatedComponent = [];
        const componentList = await Components.find({}).lean();
        for (const component of componentList) {

            if (component && component.platform_data && component.platform_data.length) {
                for (const platform of component.platform_data) {

                    if (platform && platform.modifier && !Array.isArray(platform.modifier)) {
                        const queryResult = await Components.updateOne(
                            {
                                'platform_data._id': platform._id
                            },
                            {
                                '$set': {
                                    'platform_data.$.modifier': [platform.modifier]
                                }
                            }
                        );
                        console.log('Modifier update queryResult', queryResult);
                        updatedComponent.push({ slug: component.slug, platform: platform._id });
                        logger.info(`${component.slug} updated successfully for platform _id ${platform._id}`);
                    }
                }
            }
        }
        return ReS(res, constants.success_code, 'Migration Success', updatedComponent);
    } catch (err) {
        logger.error(`Error at CMS Controller updateComponentsModifiersToArray${err}`);
        return sendError(res, err);
    }
}

async function updateComponentsState(req, res) {
    try {
        // Perform the migration by updating the components based on the given conditions
        const result = await Components.updateMany(
            {}, // target all documents
            [
                {
                    $set: {
                        component_state: {
                            $cond: [
                                { $eq: ['$is_deleted', true] },
                                componentState.SOFT_DELETED,
                                {
                                    $cond: [
                                        { $eq: ['$is_active', true] },
                                        componentState.PUBLISHED,
                                        componentState.ACTIVE_DRAFT
                                    ]
                                }
                            ]
                        }
                    }
                }
            ]
        );

        logger.info(`Migration completed. Updated ${result.modifiedCount} components.`);
        return ReS(res, constants.success_code, `Migration completed. Updated ${result.modifiedCount} components.`);

    } catch (err) {
        logger.error(`Error at CMS Controller updateComponentsState${err}`);
        return sendError(res, err);
    }
}

async function updateCodeSpaceTechnologiesToArray(req, res) {
    try {
        const updatedCodeSpace = [];
        const codeSpaceList = await GitlabRepository.find({}).lean();
        for (const repository of codeSpaceList) {
            if (repository && repository.platform_id && !Array.isArray(repository.platform_id)) {
                const queryResult = await GitlabRepository.updateOne(
                    {
                        '_id': repository._id
                    },
                    {
                        '$set': {
                            'platform_id': [repository.platform_id]
                        }
                    }
                );
                console.log('platform_id update queryResult', queryResult);
                updatedCodeSpace.push({ _id: repository._id });
                logger.info(`${repository.project_name} updated successfully for platform _id ${repository.platform_id}`);
            }
        }
        return ReS(res, constants.success_code, 'Migration Success', updatedCodeSpace);
    } catch (err) {
        logger.error(`Error at CMS Controller updateComponentsModifiersToArray${err}`);
        return sendError(res, err);
    }
}

async function updateComponentCollectionWithStatistics(req, res) {
    try {
        const updatedComponents = [];
        const componentList = await Components.find({}, '_id').lean();
        for (const component of componentList) {
            const statistics = await ComponentStatistics.findOne({
                component_id: component._id
            }).lean();
            await Components.updateOne({
                _id: component._id
            }, {
                $set: {
                    likes: (statistics?.likes?.total_likes) ? statistics?.likes?.total_likes : 0,
                    views: (statistics?.views?.total_views) ? statistics?.views?.total_views : 0,
                    bookmarks: (statistics?.bookmarks?.total_bookmarks) ? statistics?.bookmarks?.total_bookmarks : 0
                }
            });
            updatedComponents.push(component._id);
        }
        return ReS(res, constants.success_code, 'Migration Success', updatedComponents);
    } catch (err) {
        logger.error(`Error at CMS Controller updateComponentCollectionWithStatistics ${err}`);
        return sendError(res, err);
    }
}

async function updateComponentCollectionWithTechnologies(req, res) {
    try {
        const updatedComponents = [];

        const componentList = await Components.find({
            component_state: componentState.PUBLISHED,
            component_type: componentType.REPOSITORY
        }, '_id platform_data').lean();

        for (const component of componentList) {

            if (component && component.platform_data && component.platform_data.length) {

                const technologies = [];

                for (const singlePlatform of component.platform_data) {
                    if (singlePlatform && singlePlatform.platform_id && Array.isArray(singlePlatform.platform_id)) {
                        for (const platform of singlePlatform.platform_id) {
                            technologies.push(platform.toString());
                        }
                    }
                }
                const uniqueTechnologies = [...new Set(technologies)];
                await Components.updateOne({
                    _id: component._id
                }, {
                    $set: {
                        technologies: uniqueTechnologies
                    }
                });
                updatedComponents.push(component._id);
            }
        }
        return ReS(res, constants.success_code, 'Migration Success', updatedComponents);
    } catch (err) {
        logger.error(`Error at CMS Controller updateComponentCollectionWithTechnologies ${err}`);
        return sendError(res, err);
    }
}

async function updateUserCollectionWithComponentStatistics(req, res) {
    try {
        const updatedUsers = [];

        const userList = await Users.find({
            is_deleted: false,
            is_verified: true,
            email: {
                $exists: true
            }
        }, '_id platform_data').lean();

        console.log('userList', userList);

        for (const user of userList) {

            const components = await Components.find({
                created_by_user: user._id
            }, 'likes views bookmarks').lean();

            const totals = components.reduce(
                (acc, item) => {
                    acc.likes += item.likes;
                    acc.views += item.views;
                    acc.bookmarks += item.bookmarks;
                    return acc;
                },
                { likes: 0, views: 0, bookmarks: 0 }
            );

            await Users.updateOne({
                _id: user._id
            }, {
                $set: {
                    total_views: totals.views,
                    total_likes: totals.likes,
                    total_bookmarks: totals.bookmarks
                }
            });
            updatedUsers.push(user._id);
        }
        return ReS(res, constants.success_code, 'Migration Success', updatedUsers);
    } catch (err) {
        logger.error(`Error at CMS Controller updateUserCollectionWithComponentStatistics ${err}`);
        return sendError(res, err);
    }
}

async function addCollaboratorCollectionOwnerRole(req, res) {
    try {
        const updatedRepos = [];

        const repos = await GitlabRepository.find({}).lean();

        for (const repo of repos) {

            const repoDetails = await Collaborator.findOne({
                repo_id: repo._id
            }, 'likes views bookmarks').lean();

            if (!repoDetails) {
                const gitlabUserData = await GitlabUsers.findOne({ _id: repo.gitlab_user_id }).lean();
                if (gitlabUserData?.user_id) {
                    await Collaborator.create({
                        user_id: gitlabUserData.user_id,
                        gitlab_user_id: gitlabUserData.gitlab_user_id,
                        gitlab_user_ref: gitlabUserData._id,
                        repo_id: repo._id,
                        access_level: gitlabAccessLevel.OWNER
                    });
                }
                updatedRepos.push(repo._id);
            }
        }
        return ReS(res, constants.success_code, 'Migration Success', updatedRepos);
    } catch (err) {
        logger.error(`Error at CMS Controller addCollaboratorCollectionOwnerRole ${err}`);
        return sendError(res, err);
    }
}

async function updateGitlabRepoDomain(req, res) {
    try {
        const updatedRepos = [];

        const repos = await GitlabRepository.find({}).lean();

        for (const repo of repos) {
            const oldUrl = repo.http_url_to_repo;
            if (typeof oldUrl === 'string') {
                // Replace the domain in the URL
                const newUrl = oldUrl.replace(
                    /gitlab\.multiplatform\.network/g,
                    'creator.multiplatform.network'
                );

                if (newUrl !== oldUrl) {
                    await GitlabRepository.updateOne(
                        { _id: repo._id },
                        { $set: { http_url_to_repo: newUrl } }
                    );
                    updatedRepos.push({
                        _id: repo._id,
                        http_url_to_repo: newUrl
                    });
                }
            }
        }
        return ReS(res, constants.success_code, 'Migration Success', updatedRepos);
    } catch (err) {
        logger.error(`Error at CMS Controller updateGitlabRepoDomain ${err}`);
        return sendError(res, err);
    }
}

async function createProjectForPublicCodeSpace(req, res) {
    try {
        const repositoryList = await GitlabRepository.find({
            state: repositoryState.PUBLIC,
            published_state: publishState.PUBLISHED
        }).lean();

        console.log(`Published public code-space counts => ${repositoryList.length}`);

        const updatedRepos = [];

        for (const repository of repositoryList) {
            const componentObj = {
                title: repository?.project_name,
                slug: generateSlug(repository?.project_name),
                short_description: repository?.description,
                created_by_user: repository?.user_id,
                updated_by_user: repository?.user_id,
                component_type: componentType.CODESPACE,
                component_state: componentState.PUBLISHED,
                last_published_at: new Date(),
                detected_technologies: repository?.detected_platforms,
                public_repository_id: repository?._id
            };
            const component = await Components.create(componentObj);

            await GitlabRepository.updateOne({
                _id: repository?._id
            }, {
                $set: {
                    component_id: component?._id
                }
            });
            console.log(`Component entry created for the public code-space => ${repository?._id}`);
            updatedRepos.push(repository?._id);
        }
        return ReS(res, constants.success_code, 'Migration Success', updatedRepos);
    } catch (err) {
        logger.error(`Error at CMS Controller createProjectForPublicCodeSpace ${err}`);
        return sendError(res, err);
    }
}

async function regenerateElementsThumbnails(req, res) {
    try {
        // Send response right away
        ReS(res, constants.success_code, 'Thumbnail regeneration started in background');

        // Run the actual logic asynchronously in the background
        global.setImmediate(async () => {
            try {
                const elementList = await Components.find({
                    component_type: componentType.ELEMENTS,
                    component_state: componentState.PUBLISHED
                }).lean();

                for (const element of elementList) {
                    let htmlContent = '';

                    const htmlCode = element?.elements_data?.html || '';
                    const cssCode = element?.elements_data?.css || '';
                    const jsCode = element?.elements_data?.js || '';

                    const centeredStyles = `
                        body {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            min-height: 100vh;
                        }
                        body::-webkit-scrollbar {
                            width: 3px;
                        }
                        body::-webkit-scrollbar-track {
                            background: rgba(33, 33, 33, 0.8);
                        }
                        body::-webkit-scrollbar-thumb {
                            background: rgba(21, 21, 25, 0.7);
                        }`;

                    htmlContent = `<style>${centeredStyles}${cssCode}</style>${htmlCode}<script type="module">${jsCode}</script>`;

                    const gifBuffer = await html2gifWithInteractions(htmlContent);

                    const timestamp = Date.now();
                    const filename = `${timestamp}.gif`;
                    const documentPath = `assets/${filename}`;
                    const mimeType = 'image/gif';

                    await uploadBufferToS3(gifBuffer, documentPath, mimeType);

                    await Components.updateOne({
                        _id: element._id
                    }, {
                        $set: {
                            linked_output: htmlContent,
                            image_url: documentPath
                        }
                    });

                    console.log(`Element thumbnail generated successfully for element => ${element._id}`);
                }

                console.log('Background thumbnail generation completed.');
            } catch (bgErr) {
                logger.error(`Error during background thumbnail generation: ${bgErr}`);
            }
        });

    } catch (err) {
        logger.error(`Error at CMS Controller regenerateElementsThumbnails ${err}`);
        return sendError(res, err);
    }
}

async function migrateRepositoryStructureFromRedisToMongoDB(req, res) {
    try {
        const repositoryList = await GitlabRepository.find().lean();

        for (const repository of repositoryList) {
            const redisKeyFiles = `repository:${repository._id}:files:${gitlabProductionBranch}`;

            const repositoryFiles = await redis.get(redisKeyFiles);

            const filesPath = repositoryFiles ? JSON.parse(repositoryFiles) : [];

            const redisKeyTree = `repository:${repository._id}:tree:${gitlabProductionBranch}`;

            const repositoryTree = await redis.get(redisKeyTree);
            const filesTree = repositoryTree ? JSON.parse(repositoryTree) : [];

            await RepositoryStructure.updateOne(
                { repository_id: repository._id },
                {
                    $set: {
                        files_path: filesPath,
                        files_tree: filesTree
                    },
                    $setOnInsert: {
                        created_at: new Date()
                    }
                },
                { upsert: true }
            ).lean();

            console.log(`Repository data migrated successfully in MongoDB => ${repository._id}`);
        }

        console.log('Repository data migration from Redis to MongoDB completed.');
        return ReS(res, constants.success_code, 'Migration Success', repositoryList.length);
    } catch (err) {
        logger.error(`Error at CMS Controller migrateRepositoryStructureFromRedisToMongoDB: ${err}`);
        return sendError(res, err);
    }
}

async function fetchAndSyncUserUsedStorage(req, res) {
    try {
        const repositoryList = await GitlabRepository.find({}, 'user_id project_id').lean();

        // Fetch all statistics in parallel
        const statPromises = repositoryList.map((repo) => {
            if (repo?.project_id) {
                return fetchGitlabProjectStatistics(repo.project_id)
                    .then((stat) => ({ project_id: repo.project_id, storage_size: stat?.storage_size }))
                    .catch(() => null); // fail-safe to avoid total crash
            }
            return null;
        });

        const stats = await Promise.all(statPromises);

        // Batch update repositories
        const repoUpdates = stats
            .filter(Boolean)
            .map(({ project_id, storage_size }) => ({
                updateOne: {
                    filter: { project_id },
                    update: { $set: { storage_size } }
                }
            }));

        if (repoUpdates.length) {
            await GitlabRepository.bulkWrite(repoUpdates);
        }

        // Aggregate used storage per user in-memory
        const allRepos = await GitlabRepository.find({}, 'user_id storage_size').lean();
        const userStorageMap = {};

        for (const repo of allRepos) {
            if (!repo.user_id) continue;
            const size = repo.storage_size || 0;
            userStorageMap[repo.user_id] = (userStorageMap[repo.user_id] || 0) + size;
        }

        const userList = await Users.find({ is_verified: true }, '_id').lean();

        const userUpdates = userList.map((user) => ({
            updateOne: {
                filter: { _id: user._id },
                update: {
                    $set: {
                        storage_limit: 500 * 1024 * 1024,
                        used_storage: userStorageMap[user._id] || 0,
                        last_storage_sync: new Date()
                    }
                }
            }
        }));

        if (userUpdates.length) {
            await Users.bulkWrite(userUpdates);
        }
        return ReS(res, constants.success_code, 'Storage sync completed.', userUpdates.length);
    } catch (err) {
        console.error(`Error at CMS Controller fetchAndSyncUserUsedStorage ${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    updateComponentsV2,
    updateComponentsModifiers,
    updateComponentsModifiersToArray,
    updateComponentsState,
    updateCodeSpaceTechnologiesToArray,
    updateComponentCollectionWithStatistics,
    updateComponentCollectionWithTechnologies,
    updateUserCollectionWithComponentStatistics,
    addCollaboratorCollectionOwnerRole,
    updateGitlabRepoDomain,
    createProjectForPublicCodeSpace,
    regenerateElementsThumbnails,
    migrateRepositoryStructureFromRedisToMongoDB,
    fetchAndSyncUserUsedStorage
};