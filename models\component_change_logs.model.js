const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { componentUserAction } = require('./../config/component.constant');

const componentChangeLogSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    changed_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    type: {
        type: String,
        enum: Object.values(componentUserAction),
        required: true
    },
    logs: {
        type: Object
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const ComponentChangeLog = mongoose.model('component_change_logs', componentChangeLogSchema);

module.exports = {
    ComponentChangeLog
};