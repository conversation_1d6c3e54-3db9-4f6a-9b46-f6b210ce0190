const Joi = require('joi');
class TopUpPlanValidation {
    createPlan(value) {
        const schema = Joi.object({
            plan_name: Joi.string().required(),
            plan_coins: Joi.number().integer().min(0).required(),
            bonus_coins: Joi.number().integer().min(0).required(),
            bonus_percentage: Joi.number().integer().min(0).optional(),
            plan_price: Joi.number().positive().required(),
            is_active: Joi.boolean().optional(),
            plan_media: Joi.string().optional()
        });
        return schema.validate(value);
    }

    updatePlan(value) {
        const schema = Joi.object({
            plan_name: Joi.string().required(),
            plan_coins: Joi.number().integer().min(0).required(),
            bonus_coins: Joi.number().integer().min(0).required(),
            bonus_percentage: Joi.number().integer().min(0).optional(),
            plan_price: Joi.number().positive().required(),
            is_active: Joi.boolean().optional(),
            plan_media: Joi.string().optional()
        });
        return schema.validate(value);
    }

    updatePlanStatus(value) {
        const schema = Joi.object({
            is_active: Joi.boolean().required()
        });
        return schema.validate(value);
    }
}

module.exports = new TopUpPlanValidation();