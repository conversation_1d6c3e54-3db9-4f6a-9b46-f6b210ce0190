const Joi = require('joi');

class MergeRequestValidation {
    createMergeRequest(params) {
        const schema = Joi.object({
            source_branch: Joi.string().required(),
            target_branch: Joi.string().required()
        });
        return schema.validate(params);
    }

    addCommentToMergeRequest(params) {
        const schema = Joi.object({
            comment: Joi.string().required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

}

module.exports = new MergeRequestValidation();