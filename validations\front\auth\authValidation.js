const Joi = require('joi');
class AuthValidation {
    signUpWithEmail(params) {
        const schema = Joi.object({
            email: Joi.string().trim().email().required(),
            first_name: Joi.string().trim().required(),
            last_name: Joi.string().trim().required(),
            terms_and_conditions: Joi.boolean().optional().allow(null, ''),
            guest_id: Joi.string().required(),
            otp: Joi.string().min(6).max(6).optional().custom((value, helpers) => {
                // Check if all characters in the OTP are the same
                if (/^(\d)\1*$/.test(value)) {
                    return helpers.message('OTP should not consist of the same digit repeated.');
                }
                // Check for patterns like 111222 or 333444
                if (/(\d)\1{2,}(\d)\2{2,}/.test(value)) {
                    return helpers.message('OTP should not contain repeated digit patterns like 111222 or 333444.');
                }
                return value; // Keep the value as is if it passes the check
            }),
            username: Joi.string()
                // .pattern(new RegExp('/^[a-zA-Z][a-zA-Z0-9-]{2,}$/'))  // Allows alphanumeric characters and dash
                .min(3)                                  // Minimum length of 3 characters
                .max(15)                                 // Maximum length of 15 characters
                .required()                              // Username is required
                .invalid('admin', 'root', 'support', 'git', 'public', 'dashboard')
                .messages({
                    'string.pattern.base': 'Username can only contain letters, numbers, and dash.',
                    'string.min': 'Username should have at least 3 characters.',
                    'string.max': 'Username should not exceed 15 characters.'
                })
        });
        return schema.validate(params);
    }

    loginWithEmail(params) {
        const schema = Joi.object({
            email: Joi.string().email().required()
        });
        return schema.validate(params);
    }

    verifyEmailOtp(params) {
        const schema = Joi.object({
            otp: Joi.number().required()
        });
        return schema.validate(params);
    }

    checkUsernameAvailability(params) {
        const schema = Joi.object({
            username: Joi.string().trim().max(15).required()
        });
        return schema.validate(params);
    }

    checkEmailAvailability(params) {
        const schema = Joi.object({
            email: Joi.string().required()
        });
        return schema.validate(params);
    }

    signUpWithSocial(params) {
        const schema = Joi.object({
            idToken: Joi.string().required(),
            provider_name: Joi.string().required(),
            guest_id: Joi.string().optional(),
            terms_and_conditions: Joi.boolean().optional(),
            username: Joi.string()
                // .pattern(new RegExp('/^[a-zA-Z][a-zA-Z0-9-]{2,}$/'))  // Allows alphanumeric characters and dash
                .min(3)                                  // Minimum length of 3 characters
                .max(15)                                 // Maximum length of 15 characters
                .optional()
                .invalid('admin', 'root', 'support', 'git', 'public', 'dashboard')                      // Username is required
                .messages({
                    'string.pattern.base': 'Username can only contain letters, numbers, and dash.',
                    'string.min': 'Username should have at least 3 characters.',
                    'string.max': 'Username should not exceed 15 characters.'
                })
        });
        return schema.validate(params);
    }

    guestLogin(params) {
        const schema = Joi.object({
            guest_id: Joi.string().required()
        });
        return schema.validate(params);
    }

    addUserInterest(params) {
        const schema = Joi.object({
            email: Joi.string().email().required()
        });
        return schema.validate(params);
    }

    verifyLoginOtp(params) {
        const schema = Joi.object({
            otp: Joi.string().min(6).max(6).required().custom((value, helpers) => {
                // Check if all characters in the OTP are the same
                if (/^(\d)\1*$/.test(value)) {
                    return helpers.message('OTP should not consist of the same digit repeated.');
                }
                // Check for patterns like 111222 or 333444
                if (/(\d)\1{2,}(\d)\2{2,}/.test(value)) {
                    return helpers.message('OTP should not contain repeated digit patterns like 111222 or 333444.');
                }
                return value; // Keep the value as is if it passes the check
            })
        });
        return schema.validate(params);
    }

    resendSignUpOtp(params) {
        const schema = Joi.object({
            email: Joi.string().email().required(),
            first_name: Joi.string().trim().required()
        });
        return schema.validate(params);
    }
}

module.exports = new AuthValidation();