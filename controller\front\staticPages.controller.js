// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, sendError } = require('../../services/general.helper');

// Models declaration
const StaticPage = require('../../models/static_page.model').StaticPage;

const { fetchFileContent } = require('./../../services/gitlab.helper');
const { findComponentAndRepository } = require('../../services/repository.service');

const { gitlabProductionBranch, gitlabDefaultBranch } = require('../../config/gitlab.constant');


async function getSingleStaticPage(req, res) {
    try {
        const filter = {
            slug: req.params.slug,
            is_active: true
        };

        const pageData = await StaticPage.findOne(filter).lean();

        if (pageData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Page Not Found.');
        }

        return ReS(res, constants.success_code, 'Data Fetched', pageData);
    } catch (err) {
        logger.error(`Error at Front Controller getSingleStaticPage${err}`);
        return sendError(res, err);
    }
}

async function fetchRepositoryAssets(req, res) {
    try {

        const { id, path } = req.params;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const response = await fetchFileContent(result.repository.project_id, path, gitlabProductionBranch);


        const base64Content = response.data;
        res.setHeader('Content-Type', response.headers['content-type']);
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', response.headers['content-disposition']);

        return res.send(base64Content);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryFileContent: ${err}`);
        return sendError(res, err);
    }
}

async function fetchRepositoryAssetsDevelopment(req, res) {
    try {

        const { id, path } = req.params;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const response = await fetchFileContent(result.repository.project_id, path, gitlabDefaultBranch);


        const base64Content = response.data;
        res.setHeader('Content-Type', response.headers['content-type']);
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', response.headers['content-disposition']);

        return res.send(base64Content);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryFileContent: ${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    getSingleStaticPage,
    fetchRepositoryAssets,
    fetchRepositoryAssetsDevelopment
};