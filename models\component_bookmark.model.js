const mongoose = require('mongoose');
const logger = require('../config/logger');
const Schema = mongoose.Schema;

const ComponentStatistics = require('./component_statistics.model').ComponentStatistics;
const Components = require('./component.model').Components;
const Users = require('./users.model').Users;

const componentBookmarkSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    creator: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    bookmarked_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    is_active: {
        type: Boolean,
        default: true
    },
    is_deleted: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

// Define a post-save hook for the componentBookmarkSchema
componentBookmarkSchema.post('save', async function () {
    try {
        // Capture the reference to the current context
        const currentContext = this;
        // Check if the context and component_id exist
        if (currentContext && currentContext.component_id) {
            // Increment the bookmarks count for the corresponding component
            const country = 'GLOBAL';
            // Prepare update query to increment total bookmarks and country-specific bookmarks
            const updateQuery = {
                $inc: {
                    'bookmarks.total_bookmarks': 1,
                    [`bookmarks.countries.${country}`]: 1
                }
            };

            // Update or insert the ComponentStatistics document
            await ComponentStatistics.updateOne(
                { component_id: currentContext.component_id }, // Filter by component_id
                updateQuery, // Update query
                { upsert: true } // Create a new document if not found
            );

            // Update bookmarks in component document as well
            await Components.updateOne({
                _id: currentContext.component_id
            }, {
                $inc: {
                    bookmarks: 1
                }
            });

            // Fetch the component by ID and retrieve the 'created_by_user' field
            const { created_by_user } = await Components.findById(
                currentContext.component_id,
                'created_by_user'
            ).lean() || {};

            // If the 'created_by_user' field exists, increment their 'total_bookmarks'
            if (created_by_user) {
                await Users.updateOne({
                    _id: created_by_user
                }, {
                    $inc: {
                        total_bookmarks: 1
                    }
                });
            }
        }
    } catch (error) {
        // Log any errors that occur during the operation
        logger.error(`Error occurred in the post-save hook of componentBookmarkSchema:${error}`);
    }
});

// Define a post-deleteOne hook for the componentBookmarkSchema
componentBookmarkSchema.post('findOneAndDelete', async function (doc) {
    try {
        // Access the deleted document from the 'doc' parameter
        const deletedDocument = doc;
        // Check if the context and component_id exist
        if (deletedDocument && deletedDocument.component_id) {
            // Determine the country based on the provided code, defaulting to 'GLOBAL' if not found
            const country = 'GLOBAL';

            // Prepare update query to increment total bookmarks and country-specific bookmarks
            const updateQuery = {
                $inc: {
                    'bookmarks.total_bookmarks': -1,
                    [`bookmarks.countries.${country}`]: -1
                }
            };

            // Update or insert the ComponentStatistics document
            await ComponentStatistics.updateOne(
                { component_id: deletedDocument.component_id }, // Filter by component_id
                updateQuery, // Update query
                { upsert: true } // Create a new document if not found
            );

            // Update bookmarks in component document as well
            await Components.updateOne({
                _id: deletedDocument.component_id
            }, {
                $inc: {
                    bookmarks: -1
                }
            });

            // Fetch the component by ID and retrieve the 'created_by_user' field
            const { created_by_user } = await Components.findById(
                deletedDocument.component_id,
                'created_by_user'
            ).lean() || {};

            // If the 'created_by_user' field exists, decrement their 'total_bookmarks'
            if (created_by_user) {
                await Users.updateOne({
                    _id: created_by_user
                }, {
                    $inc: {
                        total_bookmarks: -1
                    }
                });
            }
        }
    } catch (error) {
        // Log any errors that occur during the operation
        logger.error(`Error occurred in the post-deleteOne hook of componentBookmarkSchema:${error}`);
    }
});


const ComponentBookmarks = mongoose.model('component_bookmarks', componentBookmarkSchema);

module.exports = {
    ComponentBookmarks
};