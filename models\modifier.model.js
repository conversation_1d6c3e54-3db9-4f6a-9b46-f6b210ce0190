const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const modifierSchema = new Schema({
    title: {
        type: String,
        require: true
    },
    slug: {
        type: String,
        required: true
    },
    description: {
        type: String
    },
    selected_option: {
        type: String
    },
    platform_id: [{
        type: mongoose.Types.ObjectId,
        ref: 'supported_platforms'
    }],
    options: [{
        _id: false,
        section_id: {
            type: mongoose.Types.ObjectId,
            ref: 'sections'
        },
        input_data: {
            type: mongoose.Schema.Types.Mixed
        }
    }],
    is_active: {
        type: Boolean,
        default: true
    },
    created_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    updated_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const Modifiers = mongoose.model('modifiers', modifierSchema);

module.exports = {
    Modifiers
};