const { createTemplate, updateTemplate, updateTemplateStatus } = require('../../../../validations/cms/template/templateValidation');

class TemplateValidationMiddleware {
    createTemplateValidation(req, res, next) {
        const { value, error } = createTemplate(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateTemplateValidation(req, res, next) {
        const { value, error } = updateTemplate(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateTemplateStatusValidation(req, res, next) {
        const { value, error } = updateTemplateStatus(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new TemplateValidationMiddleware(); 