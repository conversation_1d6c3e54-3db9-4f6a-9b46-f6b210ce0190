const constants = require('../../config/constants');
const { coinTypes } = require('../../config/component.constant');

const { ReS, sendError } = require('../../services/general.helper');

const UserCoinsHistory = require('../../models/user_coins_history.model').UserCoinsHistory;

const moment = require('moment');
const logger = require('../../config/logger');

// Refactored function with comments
async function getSystemPointStatistics(req, res) {
    try {
        // Define future date, start date, and end date for filtering
        const futureDate = moment().add(30, 'days');
        const startDate = moment().startOf('day');
        const endDate = moment(futureDate).endOf('day');

        // Define aggregation pipelines for fetching system point statistics
        const pipelines = [
            // Pipeline to aggregate different types of coins
            {
                '$facet': {
                    'normalCoins': [
                        // Match normal coins
                        {
                            '$match': { 'type': coinTypes.NORMAL }
                        },
                        // Group by type and calculate total coins
                        {
                            '$group': {
                                '_id': null,
                                'totalCoins': { '$sum': '$points' }
                            }
                        }
                    ],
                    'bonusCoins': [
                        // Match bonus coins
                        {
                            '$match': { 'type': coinTypes.BONUS }
                        },
                        // Group by type and calculate total coins
                        {
                            '$group': {
                                '_id': null,
                                'totalCoins': { '$sum': '$points' }
                            }
                        }
                    ],
                    'bonusExpiring': [
                        // Match bonus coins expiring within the next 30 days
                        {
                            $match: {
                                'type': coinTypes.BONUS,
                                expired_on: {
                                    $gte: new Date(startDate),
                                    $lte: new Date(endDate)
                                }
                            }
                        },
                        // Group by type and calculate total pending coins
                        {
                            '$group': {
                                '_id': null,
                                'totalCoins': { '$sum': '$pending_points' }
                            }
                        }
                    ]
                }
            },
            // Project stage to reshape output and handle null values
            {
                '$project': {
                    '_id': 0,
                    'totalMpPoints': {
                        '$ifNull': [
                            { '$arrayElemAt': ['$normalCoins.totalCoins', 0] },
                            0
                        ]
                    },
                    'totalMpBonus': {
                        '$ifNull': [
                            { '$arrayElemAt': ['$bonusCoins.totalCoins', 0] },
                            0
                        ]
                    },
                    'bonusExpiringInNext30Days': {
                        '$ifNull': [
                            { '$arrayElemAt': ['$bonusExpiring.totalCoins', 0] },
                            0
                        ]
                    }
                }
            }
        ];

        // Execute aggregation pipeline
        const points = await UserCoinsHistory.aggregate(pipelines);

        // Respond with success and fetched data
        return ReS(res, constants.success_code, 'Data Fetched', points[0]);
    } catch (err) {
        // Handle errors
        logger.error(`Error at CMS Controller getSystemPointStatistics${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    getSystemPointStatistics
};