// Constants declaration
const constants = require('../../config/constants');
const forge = require('node-forge');
const mongoose = require('mongoose');
const moment = require('moment');
// Service declaration
const { ReS, sendError, generateRandomPassword, generateGitlabTokenName, encryptDataWithAES, decryptDataWithAES } = require('../../services/general.helper');
const logger = require('../../config/logger');

const { sendSubAdminWelcomeEmail } = require('../../services/send_email.service');
const { createUserAccessToken, checkUserNameAvailability } = require('./../../services/gitlab.helper');
const { gitlabUserScope } = require('../../config/gitlab.constant');
const { createRepositoryUserFromAdmin } = require('./../../services/repository.service');

// Models declaration
const Admins = require('../../models/admins.model').Admins;
const GitlabUsers = require('../../models/gitlab_users.model').GitlabUsers;
const GitlabUserTokens = require('../../models/gitlab_user_tokens.model').GitlabUserTokens;

async function createAdmin(req, res) {
    try {
        const {
            email,
            roles,
            first_name,
            last_name
        } = req.body;
        // Check if the admin with provided email already exists
        const adminUser = await Admins.findOne({ email }).lean();

        if (adminUser) {
            // If user already exists, return conflict status with user details
            return res.status(constants.accepted_code).json({ 'status': 409, 'message': 'Oops! admin already registered', 'data': { admin_id: adminUser._id, is_active: adminUser.is_active } });
        }
        const password = generateRandomPassword(8);

        const hashPassword = await forge.md.sha512.create().update(password).digest().toHex();

        await Admins.create({
            email: email,
            password: hashPassword,
            roles: roles,
            first_name: first_name,
            last_name: last_name
        });

        const fullName = `${first_name} ${last_name}`;

        // Send sub-admin welcome email with password
        await sendSubAdminWelcomeEmail(fullName, email, password);
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createAdmin${err}`);
        return sendError(res, err);
    }
}

async function getAdminDetails(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };

        const adminData = await Admins.findOne(filter, {
            password: 0
        }).lean();

        if (adminData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Admin Not Found.');
        }

        return ReS(res, constants.success_code, 'Data Fetched', adminData);
    } catch (err) {
        logger.error(`Error at CMS Controller getAdminDetails${err}`);
        return sendError(res, err);
    }
}

async function updateAdminStatus(req, res) {
    try {
        const { is_active } = req.body;

        const filter = {
            _id: req.params.id
        };

        const adminData = await Admins.findOne(filter).lean();

        if (adminData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Admin Not Found.');
        }

        await Admins.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateAdminStatus${err}`);
        return sendError(res, err);
    }
}

async function updateAdmin(req, res) {
    try {
        const postData = req.body;

        const admin = await Admins.findOne({
            _id: req.params.id
        }).lean();

        if (admin == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Admin  Not Found.');
        }

        await Admins.updateOne({
            _id: req.params.id
        }, {
            '$set': postData
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateAdmin${err}`);
        return sendError(res, err);
    }
}

async function deleteAdmin(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };

        await Admins.deleteOne(filter).then((data) => {
            return ReS(res, constants.success_code, 'Success', data);
        }).catch((err) => {
            throw new Error(err);
        });

    } catch (err) {
        logger.error(`Error at CMS Controller deleteAdmin${err}`);
        return sendError(res, err);
    }
}

async function changePassword(req, res) {
    try {
        // Get user ID from session
        const userId = req.session._id;

        // Extract current and new passwords from request body
        const { current_password, new_password } = req.body;

        // Find user data from database
        const userData = await Admins.findOne({
            _id: userId
        }).lean();

        // If user data is not found, return error
        if (userData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! User Not Found.');
        }

        // Check if current password is the same as new password
        if (current_password == new_password) {
            return ReS(res, constants.bad_request_code, 'Oops! The current password and new password must be different.');
        }

        // Generate hash for the current password
        const currentHashPassword = await forge.md.sha512.create().update(current_password).digest().toHex();

        // Verify if provided current password matches with stored password hash
        if (userData.password != currentHashPassword) {
            return ReS(res, constants.bad_request_code, 'Oops! The provided current password is invalid.');
        }

        // Generate hash for the new password
        const newHashPassword = await forge.md.sha512.create().update(new_password).digest().toHex();

        // Update user's password in the database
        await Admins.updateOne({
            _id: userId
        }, {
            $set: {
                password: newHashPassword
            }
        });

        // Return success response
        return ReS(res, constants.success_code, 'Password changed successfully.');
    } catch (err) {
        // Handle errors
        console.log('Error at CMS Controller changePassword', err);
        return sendError(res, err);
    }
}

async function generateGitlabAccessToken(req, res) {
    try {
        // Get user ID from the session
        const userId = req.session._id;

        // Find user data from the database
        const userData = await Admins.findOne({ _id: userId }, 'gitlab_user_exists').lean();

        // If user data is not found, return error
        if (!userData) {
            return ReS(res, constants.resource_not_found, 'Oops! User Not Found.');
        }

        // If the user does not exist on GitLab, return error
        if (!userData.gitlab_user_exists) {
            return ReS(res, constants.bad_request_code, 'Oops! User Not exists on Gitlab.');
        }

        // Retrieve GitLab user data
        const gitlabUser = await GitlabUsers.findOne({ admin_id: userId }, 'gitlab_user_id personal_access_token_name').lean();

        // Retrieve last Gitlab access token details
        const lastAccessToken = await GitlabUserTokens.findOne({
            user_id: gitlabUser._id
        }).sort({
            created_at: -1
        });

        // Generate a new token name for the GitLab user
        const tokenName = generateGitlabTokenName(lastAccessToken.personal_access_token_name);

        // Create a new GitLab user access token
        const gitlabUserToken = await createUserAccessToken(gitlabUser.gitlab_user_id, tokenName, gitlabUserScope);

        // Create GitLab user document with the generated token and its details
        await GitlabUserTokens.create({
            user_id: gitlabUser._id,
            personal_access_token_name: tokenName,
            personal_access_tokens: encryptDataWithAES(gitlabUserToken.token),
            token_expires_at: gitlabUserToken.expires_at,
            scopes: gitlabUserToken.scopes
        });
        // Return success response
        return ReS(res, constants.success_code, 'Gitlab access token generated successfully.', {
            personal_access_token_name: tokenName,
            personal_access_tokens: gitlabUserToken.token,
            token_expires_at: new Date(gitlabUserToken.expires_at)
        });
    } catch (err) {
        // Log and handle errors
        console.log('Error in generateGitlabAccessToken:', err);
        return sendError(res, err);
    }
}

async function getAdminProfile(req, res) {
    try {
        // Extract user ID from the session
        const userId = req.session._id;

        // Fetch user data from the database based on the user ID
        const userData = await Admins.findOne({ _id: userId }, '_id').lean();

        // If user data is not found, return an error response
        if (!userData) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }

        // Define the aggregation pipeline to fetch admin profile and associated GitLab user data
        const pipelines = [
            {
                $match: {
                    _id: new mongoose.Types.ObjectId(userId) // Match admin by user ID
                }
            },
            {
                $lookup: {
                    from: 'gitlab_users', // Join with gitlab_users collection
                    let: { admin_id: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$admin_id', '$$admin_id'] }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                email: 1,
                                username: 1,
                                extern_uid: 1,
                                personal_access_tokens: 1,
                                personal_access_token_name: 1,
                                token_expires_at: 1 // Project required fields
                            }
                        }
                    ],
                    as: 'gitlab_user' // Output array field for the joined data
                }
            },
            {
                $unwind: {
                    path: '$gitlab_user',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: 'gitlab_user_tokens', // Join with gitlab_user_tokens collection
                    let: { admin_id: '$gitlab_user._id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$user_id', '$$admin_id'] }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                personal_access_tokens: 1,
                                personal_access_token_name: 1,
                                created_at: 1,
                                token_expires_at: 1 // Project required fields
                            }
                        },
                        {
                            $sort: {
                                created_at: 1
                            }
                        }
                    ],
                    as: 'gitlab_user.access_tokens' // Output array field for the joined data
                }
            },
            {
                $project: {
                    first_name: 1,
                    last_name: 1,
                    email: 1,
                    gitlab_user_exists: 1,
                    gitlab_user: 1,
                    is_active: 1 // Project required fields,
                }
            }
        ];

        // Execute the aggregation pipeline
        const adminData = await Admins.aggregate(pipelines);

        // Get the first result from the aggregation
        const adminProfile = (adminData.length) ? adminData[0] : null;

        // Decrypt personal access tokens if available
        if (adminProfile?.gitlab_user?.access_tokens) {
            adminProfile.gitlab_user.access_tokens.forEach((token) => {
                token.personal_access_tokens = decryptDataWithAES(token.personal_access_tokens);
            });
        }
        // Return success response with admin profile data
        return ReS(res, constants.success_code, 'Admin profile retrieved successfully.', adminProfile);
    } catch (err) {
        // Log the error and return error response
        console.error('Error in getAdminProfile:', err);
        return sendError(res, err);
    }
}


async function downloadGitlabCredentials(req, res) {
    try {
        // Extract user ID from the session
        const userId = req.session._id;

        // Fetch user data from the database based on the user ID
        const userData = await Admins.findOne({ _id: userId }, 'gitlab_user_exists').lean();

        // If user data is not found, return an error response
        if (!userData) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }

        // If the user does not exist on GitLab, return error
        if (!userData.gitlab_user_exists) {
            return ReS(res, constants.bad_request_code, 'Oops! User does not exist on GitLab.');
        }

        // Retrieve GitLab user data
        const gitlabUser = await GitlabUsers.findOne({
            admin_id: userId
        }, 'email username gitlab_user_id').lean();

        // Retrieve last GitLab access token
        const lastAccessToken = await GitlabUserTokens.findOne({
            user_id: gitlabUser._id
        }).sort({
            created_at: -1
        }).lean();

        if (!lastAccessToken) {
            return ReS(res, constants.bad_request_code, 'Oops! User does not contains any active access token');
        }

        // Decrypt the personal access tokens
        const accessTokens = decryptDataWithAES(lastAccessToken.personal_access_tokens);

        // Create the content for the file
        const fileContent = `MPN Creator Email: ${gitlabUser.email}\nMPN Creator Username: ${gitlabUser.username}\nAccess Token: ${accessTokens}\nToken Expires At: ${moment(new Date(lastAccessToken.token_expires_at)).format('DD/MM/YYYY hh:mm A')}`;

        // Set the file name using the GitLab username
        const fileName = `${gitlabUser.username}.txt`;

        // Set response headers for file download
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);
        res.setHeader('Content-Type', 'text/plain');

        // Send the file content
        res.send(fileContent);
    } catch (err) {
        // Log the error and return error response
        console.error('Error in downloadGitlabCredentials:', err);
        return sendError(res, err);
    }
}

async function checkGitlabUserNameAvailability(req, res) {
    const { username } = req.query;
    const isAvailable = await checkUserNameAvailability(username);
    // Return success response with admin profile data
    return ReS(res, constants.success_code, 'Admin profile retrieved successfully.', { isAvailable });
}

async function createGitlabUser(req, res) {
    try {
        // Extract user ID from the session
        const adminId = req.session._id;

        const { username } = req.body;

        // Fetch user data from the database based on the user ID
        const userData = await Admins.findOne({ _id: adminId }, 'gitlab_user_exists').lean();

        // If user data is not found, return an error response
        if (!userData) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }

        // If the user exist on GitLab, return error
        if (userData && userData.gitlab_user_exists == true) {
            return ReS(res, constants.conflict_code, 'Oops! GitLab user already exists');
        }

        const gitlabUser = await createRepositoryUserFromAdmin(adminId, username);

        // Return success response with admin profile data
        return ReS(res, constants.success_code, 'Creator profile created successfully.', gitlabUser);
    } catch (err) {
        // Log the error and return error response
        console.error('Error in createGitlabUser:', err);
        return sendError(res, err);
    }
}

module.exports = {
    createAdmin,
    getAdminDetails,
    updateAdmin,
    updateAdminStatus,
    deleteAdmin,
    changePassword,
    generateGitlabAccessToken,
    getAdminProfile,
    downloadGitlabCredentials,
    checkGitlabUserNameAvailability,
    createGitlabUser
};