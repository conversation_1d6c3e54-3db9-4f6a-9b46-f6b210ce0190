const express = require('express');
const router = express.Router();

const { createSectionValidation, updateSectionValidation } = require('../../middlewares/validations/cms/section/sectionValidation');
const { createSection, getAllSection, getAllSectionSortList, getSectionDetails, updateSection } = require('../../controller/cms/section.controller');


router.post('/create', createSectionValidation, createSection);
router.put('/update/:id', updateSectionValidation, updateSection);
router.get('/sort-list', getAllSectionSortList);
router.post('/list', getAllSection);
router.get('/details/:id', getSectionDetails);


module.exports = router;