const express = require('express');
const router = express.Router();

const { createLicense, getLicense, getLicenseList, updateLicense, updateLicenseStatus } = require('../../controller/cms/platformLicenses.controller');

const { createLicenseValidation, updateLicenseValidation } = require('../../middlewares/validations/cms/platform_license/platformLicenseValidation');

router.post('/create', createLicenseValidation, createLicense);
router.get('/details/:id', getLicense);
router.post('/list', getLicenseList);
router.put('/update/:id', updateLicenseValidation, updateLicense);
router.put('/update/status/:id', updateLicenseStatus);

module.exports = router;