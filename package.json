{"name": "multi-platform-backend", "version": "0.0.0", "private": true, "scripts": {"start": "node app.js", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@fingerprintjs/fingerprintjs-pro-server-api": "^4.0.1", "@supercharge/request-ip": "^1.2.0", "adm-zip": "^0.5.15", "archiver": "^7.0.1", "aws-sdk": "^2.1495.0", "axios": "^1.6.8", "bullmq": "^5.51.0", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "country-iso-2-to-3": "^1.1.0", "debug": "~2.6.9", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.16.4", "express-fileupload": "^1.4.2", "express-rate-limit": "^7.1.5", "ffmpeg-static": "^5.2.0", "file-type": "^16.5.4", "fluent-ffmpeg": "^2.1.3", "google-auth-library": "^9.7.0", "http-errors": "~1.6.3", "image-size": "^1.1.1", "ioredis": "^5.4.1", "jade": "~1.11.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "moment": "^2.30.1", "mongoose": "^7.6.4", "morgan": "~1.9.1", "multer": "^1.4.5-lts.1", "nanoid": "^3.3.11", "node-cron": "^3.0.3", "node-forge": "^1.3.1", "nodemailer": "^6.9.11", "pino": "^8.20.0", "pino-pretty": "^11.0.0", "puppeteer": "^21.3.8", "rss-to-json": "^2.1.1", "sharp": "^0.34.1", "slugify": "^1.6.6", "socket.io": "^4.8.1", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^9.26.0"}}