const express = require('express');
const router = express.Router();

const { fetchUnReadNotifications, notificationMarkAsRead, fetchNotificationUnReadCount, notificationMarkAllAsRead } = require('../../controller/front/notification.controller');

router.post('/me/fetch-unread', fetchUnReadNotifications);
router.post('/:id/mark-as-read', notificationMarkAsRead);
router.get('/me/fetch-unread-count', fetchNotificationUnReadCount);
router.put('/me/mark-all-as-read', notificationMarkAllAsRead);

module.exports = router;