// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, escapeRegex, sendError } = require('../../services/general.helper');

// Models declaration
const AdminPermissionGroups = require('../../models/admin_permission_groups.model').AdminPermissionGroups;

async function createPermissionGroup(req, res) {
    try {
        const { permission_group } = req.body;

        await AdminPermissionGroups.bulkWrite(
            permission_group.map((group) => ({
                updateOne: {
                    filter: { 'group_name': group.group_name },
                    update: {
                        $set: {
                            module_name: group.module_name,
                            is_active: true,
                            group_name: group.group_name,
                            created_by: req.session._id
                        },
                        $addToSet: { permission_list: { $each: group.permission_list } }
                    },
                    upsert: true
                }
            }))
        );

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createPermissionGroup ${err}`);
        return sendError(res, err);
    }
}

async function getAllPermissionGroups(req, res) {
    try {
        const totalDocuments = await AdminPermissionGroups.countDocuments();
        const filter = {};
        if (req.body.is_active != undefined) {
            filter.is_active = req.body.is_active;
        }
        if (req.body.group_name) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.group_name);
            filter['$or'] = [{
                'group_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        // Default sort
        const sort = {
            'order': 1
        };
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        // Count filtered documents after filter apply
        const filterDocuments = await AdminPermissionGroups.countDocuments(filter);
        const permissionGroupList = await AdminPermissionGroups.find(filter, {
            'group_name': 1,
            'module_name': 1,
            'permission_list': 1,
            'is_active': 1,
            'created_at': 1
        }).populate({
            path: 'permission_list',
            select: 'display_name name module'
        }).sort(sort).skip(skip).limit(limit).lean();

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: permissionGroupList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllPermissionGroups${err}`);
        return sendError(res, err);
    }
}

async function updatePermissionGroupStatus(req, res) {
    try {
        const { is_active } = req.body;

        const permissionGroup = await AdminPermissionGroups.findOne({
            _id: req.params.id
        }).lean();

        if (permissionGroup == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Admin Permission Group Not Found.');
        }

        await AdminPermissionGroups.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active,
                updated_by: req.session._id
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updatePermissionGroupStatus${err}`);
        return sendError(res, err);
    }
}

async function updatePermissionGroup(req, res) {
    try {
        const postData = req.body;

        const permissionGroup = await AdminPermissionGroups.findOne({
            _id: req.params.id
        }).lean();

        if (permissionGroup == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Admin Permission Group Not Found.');
        }

        // Set the updated_by field to the current user's session ID
        postData['updated_by'] = req.session._id;

        await AdminPermissionGroups.updateOne({
            _id: req.params.id
        }, {
            '$set': postData
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updatePermissionGroup${err}`);
        return sendError(res, err);
    }
}

async function getAllPermissionGroupsSortList(req, res) {
    try {
        const filter = {
            is_active: true
        };
        if (req.body.is_active != undefined) {
            filter.is_active = req.body.is_active;
        }
        if (req.body.group_name) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.group_name);
            filter['$or'] = [{
                'group_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        // Default sort
        const sort = {
            'created_at': 1
        };
        const permissionGroupList = await AdminPermissionGroups.find(filter, {
            'group_name': 1,
            'module_name': 1,
            'permission_list': 1,
            'is_active': 1,
            'created_at': 1
        }).sort(sort).lean();

        return ReS(res, constants.success_code, 'Data Fetched', permissionGroupList);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllPermissionGroupsSortList${err}`);
        return sendError(res, err);
    }
}

async function getPermissionGroupDetails(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };

        const permissionGroup = await AdminPermissionGroups.findOne(filter, {
            'group_name': 1,
            'module_name': 1,
            'permission_list': 1,
            'is_active': 1,
            'created_at': 1
        }).populate({
            path: 'permission_list',
            select: 'display_name name module'
        }).lean();

        if (permissionGroup == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Permission Group Not Found.');
        }

        return ReS(res, constants.success_code, 'Data Fetched', permissionGroup);
    } catch (err) {
        logger.error(`Error at CMS Controller getPermissionGroupDetails${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createPermissionGroup,
    getAllPermissionGroups,
    updatePermissionGroup,
    updatePermissionGroupStatus,
    getAllPermissionGroupsSortList,
    getPermissionGroupDetails
};