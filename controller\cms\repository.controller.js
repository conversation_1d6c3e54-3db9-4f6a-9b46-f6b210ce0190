// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');
const { gitlabDefaultBranch } = require('../../config/gitlab.constant');

// Service declaration
const { ReS, sendError, filterObject, createGitlabTree } = require('../../services/general.helper');
const { fetchRepoContent, fetchCommitInfo, fetchFileContent, downloadRepoContent, fetchBranches, createCommit, checkProjectNameAvailability, fetchProjectDetails, getRepositoryFilesRecursively, downloadFileContent, fetchCommitDiff, findRepositoryFile } = require('./../../services/gitlab.helper');

// Models declaration

const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;
const GitlabUsers = require('../../models/gitlab_users.model').GitlabUsers;

// Npm declaration
const AdmZip = require('adm-zip');
const fileType = require('file-type');

async function checkRepositoryNameAvailability(req, res) {
    const { project_name } = req.query;
    const isAvailable = await checkProjectNameAvailability(project_name);
    // Return success response with admin profile data
    return ReS(res, constants.success_code, 'Repository name availability successfully.', { isAvailable });
}

async function getRepositoryContent(req, res) {
    try {
        const { id } = req.params;

        // Extract query parameters with default values
        const { path = '', per_page = 100, ref = gitlabDefaultBranch } = req.query;

        // Find the Gitlab repository
        const repository = await GitlabRepository.findOne({
            _id: id
        }, 'project_id');

        // If no repository is found, return a resource not found response
        if (repository == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        const repositoryData = await fetchRepoContent(repository.project_id, path, per_page, ref);

        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryContent${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryCommitInfo(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        // Extract query parameters with default values
        const { path = '', per_page = 1, ref_name = gitlabDefaultBranch } = req.query;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        // Fetch commit information from GitLab
        const repositoryData = await fetchCommitInfo(repository.project_id, path, per_page, ref_name);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryCommitInfo: ${err}`);
        return sendError(res, err);
    }
}


async function getRepositoryFileContent(req, res) {
    try {

        const { id } = req.params;

        const { path } = req.params;

        // Extract query parameters with default values
        const { ref = gitlabDefaultBranch } = req.query;

        // Find the Gitlab repository
        const repository = await GitlabRepository.findOne({
            _id: id
        }, 'project_id');

        // If no repository is found, return a resource not found response
        if (repository == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        const response = await fetchFileContent(repository.project_id, path, ref);

        const base64Content = response.data;
        res.setHeader('Content-Type', response.headers['content-type']);
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', response.headers['content-disposition']);
        return res.send(base64Content);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryFileContent${err}`);
        return sendError(res, err);
    }
}

async function downloadRepositoryContent(req, res) {
    try {
        const { id } = req.params;

        // Extract query parameters with default values
        const { ref = gitlabDefaultBranch } = req.query;

        // Find the Gitlab repository
        const repository = await GitlabRepository.findOne({
            _id: id
        }, 'project_id');

        // If no repository is found, return a resource not found response
        if (repository == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        const response = await downloadRepoContent(repository.project_id, ref);

        // Set headers from the Axios response to the Express response
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', response.headers['content-disposition']);
        res.setHeader('Content-Type', response.headers['content-type']);
        // Pipe the Axios response data to the Express response
        response.data.pipe(res);
    } catch (err) {
        logger.error(`Error at Front Controller downloadRepositoryContent${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryBranches(req, res) {
    try {

        const { id } = req.params;

        // Find the Gitlab repository
        const repository = await GitlabRepository.findOne({
            _id: id
        }, 'project_id');

        // If no repository is found, return a resource not found response
        if (repository == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        const repositoryData = await fetchBranches(repository.project_id);

        return ReS(res, constants.success_code, 'Data Fetched', repositoryData);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryFileContent${err}`);
        return sendError(res, err);
    }
}

async function createRepositoryCommit(req, res) {
    try {
        const { actions, commit_message } = req.body;

        const { id } = req.params;

        // Fetch GitLab user information based on the admin ID from the session
        const gitlabUser = await GitlabUsers.findOne({ admin_id: req.session._id }).lean();

        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        // Find the Gitlab repository
        const repository = await GitlabRepository.findOne({
            _id: id
        }, 'project_id');

        // If no repository is found, return a resource not found response
        if (repository == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        const repositoryData = await createCommit(
            repository.project_id,
            gitlabDefaultBranch,
            commit_message,
            actions,
            gitlabUser.email,
            gitlabUser.username
        );

        return ReS(res, constants.success_code, 'Data Fetched', repositoryData.id);
    } catch (err) {
        logger.error(`Error at Front Controller createRepositoryCommit${err}`);
        return sendError(res, err);
    }
}

async function fetchRepositoryDetails(req, res) {
    try {
        const { id } = req.params;

        // Find the Gitlab repository
        const repository = await GitlabRepository.findOne({
            _id: id
        }, 'project_id');

        // If no repository is found, return a resource not found response
        if (repository == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        const repositoryData = await fetchProjectDetails(repository.project_id);

        const keysToKeep = ['name', 'http_url_to_repo', 'readme_url', 'path', 'default_branch', 'web_url'];

        const gitlabRepository = filterObject(repositoryData, keysToKeep);

        return ReS(res, constants.success_code, 'Data Fetched', gitlabRepository);
    } catch (err) {
        logger.error(`Error at Front Controller fetchRepositoryDetails${err}`);
        return sendError(res, err);
    }
}

async function uploadFilesToRepository(req, res) {
    try {
        // Check if files are uploaded
        if (!req.files) {
            return res.status(400).send('No file uploaded.');
        }

        // Fetch GitLab user information based on the admin ID from the session
        const gitlabUser = await GitlabUsers.findOne({ admin_id: req.session._id }).lean();
        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        const { id } = req.params;

        // Find the GitLab repository based on the provided repository ID
        const repository = await GitlabRepository.findOne({ _id: id }, 'project_id');
        if (repository == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        // Initialize AdmZip to handle the uploaded zip file
        const zip = new AdmZip(req.files.folder.data);
        const zipEntries = zip.getEntries();
        const newFiles = [];

        // Iterate through the files in the zip
        for (const entry of zipEntries) {
            if (!entry.isDirectory) { // Process only files, not directories
                const filePath = entry.entryName; // Get the file path within the zip
                const fileContent = zip.readAsText(entry); // Read the content of the file
                newFiles.push({ filePath, content: fileContent });
            }
        }

        // Fetch existing files in the repository
        const existingFiles = await getRepositoryFilesRecursively(repository.project_id, gitlabDefaultBranch);

        // Prepare actions for deleting existing files
        const deleteActions = existingFiles.map((filePath) => ({
            action: 'delete',
            file_path: filePath
        }));

        // Prepare actions for creating new files
        const createActions = newFiles.map((file) => ({
            action: 'create',
            file_path: file.filePath,
            content: file.content
        }));

        // Combine delete and create actions
        const actions = [...deleteActions, ...createActions];


        // If no changes are detected, return a response indicating no action was taken
        if (actions.length === 0) {
            return res.status(400).send('No changes to commit.');
        }

        // Prepare the commit message and create the commit in the GitLab repository
        const commitMessage = 'Upload zip file and create commit via API';

        const repositoryData = await createCommit(
            repository.project_id,
            gitlabDefaultBranch,
            commitMessage,
            actions,
            gitlabUser.email,
            gitlabUser.username
        );

        // Respond with the ID of the created commit
        return ReS(res, constants.success_code, 'Data Fetched', repositoryData.id);
    } catch (error) {
        // Log the error and send an error response
        console.error(error);
        return sendError(res, error);
    }
}

async function uploadMultipleFilesToRepository(req, res) {
    try {

        const { path } = req.query;
        const { id } = req.params;
        const { commit_message } = req.body;

        // Check if files are uploaded
        if (!req?.files?.assets) {
            return ReS(res, constants.bad_request_code, 'Oops! No file uploaded.');
        }

        // Check if the 'assets' field exists in the 'req.files' object and if it's not an array but an object
        if (req.files && req.files.assets && !Array.isArray(req.files.assets) && typeof req.files.assets == 'object') {
            // Convert the 'documents' object into an array containing a single element
            req.files.assets = [req.files.assets];
        }

        // Fetch GitLab user information based on the admin ID from the session
        const gitlabUser = await GitlabUsers.findOne({ admin_id: req.session._id }).lean();

        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        // Find the GitLab repository based on the provided repository ID
        const repository = await GitlabRepository.findOne({ _id: id }, 'project_id');
        if (repository == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        const newFiles = [];

        // Iterate through the files in the request
        for (const file of req.files.assets) {
            // Replace spaces with underscore in the file name
            const sanitizedFileName = file.name.replace(/\s+/g, '_'); // Replace one or more spaces with a underscore
            const filePath = `${path}/${sanitizedFileName}`; // Get the file name
            const fileData = file.data; // Get the file data (Buffer)
            const type = await fileType.fromBuffer(fileData); // Detect the MIME type
            // Determine if the file is binary or text-based
            const isBinary = type ? type.mime.startsWith('application/') || type.mime.startsWith('image/') || type.mime.startsWith('audio/') || type.mime.startsWith('video/') : false;
            const fileContent = isBinary ? file.data.toString('base64') : file.data.toString('utf8'); // Use base64 for binary files, utf8 for text files
            newFiles.push({
                filePath,
                content: fileContent,
                encoding: isBinary ? 'base64' : undefined // Specify base64 encoding for binary files
            });
        }
        // Prepare actions for creating new files
        const createActions = newFiles.map((file) => ({
            action: 'create',
            file_path: file.filePath,
            content: file.content,
            encoding: file.encoding
        }));
        // Combine delete and create actions
        const actions = [...createActions];

        // If no changes are detected, return a response indicating no action was taken
        if (actions.length === 0) {
            return res.status(400).send('No changes to commit.');
        }

        // Prepare the commit message and create the commit in the GitLab repository
        const commitMessage = (commit_message) ? commit_message : 'Upload zip file and create commit via API';

        const repositoryData = await createCommit(
            repository.project_id,
            gitlabDefaultBranch,
            commitMessage,
            actions,
            gitlabUser.email,
            gitlabUser.username
        );

        // Respond with the ID of the created commit
        return ReS(res, constants.success_code, 'Data Fetched', repositoryData.id);
    } catch (error) {
        // Log the error and send an error response
        console.error(error);
        return sendError(res, error);
    }
}

async function downloadRepositoryFileContent(req, res) {
    try {
        const { id } = req.params;
        const { path } = req.query;

        // Find the Gitlab repository
        const repository = await GitlabRepository.findOne({
            _id: id
        }, 'project_id');

        // If no repository is found, return a resource not found response
        if (repository == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        const response = await downloadFileContent(repository.project_id, path, gitlabDefaultBranch);

        // Set headers from the Axios response to the Express response
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', response.headers['content-disposition']);
        res.setHeader('Content-Type', response.headers['content-type']);
        // Pipe the Axios response data to the Express response
        response.data.pipe(res);
    } catch (err) {
        logger.error(`Error at Front Controller downloadRepositoryFileContent${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryCommitDiff(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id, sha } = req.params;

        // Extract query parameters with default values
        const { ref_name = gitlabDefaultBranch } = req.query;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        // Fetch commit information from GitLab
        const repositoryData = await fetchCommitDiff(repository.project_id, sha, ref_name);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryCommitDiff: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryContentRecursively(req, res) {
    try {
        const { id } = req.params;

        // Extract query parameters with default values
        const { ref = gitlabDefaultBranch } = req.query;

        // Find the Gitlab repository
        const repository = await GitlabRepository.findOne({
            _id: id
        }, 'project_id');

        // If no repository is found, return a resource not found response
        if (repository == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        const repositoryData = await findRepositoryFile(repository.project_id, ref);

        // Create the tree structure
        const fileTree = await createGitlabTree(repositoryData);

        return res.status(constants.success_code).json(fileTree);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryContentRecursively${err}`);
        return sendError(res, err);
    }
}


module.exports = {
    checkRepositoryNameAvailability,
    getRepositoryContent,
    getRepositoryCommitInfo,
    getRepositoryFileContent,
    downloadRepositoryContent,
    getRepositoryBranches,
    createRepositoryCommit,
    fetchRepositoryDetails,
    uploadFilesToRepository,
    downloadRepositoryFileContent,
    getRepositoryCommitDiff,
    getRepositoryContentRecursively,
    uploadMultipleFilesToRepository
};