
const logger = require('../config/logger');
const { compareArraysOfJSON, compareJSONWithStringify, generateSlug } = require('../services/general.helper');
const { fetchLastCommitDateOnBranch } = require('../services/gitlab.helper');
const { gitlabProductionBranch } = require('../config/gitlab.constant');

// Model declaration
const Components = require('../models/component.model').Components;
const ComponentVersions = require('../models/component_versions.model').ComponentVersions;
const ComponentStatistics = require('../models/component_statistics.model').ComponentStatistics;
const ComponentViews = require('../models/component_views.model').ComponentViews;
const DraftComponents = require('../models/draft_components.model').DraftComponents;
const PlatformLicense = require('../models/platform_licenses.model').PlatformLicense;
const GitlabRepository = require('../models/gitlab_repository.model').GitlabRepository;

// Npm declaration
const ffmpeg = require('fluent-ffmpeg');
const sizeOf = require('image-size');
const { nanoid } = require('nanoid'); // for unique ID suffixes

const { componentOrientation, componentType, componentState, elementFileNames } = require('../config/component.constant');

const checkAndManageComponentVersion = async (component_id, postData, updated_by) => {
    try {
        const docToUpdate = await Components.findOne({ _id: component_id }).lean();
        if (docToUpdate.platform_data && postData.platform_data) {
            const isComponentChanged = await compareArraysOfJSON(docToUpdate.platform_data, postData.platform_data);
            if (!isComponentChanged) {
                await ComponentVersions.create({
                    component_id: component_id,
                    updated_by: updated_by,
                    version: docToUpdate.version,
                    platform_data: docToUpdate.platform_data
                });
                await Components.updateOne({ _id: component_id }, { $inc: { version: 1 } });
            }
        }
    } catch (err) {
        logger.error(`Error at Component Service checkAndManageComponentVersion${err}`);
    }
};

const fetchFullComponentData = async (slug, isPaid, unlockHistory, user_id) => {
    try {
        if (isPaid && !unlockHistory) {
            // For paid components that have not been unlocked, exclude sensitive data
            const componentObj = await Components.findOne({ slug }, { platform_data: 0 }).populate({
                'path': 'created_by_user',
                'select': 'first_name last_name username email avatar biography'
            }).lean();
            const is_author = (componentObj?.created_by_user?._id.toString() == user_id) ? true : false;
            // Check for community_variations counts
            const communityVariations = await Components.countDocuments({
                component_type: componentType.ELEMENTS,
                component_state: componentState.PUBLISHED,
                variant_id: (componentObj.variant_id) ? componentObj.variant_id : componentObj._id
            }).lean();
            componentObj['community_variations'] = communityVariations;
            componentObj['license_id'] = await prepareLicenseDataForPublished(componentObj._id);
            return {
                ...componentObj,
                is_allowed_to_view: false,
                is_author
            };
        } else {
            // For free components or unlocked components, include full data
            const componentObj = await Components.findOne({ slug })
                .populate('platform_data.platform_id', 'title slug image_url')
                .populate({
                    'path': 'platform_data.repository_id',
                    'select': 'project_name project_id component_id description stars forks detected_platforms',
                    'populate': {
                        'path': 'detected_platforms',
                        'select': 'title slug image_url'
                    }
                })
                .populate({
                    'path': 'collection_id',
                    'select': 'title slug'
                }).populate({
                    'path': 'created_by_user',
                    'select': 'first_name last_name username email avatar biography'
                }).populate({
                    'path': 'languages',
                    'select': 'title slug image_url'
                }).lean();
            // Check if componentData exists and has platform_data with at least one entry
            if (componentObj?.platform_data?.length > 0) {
                // Iterate over each platform object in platform_data
                for (const platform of componentObj.platform_data) {
                    // If the platform contains a repository_id, update its updated_at property
                    if (platform.repository_id) {
                        try {
                            // Fetch the last commit date for the specified branch and assign it to updated_at
                            platform.repository_id.updated_at = await fetchLastCommitDateOnBranch(
                                platform.repository_id.project_id,
                                gitlabProductionBranch
                            );
                        } catch (error) {
                            logger.error(`Error from gitlab while fetching project details from service file ${error}`);
                        }
                    }
                }
            }
            const is_author = (componentObj?.created_by_user?._id.toString() == user_id) ? true : false;
            // Check for community_variations counts
            const communityVariations = await Components.countDocuments({
                component_type: componentType.ELEMENTS,
                component_state: componentState.PUBLISHED,
                variant_id: (componentObj.variant_id) ? componentObj.variant_id : componentObj._id
            }).lean();

            componentObj['community_variations'] = communityVariations;
            componentObj['license_id'] = await prepareLicenseDataForPublished(componentObj._id);
            // Check free component is viewed by current user or not and send flag accordingly 
            const componentViewed = await ComponentViews.findOne({ component_id: componentObj._id, viewed_by: user_id }).lean();
            return {
                ...componentObj,
                is_allowed_to_view: (componentViewed) ? true : false,
                is_author
            };
        }
    } catch (err) {
        logger.error(`Error at Component Service fetchFullComponentData ${err}`);
        throw err;
    }
};

const fetchComponentStatistics = async (componentId) => {
    try {
        const stats = await ComponentStatistics.findOne({ component_id: componentId }).lean() || {};
        return {
            likes: (stats && stats.likes) ? stats.likes.total_likes : 0,
            views: (stats && stats.views) ? stats.views.total_views : 0,
            bookmarks: (stats && stats.bookmarks) ? stats.bookmarks.total_bookmarks : 0
        };
    } catch (err) {
        logger.error(`Error at Component Service fetchComponentStatistics${err}`);
        throw err;
    }
};

// Functions to get aspect ratio
const getImageDimensions = async (filePath) => {
    try {
        const dimensions = sizeOf(filePath);
        return dimensions;
    } catch (err) {
        console.error('Error getting image aspect ratio:', err);
        throw err;
    }
};

const getVideoDimensions = (filePath) => {
    return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(filePath, (err, metadata) => {
            if (err) {
                reject(err);
            } else {
                const { width, height } = metadata.streams[0];
                const dimensions = { width: width, height: height };
                resolve(dimensions);
            }
        });
    });
};

// Function to determine the aspect ratio based on file URL
const getComponentOrientation = async (documentPath, type) => {
    try {
        if (type == 'image') {
            const imageDimensions = await getImageDimensions(documentPath.data);
            const orientation = await determineMediaOrientation(imageDimensions.width, imageDimensions.height);
            return orientation;
        } else if (type == 'video') {
            const fileUrl = `${process.env.AWS_FILE_URL}${documentPath}`;
            const videoDimensions = await getVideoDimensions(fileUrl);
            const orientation = await determineMediaOrientation(videoDimensions.width, videoDimensions.height);
            return orientation;
        } else {
            return componentOrientation.DEFAULT;
        }
    } catch (err) {
        console.error('Error determining aspect ratio:', err);
    }
};

const determineMediaOrientation = async (width, height) => {
    console.log('Function calling for determine orientation', width, height);
    // If the height is 20% larger than the width, it's a portrait
    if (height > width * 1.2) {
        return componentOrientation.PORTRAIT;
    }
    // If the width is larger than the height, it's a landscape
    else if (width > height) {
        return componentOrientation.LANDSCAPE;
    }
    // If neither condition is met, return a message
    else {
        return componentOrientation.DEFAULT;
    }
};

const checkAndManageComponentState = async (componentId, postData) => {
    try {
        // Retrieve the current component document based on its ID, returning only the fields that are necessary for comparison
        const currentComponentData = await DraftComponents.findOne({ _id: componentId }).lean().select(Object.keys(postData).join(' '));

        // If no document is found, return false to indicate no changes can be made
        if (!currentComponentData) return false;

        // Compare the current component data with the new data
        return compareJSONWithStringify(currentComponentData, postData);

    } catch (err) {
        // Log any errors encountered during the process
        logger.error(`Error in checkAndManageComponentState: ${err.message}`);
        throw err; // Re-throw the error for higher-level handling
    }
};

const injectAssetsIntoHTML = async (html, cssLink = false, jsLink = false) => {

    // Regex patterns to identify <head>, </head>, <body>, and </body> tags

    const closingHeadTag = /<\/head>/i;

    // Initialize result as the original HTML string
    let result = html;

    const cssLinkTag = cssLink != false ? `<link rel="stylesheet" href="${elementFileNames.CSS}">` : '';
    const scriptTag = jsLink != false ? `<script src="${elementFileNames.JAVASCRIPT}"></script>` : '';

    // If <head> exists, inject the CSS link before </head>
    if (closingHeadTag.test(html)) {
        result = result.replace(closingHeadTag, `${cssLinkTag}${scriptTag}</head>`);
    } else {
        // If <head> doesn't exist, add it after <html>
        const htmlTag = /<html>/i;
        if (htmlTag.test(html)) {
            result = result.replace(htmlTag, `<html><head>${cssLinkTag}${scriptTag}</head>`);
        } else {
            // If <html> also doesn't exist, wrap the content
            result = `<html><head>${cssLinkTag}${scriptTag}</head><body>${result}</body></html>`;
        }
    }

    return result;
};

const prepareLicenseDataForPublished = async (componentId) => {
    try {
        const component = await Components.findOne({
            _id: componentId
        }, 'is_paid license_id'
        ).populate({
            path: 'created_by_user',
            select: 'first_name last_name username email avatar'
        }).lean();

        const currentYear = new Date().getFullYear();
        const copyrightHolder = `${component?.created_by_user?.first_name || ''} ${component?.created_by_user?.last_name || ''}`.trim();

        const replacePlaceholders = (content) =>
            content
                .replace(/{{year}}/g, currentYear)
                .replace(/{{copyright_holder}}/g, copyrightHolder);

        const licenseQuery = component.is_paid && component.license_id
            ? { _id: component.license_id }
            : { is_default: true };

        const license = await PlatformLicense.findOne(licenseQuery, 'title slug description').lean();

        if (license) {
            license.description = replacePlaceholders(license.description);
        }

        return license;
    } catch (error) {
        logger.error(`Error in prepareLicenseDataForPublished: ${error.message}`);
        throw error;
    }
};

const updateComponentDownloadCount = async (componentId) => {
    try {
        await Components.updateOne({
            _id: componentId
        }, {
            $inc: {
                downloads: 1
            }
        });
    } catch (error) {
        logger.error(`Failed to update download count for component ID ${componentId}: ${error.message}`);
        throw new Error(`Could not increase download count: ${error.message}`);
    }
};

const fetchDefaultLicense = async () => {
    try {
        return await PlatformLicense.findOne({ is_default: true }).lean();
    } catch (error) {
        logger.error(`Failed to fetch default license: ${error.message}`);
        throw new Error(`Unable to fetch default license: ${error.message}`);
    }
};

const fetchUserComponentStatistics = async (user_id) => {
    try {

        const [draftCount, publishedCount, privateCount] = await Promise.all([
            DraftComponents.countDocuments({ created_by_user: user_id, component_state: componentState.ACTIVE_DRAFT }),
            Components.countDocuments({ created_by_user: user_id, component_state: componentState.PUBLISHED }),
            Components.countDocuments({ created_by_user: user_id, component_state: componentState.PRIVATE })
        ]);

        return {
            draft: draftCount,
            published: publishedCount,
            private: privateCount
        };
    } catch (error) {
        logger.error(`Error in fetchUserComponentStatistics: ${error.message}`);
        throw error;
    }
};

const createComponentForPublicCodeSpace = async (repositoryId) => {
    try {
        // Fetch the repository details from the database
        const repository = await GitlabRepository.findOne({ _id: repositoryId }).lean();

        if (!repository) {
            throw new Error(`Repository not found with ID: ${repositoryId}`);
        }

        const baseSlug = generateSlug(repository.project_name); // Your own slugify logic
        const uniqueSlug = await generateUniqueSlug(baseSlug);

        const componentObj = {
            title: repository.project_name,
            slug: uniqueSlug,
            short_description: repository.description,
            created_by_user: repository.user_id,
            updated_by_user: repository.user_id,
            component_type: componentType.CODESPACE,
            component_state: componentState.PUBLISHED,
            last_published_at: new Date(),
            detected_technologies: repository.detected_platforms,
            public_repository_id: repository._id
        };

        // Perform upsert operation based on public_repository_id
        const component = await Components.findOneAndUpdate(
            { public_repository_id: repository._id }, // Filter condition
            { $set: componentObj }, // Update or set new values
            { upsert: true, new: true } // Create if not exists, return updated doc
        );

        // Update the GitLab repository with the new component ID
        await GitlabRepository.updateOne(
            { _id: repository._id },
            { $set: { component_id: component._id } }
        );

        console.log(`Component entry created for public code-space => ${repository._id}`);
    } catch (error) {
        // Log and rethrow the error
        logger.error(`Error in createComponentForPublicCodeSpace: ${error.message}`);
        throw error;
    }
};

function generateSlugWithNanoid(baseSlug) {
    // Append a short unique ID to avoid slug collisions
    return `${baseSlug}-${nanoid(6)}`; // You can increase/decrease the length
}

async function generateUniqueSlug(baseSlug) {
    let slug = baseSlug;

    // First try the baseSlug as-is
    if (!(await Components.exists({ slug }))) {
        return slug;
    }

    // Otherwise, try adding nanoid until unique
    do {
        slug = generateSlugWithNanoid(baseSlug);
    } while (await Components.exists({ slug }));

    return slug;
}

module.exports = {
    checkAndManageComponentVersion,
    fetchComponentStatistics,
    fetchFullComponentData,
    getComponentOrientation,
    determineMediaOrientation,
    checkAndManageComponentState,
    injectAssetsIntoHTML,
    prepareLicenseDataForPublished,
    updateComponentDownloadCount,
    fetchDefaultLicense,
    fetchUserComponentStatistics,
    createComponentForPublicCodeSpace
};