const { addCollaborator, removeCollaborator, sendInvite, verifyInvite } = require('../../../../validations/front/collaborator/collaboratorValidation');

class CollaboratorValidationMiddleware {
    addCollaboratorValidation(req, res, next) {
        const { value, error } = addCollaborator(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    removeCollaboratorValidation(req, res, next) {
        const { value, error } = removeCollaborator(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
    
    sendInviteValidation(req, res, next) {
        const { value, error } = sendInvite(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    verifyInviteValidation(req, res, next) {
        const { value, error } = verifyInvite(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new CollaboratorValidationMiddleware();