const constants = require('../../config/constants');
const { ReS, sendError } = require('../../services/general.helper');
const Admins = require('../../models/admins.model').Admins;
const forge = require('node-forge');
const Jwt = require('jsonwebtoken');
const { getAdminDetails } = require('../../services/admin.service');
const logger = require('../../config/logger');

async function adminLogin(req, res) {
    try {
        const { email, password } = req.body;

        const hashPassword = await forge.md.sha512.create().update(password).digest().toHex();
        // Set default conditions
        const conditions = {
            'email': email,
            'password': hashPassword
        };
        const adminUser = await Admins.findOne(conditions, {
            password: 0,
            created_at: 0,
            updated_at: 0,
            is_active: 0,
            __v: 0
        }).lean();

        if (adminUser === null) {
            return ReS(res, constants.unauthorized_code, 'Oops! Login Failed.');
        }

        if (adminUser.is_active === false) {
            return ReS(res, constants.unauthorized_code, 'Oops! Your account has been disabled');
        }

        let userPermissionGroup = {};
        if (adminUser.is_super_admin) {
            userPermissionGroup = { allowed: '*' };
        } else {
            userPermissionGroup = await getAdminDetails(adminUser._id);
        }
        const token = Jwt.sign({ data: adminUser }, process.env.JWT_CMS_SECRET_KEY, { expiresIn: process.env.JWT_CMS_EXPIRATION });
        adminUser['token'] = token;
        adminUser['roles'] = userPermissionGroup;
        return ReS(res, constants.success_code, 'Login Success', adminUser);
    } catch (err) {
        logger.error(`Error at CMS Controller deleteTool${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    adminLogin
};