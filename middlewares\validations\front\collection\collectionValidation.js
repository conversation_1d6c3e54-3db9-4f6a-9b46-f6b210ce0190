const { createOrEditCollection, addCollectionItems } = require('../../../../validations/front/collection/collectionValidation');

class CollectionValidationMiddleware {
    createOrEditCollectionValidation(req, res, next) {
        const { value, error } = createOrEditCollection(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    addCollectionItemsValidation(req, res, next) {
        const { value, error } = addCollectionItems(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new CollectionValidationMiddleware();