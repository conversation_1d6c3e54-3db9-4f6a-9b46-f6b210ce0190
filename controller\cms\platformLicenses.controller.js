// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, sendError, escapeRegex } = require('../../services/general.helper');
const createSlug = require('../../services/slug.service');

// Models declaration
const PlatformLicense = require('../../models/platform_licenses.model').PlatformLicense;

async function createLicense(req, res) {
    try {
        const {
            title,
            category,
            description,
            dynamic_values,
            license_type
        } = req.body;


        const slug = createSlug(title);
        // Check if the license with provided slug already exists
        const license = await PlatformLicense.findOne({ slug }).lean();

        if (license) {
            // If slug already exists, return conflict status with user details
            return ReS(res, constants.accepted_code, 'Oops! License already registered', {
                license_id: license._id, slug: license.slug
            });
        }

        await PlatformLicense.create({
            title: title,
            slug: slug,
            category: category,
            description: description,
            license_type: license_type,
            dynamic_values: dynamic_values
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createLicense ${err}`);
        return sendError(res, err);
    }
}

async function getLicense(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };

        const license = await PlatformLicense.findOne(filter).lean();

        if (license == null) {
            return ReS(res, constants.resource_not_found, 'Oops! License Not Found.');
        }

        return ReS(res, constants.success_code, 'Data Fetched', license);
    } catch (err) {
        logger.error(`Error at CMS Controller getLicense ${err}`);
        return sendError(res, err);
    }
}

async function getLicenseList(req, res) {
    try {
        // Get total count of documents
        const totalDocuments = await PlatformLicense.countDocuments();
        // Set default conditions
        const conditions = {};
        // Set default sort
        const sort = {
            'created_at': -1
        };

        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.body.license_type) {
            conditions['license_type'] = req.body.license_type;
        }

        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;

        const filterDocuments = await PlatformLicense.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                slug: 1,
                description: 1,
                category: 1,
                created_at: 1,
                license_type: 1,
                is_active: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });
        // Query for a license of data
        const licenseList = await PlatformLicense.aggregate(query);

        return ReS(res, constants.success_code, 'Data Fetched', {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: licenseList
        });
    } catch (err) {
        logger.error(`Error at CMS Controller getLicenseList ${err}`);
        return sendError(res, err);
    }
}

async function updateLicense(req, res) {
    try {
        const { title, description, category, dynamic_values, license_type } = req.body;

        const slug = createSlug(title);

        const filter = {
            _id: req.params.id
        };

        const license = await PlatformLicense.findOne(filter).lean();

        if (license == null) {
            return ReS(res, constants.resource_not_found, 'Oops! License Not Found.');
        }

        // Check if the license with provided slug already exists
        const foundLicense = await PlatformLicense.findOne({
            slug: slug,
            _id: { $ne: req.params.id }
        }).lean();

        if (foundLicense) {
            // If slug already exists, return conflict status with user details
            return ReS(res, constants.accepted_code, 'Oops! License already registered', {
                license_id: foundLicense._id, slug: foundLicense.slug
            });
        }

        await PlatformLicense.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                title: title,
                description: description,
                category: category,
                dynamic_values: dynamic_values,
                license_type: license_type,
                slug: slug
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateLicense ${err}`);
        return sendError(res, err);
    }
}

async function updateLicenseStatus(req, res) {
    try {
        const { is_active } = req.body;


        const filter = {
            _id: req.params.id
        };

        const license = await PlatformLicense.findOne(filter).lean();

        if (license == null) {
            return ReS(res, constants.resource_not_found, 'Oops! License Not Found.');
        }

        await PlatformLicense.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active
            }
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateLicenseStatus ${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createLicense,
    getLicense,
    getLicenseList,
    updateLicense,
    updateLicenseStatus
};