const { createMergeRequest, addCommentToMergeRequest } = require('../../../../validations/front/merge_request/mergeRequestValidation');

class MergeRequestValidationMiddleware {
    createMergeRequestValidation(req, res, next) {
        const { value, error } = createMergeRequest(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    addCommentToMergeRequestValidation(req, res, next) {
        const { value, error } = addCommentToMergeRequest(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

}
module.exports = new MergeRequestValidationMiddleware();