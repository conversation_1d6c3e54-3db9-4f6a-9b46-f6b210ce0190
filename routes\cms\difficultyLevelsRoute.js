const express = require('express');
const router = express.Router();

const { createDifficultyLevelValidation, updateDifficultyLevelValidation } = require('../../middlewares/validations/cms/difficulty_levels/difficultyLevelsValidation');
const { createDifficultyLevel, updateDifficultyLevel, getAllDifficultyLevel, getAllDifficultyLevelSortList, getDifficultyLevelDetails, updateDifficultyLevelStatus } = require('../../controller/cms/difficultyLevels.controller');


router.post('/create', createDifficultyLevelValidation, createDifficultyLevel);
router.put('/update/:id', updateDifficultyLevelValidation, updateDifficultyLevel);
router.put('/update/status/:id', updateDifficultyLevelStatus);
router.get('/sort-list', getAllDifficultyLevelSortList);
router.post('/list', getAllDifficultyLevel);
router.get('/details/:id', getDifficultyLevelDetails);

module.exports = router;