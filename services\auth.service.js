// Model declaration
const UserOTPs = require('../models/user_otps.model').UserOTPs;

// Services declaration
const { generateOTP } = require('./general.helper');
const { sendUserVerificationEmail, sendUserLoginOtpEmail, sendUpdateEmailOtp } = require('../services/send_email.service');

const { userActions } = require('../config/component.constant');
const logger = require('../config/logger');

const saveAndSendSignupOTP = async (email, first_name) => {
    try {
        const otp = generateOTP(6);
        await UserOTPs.create({
            action: userActions.SIGNUP,
            email: email,
            otp: otp
        });
        await sendUserVerificationEmail(email, otp, first_name);
    } catch (err) {
        logger.error(`Error at Component Service saveAndSendSignupOTP ${err}`);
    }
};

const saveAndSendLoginOTP = async (user_id, email, first_name) => {
    try {
        const otp = generateOTP(6);
        await UserOTPs.create({
            user_id: user_id,
            email: email,
            otp: otp,
            action: userActions.LOGIN
        });
        await sendUserLoginOtpEmail(email, otp, first_name);
    } catch (err) {
        logger.error(`Error at Component Service saveAndSendLoginOTP ${err}`);
    }
};

const saveAndSendUpdateEmailOTP = async (user_id, email, username) => {
    try {
        const otp = generateOTP(6);
        await UserOTPs.create({
            user_id: user_id,
            email: email,
            otp: otp,
            action: userActions.UPDATE_EMAIL
        });
        await sendUpdateEmailOtp(email, otp, username);
    } catch (err) {
        logger.error(`Error at Component Service saveAndSendUpdateEmailOTP ${err}`);
    }
};

module.exports = {
    saveAndSendSignupOTP,
    saveAndSendLoginOTP,
    saveAndSendUpdateEmailOTP
};