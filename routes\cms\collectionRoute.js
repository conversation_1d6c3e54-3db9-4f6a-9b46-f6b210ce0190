const express = require('express');
const router = express.Router();

const { createCollection, updateCollection, sortListCollectionAlphabetically } = require('../../controller/cms/collection.controller');

const { createOrEditCollectionValidation } = require('../../middlewares/validations/cms/collection/collectionValidation');

router.post('/', createOrEditCollectionValidation, createCollection);
router.put('/:id', createOrEditCollectionValidation, updateCollection);
router.get('/sort-list', sortListCollectionAlphabetically);


module.exports = router;