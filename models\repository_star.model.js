const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const logger = require('../config/logger');

const GitlabRepository = require('./gitlab_repository.model').GitlabRepository;
const Users = require('./users.model').Users;

const repositoryStarSchema = new Schema({
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    repository_id: {
        type: mongoose.Types.ObjectId,
        ref: 'gitlab_repository'
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

// Define a post-save hook for the repositoryStarSchema
repositoryStarSchema.post('save', async function () {
    try {
        // Capture the reference to the current context
        const currentContext = this;

        // Check if the context and repository_id exist
        if (currentContext && currentContext.repository_id) {
            // Update stars in repository document as well
            await GitlabRepository.updateOne({
                _id: currentContext.repository_id
            }, {
                $inc: {
                    stars: 1
                }
            });
        }
        if (currentContext && currentContext.user_id) {
            await Users.updateOne({
                _id: currentContext.user_id
            }, {
                $inc: {
                    total_stars: 1
                }
            });
        }
    } catch (error) {
        // Log any errors that occur during the operation
        logger.error(`Error occurred in the post-save hook of repositoryStarSchema:${error}`);
    }
});

// Define a post-deleteOne hook for the repositoryStarSchema
repositoryStarSchema.post('findOneAndDelete', async function (doc) {
    try {
        // Access the deleted document from the 'doc' parameter
        const deletedDocument = doc;
        // Check if the context and repository_id exist
        if (deletedDocument && deletedDocument.repository_id) {
            // Update stars in repository document as well
            await GitlabRepository.updateOne({
                _id: deletedDocument.repository_id
            }, {
                $inc: {
                    stars: -1
                }
            });
        }
        if (deletedDocument && deletedDocument.user_id) {
            await Users.updateOne({
                _id: deletedDocument.user_id
            }, {
                $inc: {
                    total_stars: -1
                }
            });
        }
    } catch (error) {
        // Log any errors that occur during the operation
        logger.error(`Error occurred in the post-deleteOne hook of repositoryStarSchema:${error}`);
    }
});


const RepositoryStars = mongoose.model('repository_stars', repositoryStarSchema);

module.exports = {
    RepositoryStars
};