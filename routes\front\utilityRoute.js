const express = require('express');
const router = express.Router();

const { getAllPlatformSortList, getAllCategorySortList, getAllTagsSortList, getAllDifficultyLevelSortList, uploadFile, uploadFileBase64, getAllSectionsSortList, getAllLicenseSortList, getAllRepositoryAccessList, getUserSuggestions, takeAndUploadScreenshot, convertHTMLToGIF, convertHTMLToGIFWithInteraction } = require('../../controller/front/utility.controller');

router.post('/upload-file', uploadFile);
router.post('/upload-file/base64', uploadFileBase64);

router.get('/platform/sort-list', getAllPlatformSortList);
router.get('/category/sort-list', getAllCategorySortList);
router.get('/tag/sort-list', getAllTagsSortList);
router.get('/difficulty-level/sort-list', getAllDifficultyLevelSortList);
router.get('/sections/sort-list', getAllSectionsSortList);
router.get('/licenses/sort-list', getAllLicenseSortList);
router.get('/collaborator/access/sort-list', getAllRepositoryAccessList);
router.get('/users/suggestions', getUserSuggestions);
router.post('/puppeteer/screenshot', takeAndUploadScreenshot);
router.post('/puppeteer/gif', convertHTMLToGIF);
router.post('/puppeteer/interaction/gif', convertHTMLToGIFWithInteraction);

module.exports = router;