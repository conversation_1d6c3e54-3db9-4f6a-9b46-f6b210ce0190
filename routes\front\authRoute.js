const express = require('express');
const router = express.Router();
const rateLimit = require('express-rate-limit');
const RequestIp = require('@supercharge/request-ip');
const { IPlimitReached } = require('../../services/utilService');

const oneHour = 60;
const oneSec = 1000;
const lowApiLimitSettings = {
    windowMs: 15 * oneHour * oneSec, // 15 minutes in milliseconds
    max: 10,
    headers: false,
    handler: IPlimitReached,
    keyGenerator: (req) => {
        return RequestIp.getClientIp(req);
    }
};

const { loginWithEmail, signUpWithEmail, verifyEmailOTP, checkUsernameAvailability, checkEmailAvailability, resendOTPEmail, signUpWithGoogle, guestLogin, verifyLoginOTP, resendLoginOTP } = require('../../controller/front/auth.controller');

const { loginWithEmailValidation, signUpWithEmailValidation, verifyEmailOtpValidation, checkUsernameAvailabilityValidation, checkEmailAvailabilityValidation, signUpWithSocialValidation, guestLoginValidation, verifyLoginOtpValidation, resendSignUpOtpValidation } = require('../../middlewares/validations/front/auth/authValidation');

const loginWithPhoneAPILimiter = rateLimit({ ...lowApiLimitSettings, max: 50 });

router.post('/signup-with-email', loginWithPhoneAPILimiter, signUpWithEmailValidation, signUpWithEmail);

router.post('/login-with-email', loginWithPhoneAPILimiter, loginWithEmailValidation, loginWithEmail);

router.post('/verify-login-otp/:user_id', loginWithPhoneAPILimiter, verifyLoginOtpValidation, verifyLoginOTP);

router.post('/verify-otp/:user_id', loginWithPhoneAPILimiter, verifyEmailOtpValidation, verifyEmailOTP);

router.post('/check-username-availability', loginWithPhoneAPILimiter, checkUsernameAvailabilityValidation, checkUsernameAvailability);

router.post('/check-email-availability', loginWithPhoneAPILimiter, checkEmailAvailabilityValidation, checkEmailAvailability);

router.post('/resend/otp', loginWithPhoneAPILimiter, resendSignUpOtpValidation, resendOTPEmail);

router.post('/signup-with-social', loginWithPhoneAPILimiter, signUpWithSocialValidation, signUpWithGoogle);

router.post('/guest-login', loginWithPhoneAPILimiter, guestLoginValidation, guestLogin);

router.post('/resend/login-otp/:user_id', loginWithPhoneAPILimiter, resendLoginOTP);

module.exports = router;