const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const AdminMpnPointWalletSchema = new Schema({
    points: {
        type: Number,
        required: true,
        default: 0
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const AdminMpnPointWallet = mongoose.model('admin_mpn_point_wallet', AdminMpnPointWalletSchema);

module.exports = {
    AdminMpnPointWallet
};  