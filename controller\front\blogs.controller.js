// Service declaration
const constants = require('../../config/constants');
const { ReS } = require('../../services/general.helper');
const logger = require('../../config/logger');
const { getMediumBlogs, getCacheStatus } = require('../../services/blogs.service');


async function getAllMediumBlogs(req, res) {
    try {
        const profile = req.query.profile ? req.query.profile : 'mobile-innovation-network';

        logger.info(`Fetching Medium blogs for profile: ${profile}`);

        // Get blogs from cache or fetch fresh data if cache is empty
        const mediumBlogs = await getMediumBlogs(profile);

        return ReS(res, constants.success_code, 'Data Fetched', mediumBlogs);
    } catch (err) {
        logger.error(`Error at Front Controller getAllMediumBlogs: ${err.message}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getBlogsCacheStatus(req, res) {
    try {
        const profile = req.query.profile ? req.query.profile : 'mobile-innovation-network';

        logger.info(`Getting cache status for profile: ${profile}`);

        const cacheStatus = await getCacheStatus(profile);

        return ReS(res, constants.success_code, 'Cache Status Retrieved', cacheStatus);
    } catch (err) {
        logger.error(`Error at Front Controller getBlogsCacheStatus: ${err.message}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

module.exports = {
    getAllMediumBlogs,
    getBlogsCacheStatus
};