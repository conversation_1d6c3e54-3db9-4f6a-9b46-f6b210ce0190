const { createUserRole, updateUserRole, updateUserRoleStatus } = require('../../../../validations/cms/user_roles/userRolesValidation');

class UserRoleValidationMiddleware {
    createUserRoleValidation(req, res, next) {
        const { value, error } = createUserRole(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateUserRoleValidation(req, res, next) {
        const { value, error } = updateUserRole(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateUserRoleStatusValidation(req, res, next) {
        const { value, error } = updateUserRoleStatus(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new UserRoleValidationMiddleware();