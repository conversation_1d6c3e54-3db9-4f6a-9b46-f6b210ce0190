const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const AdminsSchema = new Schema({
    email: {
        type: String,
        require: true
    },
    password: {
        type: String,
        require: true
    },
    first_name: {
        type: String,
        require: true
    },
    last_name: {
        type: String
    },
    is_active: {
        type: Boolean,
        default: true
    },
    is_super_admin: {
        type: Boolean,
        default: false
    },
    roles: [{
        type: mongoose.Types.ObjectId,
        ref: 'admin_roles'
    }],
    gitlab_user_exists: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const Admins = mongoose.model('admins', AdminsSchema);

module.exports = {
    Admins
};