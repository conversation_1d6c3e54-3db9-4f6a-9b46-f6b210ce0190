const Joi = require('joi');

class UserValidation {
    updateUserAuthorFees(params) {
        const schema = Joi.object({
            is_author_fee_overridden: Joi.boolean().required(),
            overridden_author_fee: Joi.number().min(0).required(),
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updateUserBuyerFees(params) {
        const schema = Joi.object({
            is_buyer_fee_overridden: Joi.boolean().required(),
            overridden_buyer_fee: Joi.number().min(0).required(),
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new UserValidation();