const express = require('express');
const router = express.Router();

const {
    followCreator,
    unfollowCreator,
    getFollowers,
    getFollowing,
    checkFollowStatus,
    getFollowersCount,
    getFollowingCount
} = require('../../controller/front/creatorFollow.controller');

// Follow/unfollow actions
router.post('/:username/follow', followCreator);
router.delete('/:username/unfollow', unfollowCreator);

// Get followers/following details
router.post('/me/followers/list', getFollowers);
router.post('/me/following/list', getFollowing);

// Get followers/following counts
router.get('/me/followers/count', getFollowersCount);
router.get('/me/following/count', getFollowingCount);

// Check if user is following a creator
router.get('/:username/follow-status', checkFollowStatus);

module.exports = router;
