const constants = require('../../config/constants');
const logger = require('../../config/logger');

const { ReS, generateSlug, escapeRegex, sendError } = require('../../services/general.helper');

const DifficultyLevels = require('../../models/difficulty_levels.model').DifficultyLevels;
const Settings = require('../../models/settings.model').Settings;


async function createDifficultyLevel(req, res) {
    try {
        const {
            title,
            description,
            is_active
        } = req.body;

        const levelSlug = generateSlug(title);

        await DifficultyLevels.create({
            title: title,
            slug: levelSlug,
            description: description,
            is_active: is_active,
            created_by: req.session._id
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createDifficultyLevel${err}`);
        return sendError(res, err);
    }
}

async function updateDifficultyLevel(req, res) {
    try {

        const postData = req.body;

        const difficultyLevelData = await DifficultyLevels.findOne({
            _id: req.params.id
        });

        if (difficultyLevelData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Level Not Found.');
        }

        if (postData.title) {
            postData['slug'] = generateSlug(postData.title);
        }

        postData['updated_by'] = req.session._id;

        await DifficultyLevels.updateOne({
            _id: req.params.id
        }, {
            '$set': postData
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateDifficultyLevel${err}`);
        return sendError(res, err);
    }
}

async function getAllDifficultyLevel(req, res) {
    try {
        const totalDocuments = await DifficultyLevels.countDocuments();
        // Set default conditions
        const conditions = {};
        // Set default sort
        const sort = {
            'created_at': -1
        };
        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        const filterDocuments = await DifficultyLevels.countDocuments(conditions);
        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                slug: 1,
                description: 1,
                created_at: 1,
                is_active: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });
        const difficultyLevelList = await DifficultyLevels.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: difficultyLevelList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllDifficultyLevel${err}`);
        return sendError(res, err);
    }
}

async function getDifficultyLevelDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            '_id': req.params.id
        };
        const difficultyLevelData = await DifficultyLevels.findOne(conditions).lean();
        if (!difficultyLevelData) {
            return ReS(res, constants.resource_not_found, 'Oops! Level Not Found.');
        }
        return ReS(res, constants.success_code, 'Data Fetched', difficultyLevelData);
    } catch (err) {
        logger.error(`Error at CMS Controller getDifficultyLevelDetails${err}`);
        return sendError(res, err);
    }
}

async function getAllDifficultyLevelSortList(req, res) {
    try {
        // Set default conditions
        const conditions = {
            setting_slug: 'difficulty_levels'
        };
        // Set default sort
        const sort = {
            'sort_order': 1
        };
        const query = [{
            $match: conditions
        }, {
            $unwind: '$values'
        }, {
            $replaceRoot: {
                newRoot: '$values'
            }
        }];
        query.push({
            '$sort': sort
        });
        const difficultyLevelList = await Settings.aggregate(query);
        return ReS(res, constants.success_code, 'Data Fetched', difficultyLevelList);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllDifficultyLevelSortList${err}`);
        return sendError(res, err);
    }
}

async function updateDifficultyLevelStatus(req, res) {
    try {

        const { is_active } = req.body;

        const difficultyLevelData = await DifficultyLevels.findOne({
            _id: req.params.id
        });

        if (difficultyLevelData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Level Not Found.');
        }

        await DifficultyLevels.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateDifficultyLevelStatus${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createDifficultyLevel,
    updateDifficultyLevel,
    getAllDifficultyLevel,
    getDifficultyLevelDetails,
    getAllDifficultyLevelSortList,
    updateDifficultyLevelStatus
};