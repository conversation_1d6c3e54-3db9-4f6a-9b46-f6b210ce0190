// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS } = require('../../services/general.helper');

// Models declaration
const InterestedUser = require('../../models/interested_users.model').InterestedUser;


// Function to add an interested user to the database
async function addInterestedUser(req, res) {
    try {
        // Extract email from request body
        const { email } = req.body;

        // Check if user already exists in the database
        const userData = await InterestedUser.findOne({ email });

        // If user already exists, return a message indicating their interest is noted
        if (userData !== null) {
            return ReS(res, constants.bad_request_code, 'We have noted your interest and will update you soon :)');
        }

        // Create a new record for the interested user
        await InterestedUser.create({ email });

        // Return success message
        return ReS(res, constants.success_code, 'We have noted your interest and will update you soon :)');
    } catch (err) {
        // Log any errors that occur and return a server error message
        logger.error(`Error at Front Controller addInterestedUser${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

// Function to get the count of interested users from the database
async function getInterestedUsers(req, res) {
    try {
        // Fetch the count of interested users from the database
        const interestedUsersCount = await InterestedUser.countDocuments().lean();

        // If count is retrieved successfully, return success message along with the count
        return ReS(res, constants.success_code, 'Data Fetched', interestedUsersCount);
    } catch (err) {
        // Log any errors that occur and return a server error message
        logger.error(`Error at Front Controller getInterestedUsers${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}


module.exports = {
    addInterestedUser,
    getInterestedUsers
};