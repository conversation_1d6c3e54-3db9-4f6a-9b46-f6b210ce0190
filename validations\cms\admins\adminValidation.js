const Joi = require('joi');
class AdminValidation {
    adminLogin(params) {
        const schema = Joi.object({
            email: Joi.string().email().required(),
            password: Joi.string().required()
        });
        return schema.validate(params);
    }

    createAdmin(params) {
        const schema = Joi.object({
            email: Joi.string().trim().email().required(),
            first_name: Joi.string().trim().required(),
            last_name: Joi.string().trim().required(),
            roles: Joi.array().items(Joi.string()).min(1).unique().required()
        });
        return schema.validate(params);
    }

    updateAdminStatus(params) {
        const schema = Joi.object({
            is_active: Joi.boolean().required()
        });
        return schema.validate(params);
    }

    updateAdmin(params) {
        const schema = Joi.object({
            first_name: Joi.string().trim().optional(),
            last_name: Joi.string().trim().optional(),
            email: Joi.string().trim().email().optional(),
            is_active: Joi.boolean().default(false),
            roles: Joi.array().items(Joi.string()).min(1).unique().optional()
        });
        return schema.validate(params);
    }

    changePassword(params) {
        const schema = Joi.object({
            current_password: Joi.string().trim().required(),
            new_password: Joi.string().trim().required()
        });
        return schema.validate(params);
    }

    createGitlabAccount(params) {
        const schema = Joi.object({
            username: Joi.string()
                .min(2)
                .max(255)
                .regex(/^(?!-)[a-zA-Z0-9._-]+(?<!\.(git|atom)|\.)$/)
                .invalid('admin', 'root', 'support', 'git', 'public', 'dashboard')
                .required()
        });
        return schema.validate(params);
    }
}

module.exports = new AdminValidation();