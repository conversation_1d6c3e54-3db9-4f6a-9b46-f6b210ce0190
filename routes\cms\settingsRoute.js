const express = require('express');
const router = express.Router();
const { updateSystemMaintenance, getSettingDetails } = require('../../controller/cms/settings.controller');
const { updateSystemMaintenanceValidation } = require('../../middlewares/validations/cms/settings/settingsValidation');

router.post('/update/system_maintenance', updateSystemMaintenanceValidation, updateSystemMaintenance);
router.get('/get-settings/:slug', getSettingDetails);

module.exports = router;