const puppeteer = require('puppeteer');
const sharp = require('sharp');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const os = require('os');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');

// Configs
const defaultConfig = {
    width: 360,
    height: 408,
    fps: 8,                    // FPS
    duration: 6000,            // 6 seconds
    interactionDelay: 250,
    quality: 'high',           // Maintain quality
    maxInteractions: 10,
    waitForLoad: 2000,
    maxExecutionTime: 45000,   // 45 seconds
    initialLoadTimeout: 8000,
    frameLimit: 50,            // Increased frame limit for smoother animations
    preserveAnimations: false,
    viewportScale: 2  
};

// Reusable browser instance
let sharedBrowser = null;
let browserUsageCount = 0;
const MAX_BROWSER_REUSES = 5;

// Performance logging helper
function logPerformance(name, startTime) {
    const duration = Date.now() - startTime;
    console.log(`${name} completed in ${duration}ms`);
}

// Get or create browser instance
async function getBrowser() {
    if (!sharedBrowser || browserUsageCount >= MAX_BROWSER_REUSES) {
        if (sharedBrowser) {
            await sharedBrowser.close().catch(() => { });
        }
        sharedBrowser = await launchBrowser();
        browserUsageCount = 0;
    }
    browserUsageCount++;
    return sharedBrowser;
}

// Clean up browser on process exit
process.on('exit', () => {
    if (sharedBrowser) {
        sharedBrowser.close().catch(() => { });
    }
});

async function launchBrowser() {
    console.log('Launching new browser instance...');
    return await puppeteer.launch({
        headless: 'new',
        executablePath: process.env.PUPPETEER_EXECUTABLE_PATH,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-extensions',
            '--disable-web-security',
            '--disable-features=site-per-process'
        ]
    });
}

// Setup page with optimized settings
async function setupPage(browser, options) {
    const page = await browser.newPage();

    // Apply optimized settings in parallel
    await Promise.all([
        page.setViewport({
            width: Math.round(options.width * options.viewportScale),
            height: Math.round(options.height * options.viewportScale)
        }),
        page.setRequestInterception(true),
        page.setDefaultNavigationTimeout(0),
        page.setDefaultTimeout(0),
        page.setCacheEnabled(false), // Disable cache to reduce memory usage
        // Throttle animations and timers
        page.evaluateOnNewDocument(() => {
            // Throttle requestAnimationFrame without storing original
            window.requestAnimationFrame = function (callback) {
                return setTimeout(() => callback(performance.now()), 100);
            };

            // Throttle timers for better stability
            const originalSetTimeout = window.setTimeout;
            const originalSetInterval = window.setInterval;
            window.setTimeout = function (fn, delay, ...args) {
                return originalSetTimeout(fn, Math.min(delay || 0, 1000), ...args);
            };
            window.setInterval = function (fn, delay, ...args) {
                return originalSetInterval(fn, Math.min(delay || 0, 1000), ...args);
            };
        })
    ]);

    // Efficient request filtering
    page.on('request', (request) => {
        const resourceType = request.resourceType();
        const url = request.url().toLowerCase();

        // Block non-essential resources
        if (['font', 'media', 'websocket', 'manifest', 'other'].includes(resourceType) ||
            url.includes('analytics') || url.includes('tracking') ||
            url.endsWith('.mp4') || url.endsWith('.mp3') ||
            url.endsWith('.webm') || url.endsWith('.wav')) {
            request.abort();
        } else {
            request.continue();
        }
    });

    return page;
}

// Create and manage temp directory
function createTempDir() {
    const tmpDir = path.join(os.tmpdir(), `html2gif-${uuidv4()}`);
    fs.mkdirSync(tmpDir, { recursive: true });
    return tmpDir;
}

async function cleanupTempDir(tmpDir) {
    if (tmpDir && fs.existsSync(tmpDir)) {
        try {
            fs.rmSync(tmpDir, { recursive: true, force: true });
        } catch (err) {
            console.warn('Failed to clean up temp directory:', err.message);
        }
    }
}

// Load content without navigation timeouts
async function loadContentWithoutTimeout(page, htmlContent, options) {
    // Set page content without waiting for load events
    await page.setContent(htmlContent, { waitUntil: 'domcontentloaded', timeout: 0 });

    // Start a timer that will resolve after initialLoadTimeout, but also
    // resolve early if we detect the page is ready
    const readyPromise = new Promise((resolve) => {
        // Resolve after fixed timeout
        const timeoutId = setTimeout(() => {
            console.log('Initial load timeout reached, proceeding anyway');
            resolve();
        }, options.initialLoadTimeout);

        // Try to detect when page is in a good state to proceed earlier
        const checkReadyInterval = setInterval(() => {
            page.evaluate(() => {
                // Check if document seems ready
                const hasBody = document.body !== null;
                const hasContent = document.body && document.body.children.length > 0;
                return { hasBody, hasContent };
            }).then((state) => {
                if (state.hasBody && state.hasContent) {
                    clearTimeout(timeoutId);
                    clearInterval(checkReadyInterval);
                    console.log('Content appears ready, proceeding');
                    resolve();
                }
            }).catch(() => {
                // If evaluation fails, fallback to timeout
                clearInterval(checkReadyInterval);
                console.log('Content load evaluation failed, proceeding anyway');
                resolve();
            });
        }, 500); // Check every 500ms
    });

    await readyPromise;

    // Detect WebGL content before proceeding
    const webglDetection = await page.evaluate(() => {
        // Check for canvas elements with WebGL contexts
        const canvasElements = document.querySelectorAll('canvas');
        let hasWebGLCanvas = false;

        for (const canvas of canvasElements) {
            try {
                // Try to get WebGL contexts
                const webgl1 = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                const webgl2 = canvas.getContext('webgl2');
                if (webgl1 || webgl2) {
                    hasWebGLCanvas = true;
                    break;
                }
            } catch (e) {
                // Context creation might fail, but canvas might still be intended for WebGL
            }
        }

        // Check for WebGL shader scripts
        const shaderScripts = document.querySelectorAll('script[type*="shader"], script[type*="x-shader"]');
        const hasShaderScripts = shaderScripts.length > 0;

        // Check for WebGL-specific content in script tags
        const allScripts = document.querySelectorAll('script');
        let hasWebGLCode = false;

        for (const script of allScripts) {
            const content = script.textContent || script.innerHTML || '';
            if (content.includes('getContext("webgl') ||
                content.includes('getContext(\'webgl') ||
                content.includes('getContext("experimental-webgl') ||
                content.includes('getContext(\'experimental-webgl') ||
                content.includes('gl.') ||
                content.includes('WebGLRenderingContext') ||
                content.includes('WebGL2RenderingContext') ||
                content.includes('THREE.') ||
                content.includes('BABYLON.') ||
                content.includes('createShader') ||
                content.includes('shaderSource') ||
                content.includes('compileShader') ||
                content.includes('createProgram') ||
                content.includes('linkProgram') ||
                content.includes('useProgram') ||
                content.includes('uniform') ||
                content.includes('attribute') ||
                content.includes('varying') ||
                content.includes('gl_Position') ||
                content.includes('gl_FragCoord') ||
                content.includes('gl_FragColor')) {
                hasWebGLCode = true;
                break;
            }
        }

        // Check for WebGL-specific CSS or inline styles (more specific patterns)
        const hasWebGLStyles = document.querySelector('canvas[style*="--canvas-z-index"]') !== null ||
                              document.querySelector('canvas[id*="webgl"], canvas[id*="gl"], canvas[class*="webgl"], canvas[class*="gl"]') !== null;

        // Check for shader-specific content in any script
        let hasShaderContent = false;
        for (const script of allScripts) {
            const content = script.textContent || script.innerHTML || '';
            if (content.includes('#version') ||
                content.includes('precision') ||
                content.includes('highp') ||
                content.includes('mediump') ||
                content.includes('lowp') ||
                content.includes('vec2') ||
                content.includes('vec3') ||
                content.includes('vec4') ||
                content.includes('mat2') ||
                content.includes('mat3') ||
                content.includes('mat4') ||
                content.includes('texture2D') ||
                content.includes('texture') ||
                content.includes('sampler2D') ||
                content.includes('samplerCube') ||
                content.includes('discard') ||
                content.includes('mix(') ||
                content.includes('dot(') ||
                content.includes('cross(') ||
                content.includes('normalize(') ||
                content.includes('reflect(') ||
                content.includes('refract(')) {
                hasShaderContent = true;
                break;
            }
        }

        // Check for 3D graphics libraries in script sources
        const scriptSrcs = Array.from(document.querySelectorAll('script[src]')).map(s => s.src.toLowerCase());
        const has3DLibraries = scriptSrcs.some(src =>
            src.includes('three') ||
            src.includes('babylon') ||
            src.includes('webgl') ||
            src.includes('gl-matrix') ||
            src.includes('pixi') ||
            src.includes('phaser') ||
            src.includes('aframe') ||
            src.includes('playcanvas') ||
            src.includes('cesium') ||
            src.includes('deck.gl')
        );

        // Check for WebGL-specific library usage (only if actually used, not just available)
        const hasWebGLLibraryUsage = typeof window.THREE !== 'undefined' ||
                                    typeof window.BABYLON !== 'undefined' ||
                                    typeof window.PIXI !== 'undefined';

        return {
            isWebGLContent: hasWebGLCanvas || hasShaderScripts || hasWebGLCode || hasWebGLStyles ||
                           hasShaderContent || has3DLibraries || hasWebGLLibraryUsage,
            details: {
                hasWebGLCanvas,
                hasShaderScripts,
                hasWebGLCode,
                hasWebGLStyles,
                hasShaderContent,
                has3DLibraries,
                hasWebGLLibraryUsage,
                canvasCount: canvasElements.length,
                shaderScriptCount: shaderScripts.length
            }
        };
    }).catch(() => ({
        isWebGLContent: false,
        details: { error: 'WebGL detection failed' }
    }));

    console.log(`WebGL Detection: ${webglDetection.isWebGLContent ? 'WebGL content detected' : 'No WebGL content'}`);
    if (webglDetection.isWebGLContent) {
        console.log('WebGL detection details:', webglDetection.details);
    }

    // Store WebGL detection result on the page object for later use
    page.webglDetection = webglDetection;

    // Automatically detect if content has animations
    const hasAnimations = await page.evaluate(() => {
        // Check for CSS animations in stylesheets and inline styles
        let hasKeyframes = false;
        let hasAnimationProperties = false;

        try {
            // Check stylesheets for keyframes and animation properties
            const sheets = document.styleSheets;
            for (let i = 0; i < sheets.length; i++) {
                try {
                    const rules = sheets[i].cssRules || sheets[i].rules;
                    for (let j = 0; j < rules.length; j++) {
                        const rule = rules[j];
                        if (rule.type === 7 || // CSSRule.KEYFRAMES_RULE
                            (rule.cssText && rule.cssText.includes('@keyframes'))) {
                            hasKeyframes = true;
                            break;
                        }
                        if (rule.cssText && (
                            rule.cssText.includes('animation') ||
                            rule.cssText.includes('transition') ||
                            rule.cssText.includes('transform')
                        )) {
                            hasAnimationProperties = true;
                        }
                    }
                    if (hasKeyframes) break;
                } catch (e) {
                    // Cross-origin sheets may throw errors
                    continue;
                }
            }
        } catch (e) { }

        // Check for animated elements and inline animations
        const animatedElements = document.querySelectorAll(
            '[style*="animation"], [style*="transition"], [class*="animate"], [class*="motion"], [class*="flip"]'
        );

        // Check for JavaScript-based animations (timers, intervals)
        const hasTimers = window.setInterval.toString().includes('[native code]') === false ||
                         window.setTimeout.toString().includes('[native code]') === false;

        return {
            hasAnimations: hasKeyframes || hasAnimationProperties || animatedElements.length > 0 || hasTimers,
            animatedElementCount: animatedElements.length,
            hasKeyframes,
            hasAnimationProperties,
            hasTimers
        };
    }).catch(() => ({ hasAnimations: false, animatedElementCount: 0, hasKeyframes: false, hasAnimationProperties: false, hasTimers: false }));

    console.log(`Content analysis: Has animations: ${hasAnimations.hasAnimations}, Keyframes: ${hasAnimations.hasKeyframes}, Animation properties: ${hasAnimations.hasAnimationProperties}, Timers: ${hasAnimations.hasTimers}, Animated elements: ${hasAnimations.animatedElementCount}`);

    // Auto-decide whether to preserve animations based on content
    const shouldPreserveAnimations = hasAnimations.hasAnimations ||
        hasAnimations.animatedElementCount > 0 ||
        options.preserveAnimations;

    // Apply the appropriate style modifications
    if (!shouldPreserveAnimations) {
        await page.evaluate(() => {
            const style = document.createElement('style');
            style.textContent = `
                * {
                    animation-duration: 0.01s !important;
                    transition-duration: 0.01s !important;
                }
            `;
            document.head.appendChild(style);

            document.querySelectorAll('video, audio').forEach((el) => {
                try { el.pause(); } catch (e) { }
            });
        }).catch(() => { });
    } else {
        // For content with animations, optimize them for GIF capture
        await page.evaluate(() => {
            const style = document.createElement('style');
            style.textContent = `
                * {
                    animation-play-state: running !important;
                    animation-timing-function: linear !important;
                    animation-fill-mode: both !important;
                }
                /* Optimize specific animation durations for better GIF capture */
                [class*="flip"], [class*="animate"] {
                    animation-duration: 1s !important;
                }
                /* Ensure transitions are visible but not too slow */
                * {
                    transition-duration: 0.3s !important;
                    transition-timing-function: ease-out !important;
                }
            `;
            document.head.appendChild(style);
        }).catch(() => { });
    }

    // Store the animation detection result on the page object for later use
    page.hasAnimatedContent = shouldPreserveAnimations;

    await page.waitForTimeout(300);
}

// Process frames in parallel batches
async function processFrame(screenshotBuffer, framePath, options) {
    return sharp(screenshotBuffer)
        .resize(options.width, options.height, {
            fit: 'fill',
            withoutEnlargement: true,
            fastShrinkOnLoad: true
        })
        .png({ quality: options.quality === 'high' ? 90 : 70, compressionLevel: 6 })
        .toFile(framePath);
}

// Process frames in batches for better performance
async function processFramesInBatches(frames, tempFilePath, options) {
    const batchSize = Math.min(5, Math.ceil(frames.length / 5)); // Process in reasonable batches
    const totalFrames = frames.length;
    let processedFrames = 0;

    for (let i = 0; i < totalFrames; i += batchSize) {
        const batch = frames.slice(i, i + batchSize);
        const batchPromises = batch.map((frame, index) => {
            const frameIndex = i + index;
            const framePath = `${tempFilePath}-${frameIndex}.png`;
            return processFrame(frame, framePath, options)
                .catch((err) => {
                    console.warn(`Error processing frame ${frameIndex}:`, err.message);
                    return null;
                });
        });

        const results = await Promise.allSettled(batchPromises);
        processedFrames += results.filter((r) => r.status === 'fulfilled' && r.value !== null).length;

        // Log progress for long batches
        if (totalFrames > 20 && i > 0 && i % 10 === 0) {
            console.log(`Processed ${i}/${totalFrames} frames (${Math.round(i / totalFrames * 100)}%)`);
        }
    }

    return processedFrames;
}

// Take screenshots with execution time limiting
async function captureFramesWithTimeLimit(page, tempFilePath, options) {
    const startTime = Date.now();
    const frames = [];
    let frameIndex = 0;
    let continueCapturing = true;

    // Set up execution time limit
    setTimeout(() => {
        continueCapturing = false;
        console.log('Reached maximum execution time, stopping frame capture');
    }, options.maxExecutionTime * 0.9); // Leave some time for processing

    // Use the auto-detected animation status
    const hasAnimatedContent = page.hasAnimatedContent || options.preserveAnimations;

    // Adjust frame capture based on content type
    const captureStrategy = hasAnimatedContent ? 'animation' : 'interaction';
    console.log(`Using ${captureStrategy} capture strategy based on content analysis`);

    // Optimized settings for different content types
    const fps = hasAnimatedContent ? Math.min(options.fps, 8) : options.fps;
    const frameLimit = hasAnimatedContent ? Math.min(options.frameLimit, 50) : Math.min(options.frameLimit, 40);

    // Capture frame function
    const captureFrame = async () => {
        if (!continueCapturing || frameIndex >= frameLimit) return false;

        try {
            const screenshotBuffer = await page.screenshot({
                type: 'png',
                omitBackground: false
            });

            frames.push(screenshotBuffer);
            frameIndex++;
            return true;
        } catch (err) {
            console.warn(`Failed to capture frame ${frameIndex}:`, err.message);
            return true; // Continue despite errors
        }
    };

    // Using a combined approach for all content types
    if (hasAnimatedContent) {
        // For animations: capture frames at regular intervals with initial stabilization
        console.log(`Capturing ${frameLimit} frames for animated content...`);

        // Allow animations to start and stabilize
        await page.waitForTimeout(500);

        // Calculate optimal frame interval for smooth animation capture
        const frameInterval = Math.max(Math.floor(options.duration / frameLimit), 100);
        console.log(`Using frame interval of ${frameInterval}ms (${fps} effective fps)`);

        // Capture initial state
        await captureFrame();
        await page.waitForTimeout(200);

        // Capture animation frames
        for (let i = 1; i < frameLimit; i++) {
            if (!continueCapturing || (Date.now() - startTime) > options.maxExecutionTime * 0.8) {
                console.log('Time limit approaching, stopping capture');
                break;
            }

            await captureFrame();

            if (i < frameLimit - 1) {
                // Use shorter intervals for better animation capture
                await page.waitForTimeout(frameInterval);
            }
        }
    } else {
        // For interactive content: use interaction-based approach
        console.log('Capturing frames with interactive elements...');

        // Take initial frames
        console.log('Capturing initial state...');
        for (let i = 0; i < 3; i++) {
            await captureFrame();
            if (!continueCapturing) break;
            await page.waitForTimeout(200);
        }

        // Get interactive elements (if time permits)
        if (continueCapturing && (Date.now() - startTime) < options.maxExecutionTime * 0.3) {
            console.log('Finding interactive elements...');
            const interactiveElements = await page.evaluate(() => {
                // Helper to check if element is visible and usable
                const isVisible = (el) => {
                    const rect = el.getBoundingClientRect();
                    return (
                        rect.width > 10 &&
                        rect.height > 10 &&
                        rect.width < window.innerWidth * 0.9 &&
                        rect.height < window.innerHeight * 0.9 &&
                        window.getComputedStyle(el).display !== 'none' &&
                        window.getComputedStyle(el).visibility !== 'hidden' &&
                        parseFloat(window.getComputedStyle(el).opacity) > 0.1
                    );
                };

                // Find elements that are likely to be interactive
                const elements = [
                    ...document.querySelectorAll('button, a, input:not([type="hidden"]), select, [role="button"]'),
                    ...document.querySelectorAll('[onclick], [class*="btn"], [class*="button"]')
                ];

                return Array.from(new Set(elements))
                    .filter(isVisible)
                    .slice(0, 10) // Limit number of elements
                    .map((el) => {
                        const rect = el.getBoundingClientRect();
                        return {
                            x: Math.floor(rect.x + (rect.width / 2)),
                            y: Math.floor(rect.y + (rect.height / 2))
                        };
                    });
            }).catch(() => []);

            // Interact with elements
            if (interactiveElements.length > 0) {
                console.log(`Found ${interactiveElements.length} interactive elements`);
                const maxInteractions = Math.min(interactiveElements.length, options.maxInteractions);

                for (let i = 0; i < maxInteractions; i++) {
                    if (!continueCapturing || (Date.now() - startTime) > options.maxExecutionTime * 0.7) {
                        console.log('Time limit approaching, stopping interactions');
                        break;
                    }

                    const element = interactiveElements[i];
                    try {
                        // Move to element
                        await page.mouse.move(element.x, element.y);
                        await captureFrame();

                        // Click
                        await page.mouse.down();
                        await captureFrame();
                        await page.mouse.up();
                        await captureFrame();

                        // Wait for any changes
                        await page.waitForTimeout(options.interactionDelay);
                        await captureFrame();

                        // Dismiss any dialogs that might appear
                        const client = await page.target().createCDPSession();
                        await client.send('Page.handleJavaScriptDialog', {
                            accept: true
                        }).catch(() => { });
                    } catch (err) {
                        // Continue with next element despite errors
                        console.warn(`Interaction error with element ${i}:`, err.message);
                    }
                }
            } else {
                // If no interactive elements, capture some simulated activity
                console.log('No interactive elements found, simulating activity...');

                // Scroll simulation
                for (let i = 0; i < 5; i++) {
                    if (!continueCapturing) break;

                    await page.evaluate(() => {
                        window.scrollBy(0, 50);
                        setTimeout(() => window.scrollBy(0, -30), 50);
                    }).catch(() => { });

                    await captureFrame();
                    await page.waitForTimeout(200);
                }
            }
        }

        // Add final frames to show end state
        for (let i = 0; i < 4; i++) {
            if (!continueCapturing) break;
            await captureFrame();
            await page.waitForTimeout(150);
        }
    }

    // Process all captured frames
    console.log(`Processing ${frames.length} captured frames...`);
    const processedCount = await processFramesInBatches(frames, tempFilePath, options);

    logPerformance('Frame capture and processing', startTime);
    return processedCount;
}

// Create GIF from frames with optimized settings
async function createOptimizedGif(tempFilePath, outputGifPath, frameCount, options) {
    const startTime = Date.now();

    // Skip palette generation for very small GIFs to save time
    const usePalette = frameCount > 5 && options.quality === 'high';

    // Calculate ideal FPS based on frame count and desired duration
    const idealFps = Math.min(
        Math.max(Math.floor(frameCount / (options.duration / 1000)), 4),
        Math.max(options.fps, 6) // Ensure minimum 6 FPS for smooth animations
    );

    console.log(`Creating GIF with ${frameCount} frames at ${idealFps} fps (palette: ${usePalette})`);

    return new Promise((resolve, reject) => {
        // Function to create GIF with or without palette
        const createGif = (usePalette) => {
            if (usePalette) {
                // Generate palette first
                const palettePath = path.join(path.dirname(tempFilePath), 'palette.png');
                return new Promise((resolve, reject) => {
                    ffmpeg()
                        .input(`${tempFilePath}-%d.png`)
                        .inputOption('-framerate', idealFps)
                        .outputOption('-vf', 'palettegen=max_colors=256:stats_mode=diff')
                        .output(palettePath)
                        .on('end', () => {
                            // Create GIF with palette
                            ffmpeg()
                                .input(`${tempFilePath}-%d.png`)
                                .inputOption('-framerate', idealFps)
                                .input(palettePath)
                                .complexFilter('paletteuse=dither=bayer:bayer_scale=5:diff_mode=rectangle')
                                .outputOption('-loop', '0')
                                .outputOption('-gifflags', '+transdiff')
                                .output(outputGifPath)
                                .on('end', () => {
                                    fs.unlink(palettePath, () => { });
                                    resolve();
                                })
                                .on('error', (err) => {
                                    fs.unlink(palettePath, () => { });
                                    reject(err);
                                })
                                .run();
                        })
                        .on('error', reject)
                        .run();
                });
            } else {
                // Create GIF directly
                return new Promise((resolve, reject) => {
                    ffmpeg()
                        .input(`${tempFilePath}-%d.png`)
                        .inputOption('-framerate', idealFps)
                        .outputOption('-loop', '0')
                        .outputOption('-gifflags', '+transdiff')
                        .output(outputGifPath)
                        .on('end', resolve)
                        .on('error', reject)
                        .run();
                });
            }
        };

        // Try with palette first, then fallback if needed
        createGif(usePalette)
            .then(() => {
                logPerformance('GIF creation', startTime);
                resolve();
            })
            .catch((err) => {
                console.warn('GIF creation with palette failed, trying without:', err.message);
                // Fallback to simpler GIF creation
                createGif(false)
                    .then(() => {
                        logPerformance('GIF creation (fallback)', startTime);
                        resolve();
                    })
                    .catch(reject);
            });
    });
}

// Unified content capturing function
async function captureContent(htmlContent, options, outputType = 'screenshot') {
    const startTime = Date.now();
    let browser = null;
    let tmpDir = null;
    let isBrowserShared = false;

    try {
        // Get browser (shared or create new)
        browser = await getBrowser();
        isBrowserShared = true;

        // Create temp directory if needed
        if (outputType !== 'screenshot') {
            tmpDir = createTempDir();
        }

        // Set up page with optimized settings
        const page = await setupPage(browser, options);

        // Load content with optimized approach
        await loadContentWithoutTimeout(page, htmlContent, options);

        // Handle based on output type
        if (outputType === 'screenshot') {
            // Take screenshot
            console.log('Taking screenshot...');

            // Allow for additional render time to ensure all elements are painted
            await page.waitForTimeout(300);

            console.log('Creating comprehensive screenshot...');

            // Take multiple screenshots with slight delays to ensure full rendering
            const frames = [];

            // Initial capture
            frames.push(await page.screenshot({ type: 'png' }));

            // Allow more time for complex renderings
            await page.waitForTimeout(150);
            frames.push(await page.screenshot({ type: 'png' }));

            // Try different scroll positions for more complete capture
            await page.evaluate(() => {
                if (document.body.scrollHeight > window.innerHeight) {
                    window.scrollBy(0, 50);
                }
            });
            await page.waitForTimeout(100);
            frames.push(await page.screenshot({ type: 'png' }));

            // Reset scroll position
            await page.evaluate(() => {
                window.scrollTo(0, 0);
            });

            // Try to trigger :hover states on some elements
            await page.evaluate(() => {
                document.querySelectorAll('a, button').forEach((el) => {
                    const event = new MouseEvent('mouseover', {
                        bubbles: true,
                        cancelable: true
                    });
                    el.dispatchEvent(event);
                });
            });
            await page.waitForTimeout(100);
            frames.push(await page.screenshot({ type: 'png' }));

            // Select the most representative frame (the one with most visual information)
            // This uses image analysis to find the frame with highest entropy/detail
            const frameSizes = await Promise.all(frames.map(async (frame) => {
                const metadata = await sharp(frame).metadata();
                return {
                    buffer: frame,
                    size: metadata.size // Larger size often means more visual information
                };
            }));

            // Sort by file size (simple heuristic for information content)
            frameSizes.sort((a, b) => b.size - a.size);

            // Use the frame with most visual information
            const bestFrame = frameSizes[0].buffer;

            const outputBuffer = await sharp(bestFrame)
                .resize(options.width, options.height, {
                    fit: 'fill',
                    withoutEnlargement: true,
                    fastShrinkOnLoad: true
                })
                .png({ quality: options.quality === 'high' ? 90 : 70 })
                .toBuffer();

            logPerformance('Comprehensive screenshot capture', startTime);
            return {
                buffer: outputBuffer,
                isWebGLContent: page.webglDetection?.isWebGLContent || false
            };
        } else {
            // GIF with or without interactions
            const tempFilePath = path.join(tmpDir, 'frame');
            const outputGifPath = path.join(tmpDir, 'output.gif');

            // Capture frames based on animation strategy
            const frameCount = await captureFramesWithTimeLimit(page, tempFilePath, options);

            if (frameCount < 1) {
                throw new Error('Failed to capture any frames');
            }

            // Create GIF from frames
            await createOptimizedGif(tempFilePath, outputGifPath, frameCount, options);

            // Read GIF buffer
            const gifBuffer = await fs.promises.readFile(outputGifPath);

            logPerformance('Complete GIF generation', startTime);
            return {
                buffer: gifBuffer,
                isWebGLContent: page.webglDetection?.isWebGLContent || false
            };
        }
    } catch (err) {
        console.error(`Error in ${outputType} capture:`, err);
        throw err;
    } finally {
        // Clean up resources
        if (!isBrowserShared && browser) {
            await browser.close().catch(() => { });
        }

        if (tmpDir) {
            await cleanupTempDir(tmpDir);
        }
    }
}

// Public API functions
async function html2Screenshot(htmlContent, customOptions = {}) {
    const options = { ...defaultConfig, ...customOptions };
    return captureContent(htmlContent, options, 'screenshot');
}

async function html2gif(htmlContent, customOptions = {}) {
    const options = { ...defaultConfig, ...customOptions, preserveAnimations: true };
    return captureContent(htmlContent, options, 'gif');
}

async function html2gifWithInteractions(htmlContent, customOptions = {}) {
    const options = { ...defaultConfig, ...customOptions };
    return captureContent(htmlContent, options, 'gifWithInteractions');
}

// configurability function
// function setDefaultConfig(newDefaults) {
//     Object.assign(defaultConfig, newDefaults);
//     return { ...defaultConfig }; // Return copy of current config
// }

// Clean up resources on process exit
process.on('SIGINT', async () => {
    console.log('Shutting down...');
    if (sharedBrowser) {
        await sharedBrowser.close().catch(() => { });
        sharedBrowser = null;
    }
    process.exit(0);
});

module.exports = {
    html2Screenshot,
    html2gif,
    html2gifWithInteractions
};