const Joi = require('joi');

class TemplateValidation {

    createTemplate(params) {
        const schema = Joi.object({
            title: Joi.string().trim().required(),
            zip_url: Joi.string().trim().required(),
            description: Joi.string().trim().required()
        });
        return schema.validate(params);
    }

    updateTemplate(params) {
        const schema = Joi.object({
            title: Joi.string().trim().optional(),
            zip_url: Joi.string().trim().optional(),
            description: Joi.string().trim().optional()
        });
        return schema.validate(params);
    }

    updateTemplateStatus(params) {
        const schema = Joi.object({
            is_active: Joi.boolean().required()
        });
        return schema.validate(params);
    }
}

module.exports = new TemplateValidation();
