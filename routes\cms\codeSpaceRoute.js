const express = require('express');
const router = express.Router();

const { createNewRepository, getAllRepository, getAllRepositorySortList, checkRepositoryNameAvailability, getRepositoryDetails, getRepositoryCommitList, searchRepositoryFiles, getRepositoryLanguages } = require('../../controller/cms/codeSpace.controller');

const { createRepositoryValidation } = require('../../middlewares/validations/cms/code_space/codeSpaceValidation');

router.post('/create', createRepositoryValidation, createNewRepository);
router.post('/list', getAllRepository);
router.get('/sort-list', getAllRepositorySortList);
router.get('/check-name-availability', checkRepositoryNameAvailability);
router.get('/details/:id', getRepositoryDetails);
router.get('/get-commits/:id', getRepositoryCommitList);
router.get('/search-files/:id', searchRepositoryFiles);
router.get('/get-languages/:id', getRepositoryLanguages);

module.exports = router;