const { createPlan, updatePlan, updatePlanStatus } = require('../../../../validations/cms/top_up_plans/topUpPlansValidation');
class TagValidationMiddleware {
    createPlanValidation(req, res, next) {
        const { value, error } = createPlan(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updatePlanValidation(req, res, next) {
        const { value, error } = updatePlan(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updatePlanStatusValidation(req, res, next) {
        const { value, error } = updatePlanStatus(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new TagValidationMiddleware();