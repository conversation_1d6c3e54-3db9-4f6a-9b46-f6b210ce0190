const cron = require('node-cron');
const moment = require('moment');

const { ComponentUnlockHistory } = require('./../models/component_unlock_history.model');
const { UserCoinsHistory } = require('./../models/user_coins_history.model');
const { Users } = require('./../models/users.model');
const { ComponentLikes } = require('./../models/component_likes.model');
const { ComponentBookmarks } = require('./../models/component_bookmark.model');
const { ComponentViews } = require('./../models/component_views.model');
const { GitlabUsers } = require('../models/gitlab_users.model');
const { GitlabUserTokens } = require('../models/gitlab_user_tokens.model');
const { DraftComponents } = require('../models/draft_components.model');
const { GitlabRepository } = require('../models/gitlab_repository.model');
const { RepoInvitation } = require('../models/repo_invitation.model');
const { UserMpnPointLogs } = require('../models/user_mpn_point_logs.model');

const { coinTypes, pointActivity, pointActivityDescription } = require('../config/component.constant');
const { deductPointFromMasterBalance } = require('../services/mpc_balance.service');
const logger = require('../config/logger');

const { createUserAccessToken, addMemberToProject, createGitlabBranch, fetchProjectDetails, setProjectDefaultBranch, protectBranch } = require('./gitlab.helper');
const { generateGitlabTokenName, encryptDataWithAES } = require('../services/general.helper');

const { gitlabAdminScope, gitlabImportStatus, gitlabAccessLevel, gitlabDefaultBranch, gitlabProductionBranch, invitationStatus, repositoryState } = require('../config/gitlab.constant');

const { componentState } = require('../config/component.constant');

const { cacheRepositoryDataInRedis } = require('../services/repository.service');
const { createComponentForPublicCodeSpace } = require('../services/component.service');

// On Every At 12:00 AM
cron.schedule('0 0 * * *', async () => {
    try {
        await processUnlockComponents();
        await checkAndMarkUserBonusExpired();
        await checkAndRemoveNonSignupGuestUsers();
        await checkAndRenewGitlabAccessTokens();
        await checkAndMarkCollaborationInviteExpired();
    } catch (error) {
        logger.error(`error in 12:00 AM cron: ${error}`);
    }
});

// Schedule the cron job to run every hour
cron.schedule('0 * * * *', async () => {
    try {
        await deleteOldPlaceholderComponents();
    } catch (error) {
        logger.error(`Error in hourly cron job: ${error}`);
    }
});

cron.schedule('*/2 * * * *', async () => {
    try {
        await finishProjectForkProcess();
    } catch (error) {
        logger.error(`Error in every 2 mins cron job: ${error}`);
    }
});

/**
 * Asynchronously processes unlocking components based on expiry date.
 */
async function processUnlockComponents() {
    try {
        // Get the end of the current day
        const endDate = moment().endOf('day');

        // Find unlock history records that are active and expired
        const unlockHistory = await ComponentUnlockHistory.find({
            is_active: true,
            expired_on: { $lt: endDate }
        }).lean();

        // Iterate through each unlock history record
        for (const history of unlockHistory) {
            // Update the record to mark it as inactive
            await ComponentUnlockHistory.updateOne(
                { _id: history._id },
                { $set: { is_active: false } }
            );
            // Log the status of the expired component
            logger.warn(`Unlock component _id ${history._id} marked as expired`);
        }

        // Log completion message
        logger.info('processUnlockComponents Cron Completed');
        return;
    } catch (err) {
        // Log any errors that occur during processing
        logger.error(`error at processUnlockComponents${err}`);
    }
}

/**
 * Asynchronously function to check and mark user bonuses as expired
 */
async function checkAndMarkUserBonusExpired() {
    try {
        // Get the end of the current day
        const endDate = moment().endOf('day');

        // Find user coin history records for bonuses that are active
        const userBonusHistory = await UserCoinsHistory.find({
            type: coinTypes.BONUS,
            is_expired: false,
            expired_on: { $lt: endDate }
        }).lean();
        // // Iterate through each user bonus history record
        for (const history of userBonusHistory) {
            // Update the record to mark it as expired
            await UserCoinsHistory.updateOne({
                _id: history._id
            }, {
                $set: {
                    is_expired: true
                }
            });
            // Deducts points from the master balance of a user.
            await deductPointFromMasterBalance(
                history.user_id,
                history.pending_points,
                history.type
            );
            // Create user mpn point logs record for expiration
            await UserMpnPointLogs.create({
                user_id: history.user_id,
                points: history.pending_points,
                activity: pointActivity.EXPIRED,
                description: pointActivityDescription.BONUS_EXPIRED
            });
            // Log the status of the expired bonus
            logger.warn(
                `User Bonus _id ${history._id} marked as expired and deducted ${history.pending_points} from master wallet`
            );
        }
        // Log completion message
        logger.info('checkAndMarkUserBonusExpired Cron Completed');
        return;
    } catch (err) {
        // Log any errors that occur during processing
        logger.error(`Error occurred at checkAndMarkUserBonusExpired ${err}`);
    }
}

/**
 * Deletes non-signed up guest users and their related records who are older than 30 days.
 */
async function checkAndRemoveNonSignupGuestUsers() {
    try {
        // Calculate the date 30 days ago from today
        const thirtyDaysAgoDate = moment().subtract(30, 'days').endOf('day');

        // Fetch guest users who were created before 30 days ago and have not signed up
        const guestList = await Users.find({
            email: { $exists: false },
            is_deleted: false,
            created_at: { $lt: thirtyDaysAgoDate.toDate() }
        })
            .sort({ created_at: 1 })
            .lean();

        // Process each guest user
        for (const guest of guestList) {
            await markUserAndRelatedRecordsAsDeleted(guest._id);
            logger.warn(`Guest User's _id ${guest._id} marked as deleted`);
        }

        logger.info('Non-signup guest user cleanup completed.');
    } catch (err) {
        logger.error(`Error in checkAndRemoveNonSignupGuestUsers: ${err}`);
    }
}

/**
 * Marks a user and their related records as deleted.
 * @param {string} userId - The ID of the user to mark as deleted.
 */

async function markUserAndRelatedRecordsAsDeleted(userId) {
    try {
        // Mark user as deleted
        await Users.updateOne(
            { _id: userId },
            { $set: { is_deleted: true, is_active: false } }
        );

        // Mark all related likes, bookmarks, and views as deleted
        const update = { $set: { is_deleted: true, is_active: false } };
        await ComponentLikes.updateMany({ liked_by: userId }, update);
        await ComponentBookmarks.updateMany({ bookmarked_by: userId }, update);
        await ComponentViews.updateMany(
            { viewed_by: userId },
            { $set: { is_deleted: true } }
        ); // Only mark as deleted
    } catch (error) {
        logger.error(`Error in markUserAndRelatedRecordsAsDeleted: ${error}`);
        throw error;
    }
}

async function checkAndRenewGitlabAccessTokens() {
    try {
        // Get the end of the current day and add one day to ensure tokens expiring today are included
        const endDate = moment().endOf('day').add(1, 'days');

        logger.info(
            `Starting GitLab master admin token renewal check for tokens expiring before ${endDate.format()}`
        );

        const gitlabUserList = await GitlabUsers.find(
            {
                is_super_admin: true
            },
            '_id gitlab_user_id'
        ).lean();
        // Iterate through each GitLab super admin user
        for (const gitlabUser of gitlabUserList) {
            // Fetch GitLab users who are super admins and whose tokens expire before the end date
            const lastAccessToken = await GitlabUserTokens.find(
                {
                    user_id: gitlabUser._id,
                    token_expires_at: { $lt: endDate }
                },
                'token_expires_at personal_access_token_name'
            ).sort({
                created_at: -1
            });

            if (lastAccessToken) {
                // Generate a new token name based on the user's current token name
                const tokenName = generateGitlabTokenName(
                    lastAccessToken.personal_access_token_name
                );

                // Create a new access token for the user with the specified admin scope
                const gitlabUserToken = await createUserAccessToken(
                    gitlabUser.gitlab_user_id,
                    tokenName,
                    gitlabAdminScope
                );

                // Update the GitLab user document with the new token and its details
                await GitlabUserTokens.updateOne(
                    {
                        user_id: gitlabUser._id
                    },
                    {
                        personal_access_tokens: encryptDataWithAES(gitlabUserToken.token),
                        token_expires_at: gitlabUserToken.expires_at,
                        scopes: gitlabUserToken.scopes,
                        personal_access_token_name: tokenName
                    }
                );

                logger.info(
                    `Successfully updated master access token for GitLab user with ID ${gitlabUser.gitlab_user_id} (DB ID: ${gitlabUser._id})`
                );
            }
        }

        logger.info('Completed GitLab master admin token renewal check.');
    } catch (err) {
        // Log any errors that occur during the process
        logger.error(
            `Error during GitLab master admin token renewal check: ${err.message}`,
            { error: err }
        );
    }
}

// Function to delete components with component_state == placeholder and created_at at least 24 hours ago
async function deleteOldPlaceholderComponents() {
    try {
        // Calculate the timestamp for 24 hours ago
        const twentyFourHoursAgo = moment().subtract(24, 'hours').toDate();

        const result = await DraftComponents.deleteMany({
            component_state: componentState.PLACEHOLDER,
            created_at: { $lte: twentyFourHoursAgo }
        });

        logger.info(`Deleted ${result.deletedCount} old placeholder components.`);
    } catch (error) {
        logger.error(`Error while deleting old placeholder components: ${error}`);
    }
}

async function finishProjectForkProcess() {
    try {
        const repositoryList = await GitlabRepository.find({
            import_status: gitlabImportStatus.SCHEDULED
        }).sort({
            created_at: -1
        }).limit(5).lean();

        logger.info(`Found scheduled ${repositoryList.length} fork gitlab repository`);

        for (const repository of repositoryList) {

            if (repository?.project_id) {
                const gitlabProject = await fetchProjectDetails(repository.project_id);

                logger.info(`Fetched project ${repository.project_id} import_status ${gitlabProject.import_status} successfully`);

                if (gitlabProject.import_status == gitlabImportStatus.FINISHED) {
                    // Fetch GitLab user information based on the admin ID from the session
                    const gitlabUser = await GitlabUsers.findOne({ _id: repository.gitlab_user_id }).lean();

                    const project_id = repository?.project_id;

                    try {
                        // Add the GitLab user as a member of the new project with DEVELOPER access
                        await addMemberToProject(project_id, gitlabUser.gitlab_user_id, gitlabAccessLevel.DEVELOPER);
                        logger.info(`Member added successfully to project ${project_id}`);
                    } catch (error) {
                        logger.error(`Error while adding member to project ${project_id}`);
                    }

                    try {
                        // Create the production branch from the development branch
                        await createGitlabBranch(project_id, gitlabDefaultBranch, gitlabProductionBranch);
                        logger.info(`Development branch successfully added to project ${project_id}`);
                    } catch (error) {
                        logger.error(`Error while adding development branch to project ${project_id}`);
                    }

                    try {
                        // Add the GitLab user as a member of the new project with Developer access
                        await setProjectDefaultBranch(project_id, gitlabDefaultBranch);
                        logger.info(`Default branch configured successfully to project ${project_id}`);
                    } catch (error) {
                        logger.error(`Error while configured default branch to project ${project_id}`);
                    }

                    try {
                        // Protect the default branch to prevent direct pushes
                        await protectBranch(project_id, gitlabProductionBranch);
                        logger.info(`Development branch protection added successfully from project ${project_id}`);
                    } catch (error) {
                        logger.error(`Error while protecting development branch from project ${project_id}`);
                    }

                    if (repository?.state == repositoryState.PUBLIC) {
                        // Create respective project entry for public code-spaces
                        try {
                            await createComponentForPublicCodeSpace(repository._id);
                        } catch (error) {
                            logger.error(`Error while creating component for public code space ${repository._id}`);
                        }
                    }

                    try {
                        // Store main branch content in Redis caching
                        await cacheRepositoryDataInRedis(repository._id, project_id);
                    } catch (error) {
                        logger.error(`Error while caching component data in Redid for project ${project_id}`);
                    }


                    await GitlabRepository.updateOne({
                        _id: repository._id
                    }, {
                        $set: {
                            import_status: gitlabImportStatus.FINISHED,
                            production_branch_exists: true
                        }
                    });

                    logger.info(`Finished fork process successfully for project ${project_id}`);
                }
            }
        }

    } catch (error) {
        logger.error(`Error while finished processing for forked projects: ${error}`);
    }
}

/**
 * Asynchronously function to check and mark collaboration invites as expired
 */
async function checkAndMarkCollaborationInviteExpired() {
    try {
        // Get the end of the current day
        const endDate = moment().endOf('day');

        // Find invitation with pending status
        const invitations = await RepoInvitation.find({
            status: invitationStatus.PENDING,
            expires_at: { $lt: endDate }
        }).lean();
        // Iterate through each invitations record
        for (const invitation of invitations) {
            // Update the record to mark it as expired
            await RepoInvitation.updateOne({
                _id: invitation._id
            }, {
                $set: {
                    status: invitationStatus.EXPIRED,
                    is_used: true
                }
            });
            // Log the status of the expired bonus
            logger.warn(`Collaboration invite _id ${invitation._id} marked as expired for user email => ${invitation.invited_email}`);
        }
        // Log completion message
        logger.info('checkAndMarkCollaborationInviteExpired Cron Completed');
        return;
    } catch (err) {
        // Log any errors that occur during processing
        logger.error(`Error occurred at checkAndMarkUserBonusExpired ${err}`);
    }
}

