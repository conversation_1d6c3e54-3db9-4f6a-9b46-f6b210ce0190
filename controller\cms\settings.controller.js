const constants = require('../../config/constants');
const { ReS, sendError } = require('../../services/general.helper');
const logger = require('../../config/logger');


const Settings = require('../../models/settings.model').Settings;

async function updateSystemMaintenance(req, res) {
    try {
        // Extract necessary data from request body
        const { is_maintenance_on, maintenance_end_time } = req.body;

        // Find and update the system maintenance settings in the database
        const settingDetails = await Settings.findOneAndUpdate(
            // Query for finding the relevant setting
            { 'setting_slug': 'system_maintenance' },
            // Update operation
            {
                // Set new values
                $set: {
                    values: {
                        is_maintenance_on: is_maintenance_on,
                        maintenance_end_time: maintenance_end_time
                    }
                }
            },
            // Options: return the updated document
            { new: true }
        );

        // Respond with success and updated setting details
        return ReS(res, constants.success_code, 'Success', settingDetails);
    } catch (err) {
        // Log error and send error response
        logger.error(`Error at CMS Controller updateSystemMaintenance${err}`);
        return sendError(res, err);
    }
}

async function getSettingDetails(req, res) {
    try {
        const settingDetails = await Settings.findOne({
            'setting_slug': req.params.slug
        }).lean();
        return ReS(res, constants.success_code, 'Data Fetched', settingDetails);
    } catch (err) {
        console.log('Error at CMS Controller getSettingDetails', err);
        return sendError(res, err);
    }
}

module.exports = {
    updateSystemMaintenance,
    getSettingDetails
};