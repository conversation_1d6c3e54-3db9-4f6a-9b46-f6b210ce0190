const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { priceTiers } = require('../config/user.constant');

// Define the schema for social links
const SocialLinkSchema = new Schema({
    platform: {
        type: String,
        required: true
    },
    username: {
        type: String,
        required: true
    }
}, { _id: false }); // Disable automatic _id generation for sub-documents


const UsersSchema = new Schema({
    guest_id: {
        type: String
    },
    visitor_id: {
        type: String
    },
    first_name: {
        type: String
    },
    last_name: {
        type: String
    },
    email: {
        type: String
    },
    username: {
        type: String
    },
    mobile_no: {
        type: String
    },
    mobile_country_code: {
        type: String
    },
    is_active: {
        type: Boolean,
        default: true
    },
    is_verified: {
        type: Boolean,
        default: false
    },
    is_creator: {
        type: Boolean,
        default: false
    },
    terms_and_conditions: {
        type: Boolean,
        default: false
    },
    is_social_login: {
        type: Boolean,
        default: false
    },
    social_id: {
        type: String
    },
    avatar: {
        type: String
    },
    provider_name: {
        type: String
    },
    country: {
        id: {
            type: Number
        },
        iso3: {
            type: String
        },
        name: {
            type: String
        }
    },
    last_login: {
        type: Date
    },
    is_deleted: {
        type: Boolean,
        default: false
    },
    gitlab_user_exists: {
        type: Boolean,
        default: false
    },
    social_links: [SocialLinkSchema], // Array of social_links objects
    biography: {
        type: String
    },
    website: {
        type: String
    },
    price_tier: {
        type: Number,
        default: priceTiers.DEFAULT
    },
    technologies: [{
        type: mongoose.Types.ObjectId,
        ref: 'supported_platforms'
    }],
    total_views: {
        type: Number,
        default: 0
    },
    total_likes: {
        type: Number,
        default: 0
    },
    total_bookmarks: {
        type: Number,
        default: 0
    },
    total_stars: {
        type: Number,
        default: 0
    },
    followers_count: {
        type: Number,
        default: 0
    },
    following_count: {
        type: Number,
        default: 0
    },
    storage_limit: {
        type: Number,
        default: 500 * 1024 * 1024 // 500 MB in bytes
    },
    used_storage: {
        type: Number,
        default: 0 // Start with zero used storage
    },
    last_storage_sync: {
        type: Date
    },
    is_author_fee_overridden: {
        type: Boolean,
        default: false
    },
    overridden_author_fee: { // Percentage
        type: Number,
        default: 0
    },
    is_buyer_fee_overridden: {
        type: Boolean,
        default: false
    },
    overridden_buyer_fee: { // Percentage
        type: Number,
        default: 0
    },
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

UsersSchema.set('autoIndex', true);

const Users = mongoose.model('users', UsersSchema);

module.exports = {
    Users
};