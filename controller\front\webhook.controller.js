
// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, sendError } = require('../../services/general.helper');

const { webhookEvents, gitlabProductionBranch } = require('./../../config/gitlab.constant');

const { fetchAndUpdateRepositoryLanguages } = require('../../services/language.service');
const { fetchAndUpdateProjectStorageSize, fetchAndSyncUserUsedStorage } = require('../../services/gitlab_webhook.service');

const { GitlabRepository } = require('../../models/gitlab_repository.model');

async function gitlabMergeRequestEventWebhook(req, res) {
    try {
        const { event_type, object_attributes, project } = req.body;

        console.log(`Web hook call received for Event => ${event_type} for project ${project.id}`);

        if (event_type === webhookEvents.MERGE_REQUEST) {
            const action = object_attributes.action;
            const targetBranch = object_attributes.target_branch;
            const state = object_attributes.state;

            if (action === 'merge' && targetBranch === gitlabProductionBranch && state === 'merged') {
                await fetchAndUpdateRepositoryLanguages(project.id, 'webhook');
            }
        }
        return ReS(res, constants.success_code, 'Gitlab Webhook called');
    } catch (err) {
        logger.error(`Error at Front Controller gitlabMergeRequestEventWebhook: ${err}`);
        return sendError(res, err);
    }
}

async function gitlabRepositoryUpdateEventWebhook(req, res) {
    try {
        const { event_name, project_id } = req.body;

        console.log(`Web hook call received for Event => ${event_name} for project_id => ${project_id}`);

        if (event_name === webhookEvents.REPOSITORY_UPDATE) {
            await GitlabRepository.updateOne({
                project_id: project_id
            }, {
                $set: {
                    last_activity_at: new Date()
                }
            });
            // Fetch and update gitlab project storage size
            await fetchAndUpdateProjectStorageSize(project_id);
            await fetchAndSyncUserUsedStorage(project_id);
            console.log(`Updated repository last_activity_at date for project => ${project_id}`);
        }
        return ReS(res, constants.success_code, 'Gitlab Webhook called');
    } catch (err) {
        logger.error(`Error at Front Controller gitlabRepositoryUpdateEventWebhook: ${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    gitlabMergeRequestEventWebhook,
    gitlabRepositoryUpdateEventWebhook
};  