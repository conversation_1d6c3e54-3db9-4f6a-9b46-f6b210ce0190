const logger = require('../config/logger');

const Settings = require('../models/settings.model').Settings;
const GitlabUsers = require('../models/gitlab_users.model').GitlabUsers;
const GitlabUserTokens = require('../models/gitlab_user_tokens.model').GitlabUserTokens;
const Users = require('../models/users.model').Users;


const { decryptDataWithAES } = require('./general.helper');

/**
 * Retrieves the configuration for welcome bonus coins.
 * @returns {Promise<number>} The number of welcome bonus coins configured.
 */
const getWelcomeBonusCoinsConfig = async () => {
    try {
        // Find the settings document for welcome bonus coins
        const settings = await Settings.findOne({ setting_slug: 'welcome_bonus_coins' }).lean();

        // Return the configured number of bonus coins or 0 if not found

        return {
            bonus_coins: settings.values.bonus_coins || 0,
            expiry_duration: settings.values.expiry_duration || 30
        };
    } catch (err) {
        // Log any errors that occur during the retrieval
        logger.error(`Error at settings service from function getWelcomeBonusCoinsConfig${err}`);
        throw err;
    }
};

// Function to retrieve the GitLab master admin's access token.
const getGitlabMasterAccessToken = async () => {
    try {
        // Find the GitLab admin user and select only the personal access tokens.
        const gitlabUser = await GitlabUsers.findOne({ is_super_admin: true }, '_id').lean();
        // If the GitLab admin user is found, decrypt and return the access token.
        if (gitlabUser) {
            const gitlabToken = await GitlabUserTokens.findOne({ user_id: gitlabUser._id }).lean();
            return decryptDataWithAES(gitlabToken.personal_access_tokens);
        }
        // If no admin user is found, return null or handle accordingly.
        return null;
    } catch (err) {
        // Log any errors that occur during the retrieval process.
        logger.error(`Error at settings service from function getGitlabMasterAccessToken: ${err}`);
        throw err;
    }
};

const getUserGitlabAccessToken = async (user_id) => {
    try {
        // Find the GitLab admin user and select only the personal access tokens.
        const gitlabUser = await GitlabUsers.findOne({ _id: user_id }, '_id').lean();
        console.log(gitlabUser);
        // If the GitLab admin user is found, decrypt and return the access token.
        if (gitlabUser) {
            const gitlabToken = await GitlabUserTokens.findOne({ user_id: gitlabUser._id }).sort({ created_at: -1 }).lean();
            console.log(gitlabToken);
            return decryptDataWithAES(gitlabToken.personal_access_tokens);
        }
        // If no admin user is found, return null or handle accordingly.
        return null;
    } catch (err) {
        // Log any errors that occur during the retrieval process.
        logger.error(`Error at settings service from function getUserGitlabAccessToken: ${err}`);
        throw err;
    }
};

const getParityConfig = async () => {
    try {
        // Find the settings document for mpn_parity
        const settings = await Settings.findOne({ setting_slug: 'mpn_parity' }).lean();

        // Return the configured number of bonus mpn_parity or 0 if not found

        return settings.values || 0;
    } catch (err) {
        // Log any errors that occur during the retrieval
        logger.error(`Error at settings service from function getParityConfig ${err}`);
        throw err;
    }
};

const getBuyerFeeConfig = async () => {
    try {
        // Find the settings document for mpn_buyer_fee
        const settings = await Settings.findOne({ setting_slug: 'mpn_buyer_fee' }).lean();

        // Return the configured number of bonus mpn_buyer_fee or 0 if not found

        return {
            percentage: settings?.values?.percentage || 0,
            fixed_min: settings?.values?.fixed_min || 0
        };
    } catch (err) {
        // Log any errors that occur during the retrieval
        logger.error(`Error at settings service from function getBuyerFeeConfig ${err}`);
        throw err;
    }
};

const getAuthorFeeConfig = async (userId) => {
    try {
        const user = await Users.findOne({
            _id: userId
        }, "is_author_fee_overridden overridden_author_fee").lean();
        // If user has overridden the author fee, return that value
        if (user?.is_author_fee_overridden === true && typeof user.overridden_author_fee === 'number') {
            return user.overridden_author_fee;
        }
        // Find the settings document for mpn_author_fee
        const settings = await Settings.findOne({ setting_slug: 'mpn_author_fee' }).lean();

        // Return the configured number of bonus mpn_author_fee or 0 if not found

        return settings?.values?.percentage || 0;
    } catch (err) {
        // Log any errors that occur during the retrieval
        logger.error(`Error at settings service from function getAuthorFeeConfig ${err}`);
        throw err;
    }
};

module.exports = {
    getWelcomeBonusCoinsConfig,
    getGitlabMasterAccessToken,
    getUserGitlabAccessToken,
    getParityConfig,
    getBuyerFeeConfig,
    getAuthorFeeConfig
};