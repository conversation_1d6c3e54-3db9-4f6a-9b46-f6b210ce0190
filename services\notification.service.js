// Model declaration

const { Users } = require('../models/users.model');
const { Components } = require('../models/component.model');

const { saveAndSendNotification } = require('../services/notification_helper.service');

const { notificationTypes } = require('../config/component.constant');


const sendNotificationOnPublishElement = async (element_id, user_id) => {
    try {
        const element = await Components.findOne({ _id: element_id }, 'title slug').populate({
            'path': 'created_by_user',
            'select': 'first_name last_name username email avatar'
        }).lean();

        const notificationData = {
            title: element.title,
            message: `${element.title} has new updates please have a look`,
            type: notificationTypes.ELEMENT_PUBLISH,
            created_by: user_id,
            entity: {
                slug: element.slug,
                created_by_user: element.created_by_user.username
            }
        };

        const receivers = await fetchUsersList({
            email: {
                $exists: true
            },
            is_active: true,
            _id: {
                $ne: user_id
            }
        });

        await saveAndSendNotification(notificationData, receivers);
    } catch (error) {
        // Log error if any
        console.error('Error at function sendNotificationOnPublishElement', error);
        throw error;
    }
};

const fetchUsersList = async (conditions) => {
    try {
        return await Users.distinct('_id', conditions).lean();
    } catch (error) {
        // Log error if any
        console.error('Error at function fetchUsersList', error);
        throw error;
    }
};

module.exports = {
    sendNotificationOnPublishElement
};