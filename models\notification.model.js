const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const notificationSchema = new Schema({
    title: {
        type: String,
        required: true
    },
    message: {
        type: String,
        required: true
    },
    type: {
        type: String,
        required: true
    },
    entity: {
        type: mongoose.Schema.Types.Mixed
    },
    created_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const Notifications = mongoose.model('notifications', notificationSchema);

module.exports = {
    Notifications
};