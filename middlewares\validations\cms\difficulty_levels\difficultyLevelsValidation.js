const { createDifficultyLevel, updateDifficultyLevel } = require('../../../../validations/cms/difficulty_levels/difficultyLevelsValidation');
class DifficultyLevelValidationMiddleware {
    createDifficultyLevelValidation(req, res, next) {
        const { value, error } = createDifficultyLevel(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateDifficultyLevelValidation(req, res, next) {
        const { value, error } = updateDifficultyLevel(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new DifficultyLevelValidationMiddleware();