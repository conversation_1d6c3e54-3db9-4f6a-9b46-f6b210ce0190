const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const componentDownloadHistorySchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    component_type: {
        type: String
    },
    repository_id: {
        type: mongoose.Types.ObjectId,
        ref: 'gitlab_repository'
    },
    path: {
        type: String
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const ComponentDownloadHistory = mongoose.model('component_download_history', componentDownloadHistorySchema);

module.exports = {
    ComponentDownloadHistory
};