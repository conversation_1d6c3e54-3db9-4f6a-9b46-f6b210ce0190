const express = require('express');
const router = express.Router();

const { subscribeNewsLetterValidation, verifyNewsLetterSubscriptionValidation, unSubscribeNewsLetterValidation } = require('../../middlewares/validations/front/news_letter/newsLetterValidation');

const { subscribeNewsLetter, verifyNewsLetterSubscription, unSubscribeNewsLetter } = require('../../controller/front/newsletter.controller');

router.post('/subscribe-newsletter', subscribeNewsLetterValidation, subscribeNewsLetter);
router.post('/verify-newsletter-subscription', verifyNewsLetterSubscriptionValidation, verifyNewsLetterSubscription);
router.post('/unsubscribe-newsletter', unSubscribeNewsLetterValidation, unSubscribeNewsLetter);

module.exports = router;