const express = require('express');
const router = express.Router();

const { createCollection, updateCollection, sortListCollectionAlphabetically, addCollectionItems, publishCollection, unpublishCollection, getAllCollections } = require('../../controller/front/collection.controller');

const { createOrEditCollectionValidation, addCollectionItemsValidation } = require('./../../middlewares/validations/front/collection/collectionValidation');

router.post('/', createOrEditCollectionValidation, createCollection);
router.put('/:id', createOrEditCollectionValidation, updateCollection);
router.get('/sort-list', sortListCollectionAlphabetically);
router.put('/:id/items', addCollectionItemsValidation, addCollectionItems);
router.put('/:id/publish', publishCollection);
router.put('/:id/unpublish', unpublishCollection);
router.post('/list', getAllCollections);

module.exports = router;    