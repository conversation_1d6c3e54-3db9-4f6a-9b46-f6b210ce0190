const express = require('express');
const router = express.Router();

const { searchRepositoryFiles, getRepositoryContentRecursively, downloadRepositoryContent, downloadRepositoryFileContent, getRepositoryDetails, getRepositoryBranches, getRepositoryCommitDiff, getRepositoryCommitInfo, getRepositoryFileContent, getRepositoryCommitList, getRepositoryLanguages, getRepositoryTreeCommitDetails, getRepositoryLanguagesCombined, AddStarToRepository, RemoveStarFromRepository, increaseRepositoryViews } = require('../../controller/front/publishedCodeSpace.controller');

const { checkIsAccessible } = require('../../middlewares/validateCodeSpace');

router.get('/search-files/:id', checkIsAccessible, searchRepositoryFiles);
router.get('/get-tree/:id', getRepositoryContentRecursively);
router.get('/get-commits/:id', checkIsAccessible, getRepositoryCommitInfo);
router.get('/get-file-content/:id/:path/raw', checkIsAccessible, getRepositoryFileContent);
router.get('/download/file/:id', checkIsAccessible, downloadRepositoryFileContent);
router.get('/download/:id', checkIsAccessible, downloadRepositoryContent);
router.get('/get-branches/:id', checkIsAccessible, getRepositoryBranches);
router.get('/get-details/:id', checkIsAccessible, getRepositoryDetails);
router.get('/:id/commit/:sha/diff', checkIsAccessible, getRepositoryCommitDiff);
router.get('/get-commits-list/:id', checkIsAccessible, getRepositoryCommitList);
router.get('/get-languages/:id', checkIsAccessible, getRepositoryLanguages);
router.post('/get-tree/commits/:id', getRepositoryTreeCommitDetails);
router.get('/get-languages/combine/:slug', getRepositoryLanguagesCombined);
router.put('/:id/star', AddStarToRepository);
router.put('/:id/star/remove', RemoveStarFromRepository);
router.put('/:id/increase/views', increaseRepositoryViews);

module.exports = router;