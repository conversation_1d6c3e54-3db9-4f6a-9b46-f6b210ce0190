const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const paymentTransactionSchema = new Schema({
    transaction_id: {
        type: String
    },
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    amount: {
        type: Number,
        required: true,
        default: 0
    },
    status: {
        type: String
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const PaymentTransactions = mongoose.model('payment_transactions', paymentTransactionSchema);

module.exports = {
    PaymentTransactions
};