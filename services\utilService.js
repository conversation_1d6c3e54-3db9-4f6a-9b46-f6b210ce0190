const constants = require('../config/constants');
const RequestIp = require('@supercharge/request-ip');
const logger = require('../config/logger');

/* Used in routes, once max api limit is hit, this will be called*/
module.exports.IPlimitReached = function (req, res) {
    try {
        const response = {
            'status': constants.limit_reached,
            'message': 'Too many requests, please try again after sometime.'
        };
        const rateLimitedIP = RequestIp.getClientIp(req);
        logger.info('req.originalUrl', req.originalUrl);
        logger.info('rateLimitedIP', rateLimitedIP);
        return res.json(response);
    } catch (error) {
        logger.error(`error at IPlimitReached${ error}`);
        const response = {
            'status': constants.server_error_code,
            'message': 'Oops! Something went wrong.'
        };
        return res.json(response);
    }
};

