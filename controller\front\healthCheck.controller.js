const constants = require('../../config/constants');
const logger = require('../../config/logger');
const { ReS } = require('../../services/general.helper');

async function healthCheck(req, res) {
    try {
        return ReS(res, constants.success_code, 'Health check success', req.query);
    } catch (err) {
        logger.error(`Error at healthCheck Controller ${err}`);
        return ReS(res, constants.server_error_code, err);
    }
}

module.exports = {
    healthCheck
};