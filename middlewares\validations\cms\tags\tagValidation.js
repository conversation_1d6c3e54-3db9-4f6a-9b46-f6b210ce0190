const { createTag, updateTag, updateTagStatus } = require('../../../../validations/cms/tags/tagValidation');
class TagValidationMiddleware {
    createTagValidation(req, res, next) {
        const { value, error } = createTag(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateTagValidation(req, res, next) {
        const { value, error } = updateTag(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateTagStatusValidation(req, res, next) {
        const { value, error } = updateTagStatus(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new TagValidationMiddleware();