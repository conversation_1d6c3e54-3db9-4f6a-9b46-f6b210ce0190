const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const GitlabUsersSchema = new Schema({
    admin_id: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    gitlab_user_id: {
        type: Number,
        required: true
    },
    email: {
        type: String,
        required: true
    },
    extern_uid: {
        type: String,
        required: true
    },
    name: {
        type: String,
        required: true
    },
    username: {
        type: String,
        required: true
    },
    password: {
        type: String,
        required: true
    },
    version: {
        type: Number
    },
    is_super_admin: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const GitlabUsers = mongoose.model('gitlab_users', GitlabUsersSchema);

module.exports = {
    GitlabUsers
};