const { Admins } = require('./../models/admins.model');
const { AdminRoles } = require('./../models/admin_roles.model');
const mongoose = require('mongoose');
const constants = require('../config/constants');
const logger = require('../config/logger');

const getAdminDetails = async (userId) => {
    try {
        const adminDetail = await Admins.aggregate([
            {
                '$match': {
                    '_id': new mongoose.Types.ObjectId(userId)
                }
            }, {
                '$lookup': {
                    'from': 'admin_roles',
                    'let': {
                        'roles': '$roles'
                    },
                    'pipeline': [
                        {
                            '$match': {
                                '$expr': {
                                    '$in': [
                                        '$_id', '$$roles'
                                    ]
                                },
                                'is_active': true
                            }
                        }, {
                            '$lookup': {
                                'from': 'admin_permission_groups',
                                'let': {
                                    'permission_groups': '$permission_groups'
                                },
                                'pipeline': [
                                    {
                                        '$match': {
                                            '$expr': {
                                                '$in': [
                                                    '$_id', '$$permission_groups'
                                                ]
                                            },
                                            'is_active': true
                                        }
                                    }
                                ],
                                'as': 'permission_groups'
                            }
                        }
                    ],
                    'as': 'admin_roles'
                }
            }, {
                '$project': {
                    'admin_roles': 1,
                    'permission_groups': {
                        '$reduce': {
                            'input': '$admin_roles.permission_groups',
                            'initialValue': [],
                            'in': {
                                '$concatArrays': [
                                    '$$value', '$$this'
                                ]
                            }
                        }
                    }
                }
            }
        ]);
        return adminDetail;
    } catch (error) {
        logger.error(`Error from service function getAdminDetails${error}`);
        throw error;
    }
};

const getAdminPermissions = async (req, res, userId) => {
    try {
        // Authorize the sub admin to see if she can access our resources
        const adminDetails = await Admins.findOne({ _id: userId }).lean();

        if (adminDetails && adminDetails.roles) {
            // Check the Permissions
            const roles = await AdminRoles.find({ _id: { $in: adminDetails.roles }, is_active: true }).populate({
                path: 'permission_groups',
                populate: { path: 'permission_list' }
            }).lean();

            const groups = [];
            const permissions = [];

            for (const role of roles) {
                groups.push(...role.permission_groups);
            }

            for (const group of groups) {
                permissions.push(...group.permission_list);
            }
            const mappedArray = permissions.map((permission) => `${permission.module}/${permission.name}`);
            return mappedArray;
        } else {
            return res.status(constants.forbidden_code).json({
                'status': constants.forbidden_code,
                'message': 'Apologies! Your account no longer exists. Please reach out to the CMS admin for further assistance.'
            });
        }
    } catch (error) {
        logger.error(`Error from users service function getAdminPermissions${error}`);
        throw error;
    }
};

module.exports = {
    getAdminDetails,
    getAdminPermissions
};