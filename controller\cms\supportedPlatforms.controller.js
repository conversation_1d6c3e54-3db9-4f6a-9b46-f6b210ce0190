const constants = require('../../config/constants');
const logger = require('../../config/logger');

const { ReS, generateSlug, escapeRegex, sendError } = require('../../services/general.helper');

const SupportedPlatforms = require('../../models/supported_platforms.model').SupportedPlatforms;


async function createPlatform(req, res) {
    try {
        const {
            title,
            description,
            image_url,
            is_active
        } = req.body;

        const platformSlug = generateSlug(title);

        await SupportedPlatforms.create({
            title: title,
            slug: platformSlug,
            description: description,
            image_url: image_url,
            is_active: is_active,
            created_by: req.session._id
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createPlatform${err}`);
        return sendError(res, err);
    }
}

async function updatePlatform(req, res) {
    try {

        const postData = req.body;

        const platformData = await SupportedPlatforms.findOne({
            _id: req.params.id
        });

        if (platformData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Platform Not Found.');
        }

        if (postData.title) {
            postData['slug'] = generateSlug(postData.title);
        }
        postData['updated_by'] = req.session._id;
        await SupportedPlatforms.updateOne({
            _id: req.params.id
        }, {
            '$set': postData
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updatePlatform${err}`);
        return sendError(res, err);
    }
}

async function updatePlatformStatus(req, res) {
    try {

        const { is_active } = req.body;

        const platformData = await SupportedPlatforms.findOne({
            _id: req.params.id
        });

        if (platformData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Platform Not Found.');
        }

        await SupportedPlatforms.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updatePlatformStatus${err}`);
        return sendError(res, err);
    }
}

async function getAllPlatforms(req, res) {
    try {
        const totalDocuments = await SupportedPlatforms.countDocuments();
        // Set default conditions
        const conditions = {};
        // Set default sort
        const sort = {
            'created_at': -1
        };
        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        const filterDocuments = await SupportedPlatforms.countDocuments(conditions);
        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                is_active: 1,
                description: 1,
                created_at: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });
        const platformList = await SupportedPlatforms.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: platformList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllPlatforms${err}`);
        return sendError(res, err);
    }
}

async function getPlatformDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            '_id': req.params.id
        };
        const platformData = await SupportedPlatforms.findOne(conditions).lean();
        if (!platformData) {
            return ReS(res, constants.resource_not_found, 'Oops! Platform Not Found.');
        }
        return ReS(res, constants.success_code, 'Data Fetched', platformData);
    } catch (err) {
        logger.error(`Error at CMS Controller getPlatformDetails${err}`);
        return sendError(res, err);
    }
}

async function getAllPlatformSortList(req, res) {
    try {
        // Set default conditions
        const conditions = {
            is_active: true
        };
        // Set default sort
        const sort = {
            'created_at': -1
        };
        if (req.query.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.query.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                slug: 1,
                description: 1,
                image_url: 1,
                created_at: 1,
                is_active: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        const platformList = await SupportedPlatforms.aggregate(query);
        return ReS(res, constants.success_code, 'Data Fetched', platformList);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllPlatformSortList${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createPlatform,
    updatePlatform,
    updatePlatformStatus,
    getAllPlatforms,
    getPlatformDetails,
    getAllPlatformSortList
};