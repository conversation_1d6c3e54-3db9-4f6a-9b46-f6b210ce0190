const constants = require('../../config/constants');
const logger = require('../../config/logger');
const { ReS, sendError, encryptDataWithAES, decryptDataWithAES } = require('../../services/general.helper');
const { s3UploadStaticContent, uploadBase64ToS3, getFileExtensionFromBase64 } = require('../../services/general.helper');
const { getComponentOrientation } = require('../../services/component.service');
const filePath = require('path');

const GitlabUsers = require('../../models/gitlab_users.model').GitlabUsers;

async function uploadFile(req, res) {
    try {
        let orientation;
        const fileExtension = filePath.extname(req.files.image.name);
        const filename = `${new Date().getTime()}${filePath.extname(req.files.image.name)}`;
        const documentPath = `assets/${filename}`;
        await s3UploadStaticContent(req.files.image, documentPath);

        // Check if layout query parameter is true and process accordingly
        if (req.query && req.query.layout && req.query.layout === 'true') {
            // Determine whether the uploaded file is an image or video
            const type = req.files.image.mimetype.startsWith('image/') ? 'image' : 'video';

            // Process based on the type of file
            if (type === 'image') {
                try {
                    // Get aspect ratio for images
                    orientation = await getComponentOrientation(req.files.image, type);
                } catch (error) {
                    // Handle errors when getting aspect ratio for images
                    logger.error(`Error while getting aspect ratio for images: ${error}`);
                }
            } else if (type === 'video') {
                try {
                    // Get aspect ratio for videos
                    orientation = await getComponentOrientation(documentPath, type);
                } catch (error) {
                    // Handle errors when getting aspect ratio for videos
                    logger.error(`Error while getting aspect ratio for videos: ${error}`);
                }
            }
        }
        return ReS(res, constants.success_code, 'File Uploaded Successfully', {
            path: documentPath,
            orientation: orientation,
            file_extension: fileExtension
        });
    } catch (err) {
        logger.error(`Error at CMS Controller uploadFile${err}`);
        return sendError(res, err);
    }
}

async function uploadFileBase64(req, res) {
    try {
        const { image } = req.body;
        const fileExtension = getFileExtensionFromBase64(image);
        const filename = `${new Date().getTime()}.${fileExtension.extension}`;
        const documentPath = `assets/${filename}`;
        await uploadBase64ToS3(image, documentPath, fileExtension.mimeType);
        return ReS(res, constants.success_code, 'File Uploaded Successfully', { path: documentPath });
    } catch (err) {
        logger.error(`Error at CMS Controller uploadFile${err}`);
        return sendError(res, err);
    }
}

async function encryptWithAES(req, res) {
    try {
        const { plain_text } = req.body;
        const cipherText = encryptDataWithAES(plain_text);
        return ReS(res, constants.success_code, 'Encrypted Successfully', { cipherText: cipherText });
    } catch (err) {
        logger.error(`Error at CMS Controller encryptWithAES${err}`);
        return sendError(res, err);
    }
}

async function decryptWithAES(req, res) {
    try {
        const { cipher_text } = req.body;
        const plainText = decryptDataWithAES(cipher_text);
        return ReS(res, constants.success_code, 'Decrypted Successfully', { plainText: plainText });
    } catch (err) {
        logger.error(`Error at CMS Controller encryptWithAES${err}`);
        return sendError(res, err);
    }
}

async function loginToGitLab(req, res) {

    // Fetch GitLab user information from the database based on the admin ID stored in the session
    const gitlabUser = await GitlabUsers.findOne({
        admin_id: req.session._id
    }, 'username password').lean();

    // Check if the GitLab user record was found
    if (gitlabUser == null) {
        // If the GitLab user record does not exist, respond with an error message
        return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exists.');
    }

    const password = decryptDataWithAES(gitlabUser.password);

    const username = gitlabUser.username;

    const puppeteer = require('puppeteer');

    const browser = await puppeteer.launch({
        headless: 'new',
        executablePath: process.env.PUPPETEER_EXECUTABLE_PATH,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    const page = await browser.newPage();

    await page.goto('https://gitlab.multiplatform.network/users/sign_in');

    // Fill in the username and password fields
    await page.type('#user_login', username);
    await page.type('#user_password', password);

    // Click the sign-in button
    await Promise.all([
        page.click('.gl-button.btn.btn-block.btn-md.btn-confirm.js-sign-in-button'),
        page.waitForNavigation({ waitUntil: 'networkidle0' })
    ]);

    // Save the cookies
    const cookies = await page.cookies();
    await browser.close();

    return ReS(res, constants.success_code, 'Gitlab Login Successfully', { cookies });
}

module.exports = {
    uploadFile,
    uploadFileBase64,
    encryptWithAES,
    decryptWithAES,
    loginToGitLab
};