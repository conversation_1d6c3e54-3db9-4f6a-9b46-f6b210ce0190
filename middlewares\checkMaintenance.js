const constants = require('../config/constants');
const moment = require('moment');
const Settings = require('../models/settings.model').Settings;


module.exports = async function (req, res, next) {

    /*
        When performing a cross domain request, you will recieve
        a preflight request first. This is to check if our the app
        is safe. 
        We skip the token auth for [OPTIONS] requests.
        if(req.method == 'OPTIONS') next(); 
    */

    try {

        /* by pass maintenance if key is passed, most likely it will be from local env or pre-prod env to check migrations */
        if (req.headers && req.headers['x-bypass-maintenance'] === 'TrQba5KOj9LHPHRzQDvqW0D0kV8ruxUoRYX5jiA4BSAIETqBGvlQNY3zSTdV2qfp') {
            return next();
        }

        /* do not send 503 to health checker */
        const whitelistEndpoints = ['/api/front/health/health-check'];
        if (whitelistEndpoints.includes(req.path)) {
            return next();
        }

        const systemConfigInfo = await Settings
            .findOne({ setting_slug: 'system_maintenance' })
            .select('-__v -_id -created_at -updated_at').lean();

        if ((systemConfigInfo && systemConfigInfo.values && systemConfigInfo.values.is_maintenance_on)) {

            /* send end maintenance time in res header */
            if (systemConfigInfo.values.maintenance_end_time) {
                return res.status(constants.service_unavailable).json({
                    // "status": constants.service_unavailable,
                    'message': 'Server is currently under maintenance, please try after sometime.',
                    'Retry-After': moment(systemConfigInfo.values.maintenance_end_time).utc()
                });
            } else {
                return res.status(constants.service_unavailable).json({
                    // "status": constants.service_unavailable,
                    'message': 'Server is currently under maintenance, please try after sometime.',
                    'Retry-After': null
                });
            }

        } else {
            return next();
        }
    }
    catch (err) {
        console.log('error at checkMaintenance', err);
        return res.json({
            'status': constants.server_error_code,
            'message': 'Oops! Something went wrong.'
        });
    }
};





