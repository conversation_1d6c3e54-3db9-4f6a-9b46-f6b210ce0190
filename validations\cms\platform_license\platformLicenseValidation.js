const Joi = require('joi');

const { licenseType } = require('../../../config/component.constant');

class LicenseValidation {
    createLicense(value) {
        const schema = Joi.object({
            title: Joi.string().trim().required(),
            description: Joi.string().trim().required(),
            dynamic_values: Joi.string().trim().required(),
            category: Joi.string().trim().required(),
            license_type: Joi.array().items(Joi.string().valid(...Object.values(licenseType)).trim()).min(1).unique().required()
        }).options({ allowUnknown: true });
        return schema.validate(value);
    }

    updateLicense(value) {
        const schema = Joi.object({
            title: Joi.string().trim().required(),
            description: Joi.string().trim().required(),
            dynamic_values: Joi.string().trim().required(),
            category: Joi.string().trim().required(),
            license_type: Joi.array().items(Joi.string().valid(...Object.values(licenseType)).trim()).min(1).unique().required()
        }).options({ allowUnknown: true });;
        return schema.validate(value);
    }
}

module.exports = new LicenseValidation();