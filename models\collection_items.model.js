const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const collectionItemsSchema = new Schema({
    collection_id: {
        type: mongoose.Types.ObjectId,
        ref: 'collections'
    },
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const CollectionItems = mongoose.model('collection_items', collectionItemsSchema);

module.exports = {
    CollectionItems
};