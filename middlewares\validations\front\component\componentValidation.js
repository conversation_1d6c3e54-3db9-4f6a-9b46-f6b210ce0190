const { linkCodeSpacesToComponent, updateComponentRepository, calculateSalesProjection, createComponentPlaceholder } = require('../../../../validations/front/component/componentValidation');
class ComponentValidationMiddleware {

    linkCodeSpacesToComponentValidation(req, res, next) {
        const { value, error } = linkCodeSpacesToComponent(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateComponentRepositoryValidation(req, res, next) {
        const { value, error } = updateComponentRepository(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    calculateSalesProjectionValidation(req, res, next) {
        const { value, error } = calculateSalesProjection(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createComponentPlaceholderValidation(req, res, next) {
        const { value, error } = createComponentPlaceholder(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new ComponentValidationMiddleware();