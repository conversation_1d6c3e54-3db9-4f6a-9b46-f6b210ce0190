# Command Line Instructions

You can also upload existing files from your computer using the instructions below.

## Git Global Setup

```bash
git config user.name "{{username}}"
git config user.email "{{email}}"
```

## Create Code Space Repository

```bash
git clone {{http_url_to_repo}}
cd {{project_name}}
git switch --create {{default_branch}}
touch README.md
git add README.md
git commit -m "add README"
git push --set-upstream origin {{default_branch}}
```


## Push an Existing Folder

```bash
cd existing_folder
git init --initial-branch={{default_branch}}
git remote add origin {{http_url_to_repo}}
git add .
git commit -m "Initial commit"
git push --set-upstream origin {{default_branch}}
```

## Push an Existing Git Repository

```bash
cd existing_repo
git remote rename origin old-origin
git remote add origin {{http_url_to_repo}}
git push --set-upstream origin --all
git push --set-upstream origin --tags
```