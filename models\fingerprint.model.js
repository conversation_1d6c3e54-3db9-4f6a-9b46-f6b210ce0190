const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define sub-schemas
const BrowserDetailsSchema = new Schema({
    browserName: String,
    browserMajorVersion: String,
    browserFullVersion: String,
    os: String,
    osVersion: String,
    device: String,
    userAgent: String
});

const GeolocationSchema = new Schema({
    accuracyRadius: Number,
    latitude: Number,
    longitude: Number,
    postalCode: String,
    timezone: String,
    city: {
        name: String
    },
    country: {
        code: String,
        name: String
    },
    continent: {
        code: String,
        name: String
    },
    subdivisions: [
        {
            isoCode: String,
            name: String
        }
    ]
});

const AsnSchema = new Schema({
    asn: String,
    name: String,
    network: String
});

const DatacenterSchema = new Schema({
    result: Boolean,
    name: String
});

const V4Schema = new Schema({
    address: String,
    geolocation: GeolocationSchema,
    asn: AsnSchema,
    datacenter: DatacenterSchema
});

const MethodsSchema = new Schema({
    timezoneMismatch: Boolean,
    publicVPN: Boolean,
    auxiliaryMobile: Boolean
});

const visitorSchema = new Schema({
    requestId: String, //Request id is unique for every request
    identification: {
        data: {
            visitorId: String,
            requestId: String,
            browserDetails: BrowserDetailsSchema,
            incognito: Boolean,
            ip: String,
            timestamp: Number,
            time: String,
            url: String,
            tag: {
                displayHeight: Number,
                displayWidth: Number,
                process: String
            },
            confidence: {
                score: Number
            },
            visitorFound: Boolean,
            firstSeenAt: {
                global: String,
                subscription: String
            },
            lastSeenAt: {
                global: String,
                subscription: String
            }
        }
    },
    botd: {
        data: {
            bot: {
                result: String
            },
            meta: {
                displayHeight: Number,
                displayWidth: Number,
                process: String
            },
            url: String,
            ip: String,
            time: String,
            userAgent: String,
            requestId: String
        }
    },
    rootApps: {
        data: {
            result: Boolean
        }
    },
    emulator: {
        data: {
            result: Boolean
        }
    },
    ipInfo: {
        data: {
            v4: V4Schema
        }
    },
    vpn: {
        data: {
            result: Boolean,
            originTimezone: String,
            originCountry: String,
            methods: MethodsSchema
        }
    },
    incognito: {
        data: {
            result: Boolean
        }
    },
    tampering: {
        data: {
            result: Boolean,
            anomalyScore: Number
        }
    },
    clonedApp: {
        data: {
            result: Boolean
        }
    },
    factoryReset: {
        data: {
            time: String,
            timestamp: Number
        }
    },
    jailbroken: {
        data: {
            result: Boolean
        }
    },
    frida: {
        data: {
            result: Boolean
        }
    },
    privacySettings: {
        data: {
            result: Boolean
        }
    },
    virtualMachine: {
        data: {
            result: Boolean
        }
    },
    locationSpoofing: {
        data: {
            result: Boolean
        }
    },
    suspectScore: {
        data: {
            result: Number
        }
    }
});

const visitorInformationSchema = new Schema({
    visits: [visitorSchema]
});

const fingerprintSchema = new Schema({
    userId: { //User id: This is the user id of the user who generated the fingerprint.
        type: mongoose.Types.ObjectId,
        required: true
    },
    fingerprintVisitorId: { //Fingerprint visitor id: This is the id of the fingerprint visitor. This is unique for every fingerprint visitor.
        type: String,
        required: true
    },
    visitorInformation: { //Visitor information: This is the visitor information of the fingerprint visitor.
        type: visitorInformationSchema,
        required: true
    },
    createdOn: { //Created on: This represents the time with millisecond precision when the fingerprint was created.
        type: Date,
        required: true,
        default: Date.now
    },
    updatedOn: { //Updated on: This represents the time with millisecond precision when the fingerprint was updated.
        type: Date,
        required: true,
        default: Date.now
    }
});

const Fingerprint = mongoose.model('fingerprint', fingerprintSchema);

module.exports = {
    Fingerprint
};