const express = require('express');
const router = express.Router();

const { getPopularElements, getPopularProjects, getFeaturedProjects, getAllElements, getPopularElementTags, getTopCreators, getElementsSearchSuggestions, getAllElementVariations, getAllCreators, getTopTechnologies, getAllTechnologies, getTechnologyWithProjects, getPopularProjectTags, getPublicCodeSpaces, getCollectionItems, getAllPublishedItems, getAllPopularTags, getAllMobileElements } = require('../../controller/front/homepage.controller');

router.get('/popular/elements', getPopularElements);

router.get('/popular/projects', getPopularProjects);

router.get('/featured/projects', getFeaturedProjects);

router.post('/elements/list', getAllElements);

router.get('/popular/element/tags', getPopularElementTags);

router.get('/top/creators', getTopCreators);

router.get('/elements/search/suggestions', getElementsSearchSuggestions);

router.post('/elements/:slug/variations', getAllElementVariations);

router.post('/browse-all/creators', getAllCreators);

router.get('/top/technologies', getTopTechnologies);

router.post('/browse-all/technologies', getAllTechnologies);

router.post('/technology/:slug/projects', getTechnologyWithProjects);

router.get('/popular/project/tags', getPopularProjectTags);

router.post('/public/code-spaces', getPublicCodeSpaces);

router.post('/collections/:slug/items', getCollectionItems);

router.post('/browse-all/published/items', getAllPublishedItems);

router.get('/popular/tags', getAllPopularTags);

router.post('/mobile-elements/list', getAllMobileElements);

module.exports = router;