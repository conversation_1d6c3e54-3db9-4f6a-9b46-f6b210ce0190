const express = require('express');
const router = express.Router();

const { updateComponentsV2, updateComponentsModifiers, updateComponentsModifiersToArray, updateComponentsState, updateCodeSpaceTechnologiesToArray, updateComponentCollectionWithStatistics, updateComponentCollectionWithTechnologies, updateUserCollectionWithComponentStatistics, addCollaboratorCollectionOwnerRole, updateGitlabRepoDomain, createProjectForPublicCodeSpace, regenerateElementsThumbnails, migrateRepositoryStructureFromRedisToMongoDB, fetchAndSyncUserUsedStorage } = require('../../controller/cms/migration.controller');


router.post('/update-components-v2', updateComponentsV2);
router.post('/update-components/modifiers', updateComponentsModifiers);
router.post('/update-components/modifiers/multiple', updateComponentsModifiersToArray);
router.post('/update-components/component-state', updateComponentsState);
router.post('/update-code-space/technologies', updateCodeSpaceTechnologiesToArray);
router.post('/update-components/statistics', updateComponentCollectionWithStatistics);
router.post('/update-components/technologies', updateComponentCollectionWithTechnologies);
router.post('/update-users/component-state', updateUserCollectionWithComponentStatistics);
router.post('/add-collaborator/owner-role', addCollaboratorCollectionOwnerRole);
router.post('/update-repo-domain', updateGitlabRepoDomain);
router.post('/update-public-code-spaces', createProjectForPublicCodeSpace);
router.post('/generate/elements/thumbnails', regenerateElementsThumbnails);
router.post('/repository/structure-redis', migrateRepositoryStructureFromRedisToMongoDB);
router.post('/sync/user-storage', fetchAndSyncUserUsedStorage);

module.exports = router;