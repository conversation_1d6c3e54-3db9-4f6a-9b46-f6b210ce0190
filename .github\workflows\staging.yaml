name: Multiplatform to Staging

on:
  push:
    branches:
      - staging
  pull_request:
    branches:
      - staging


env:
  IMAGE_TAG: ${{github.sha}}
  ECR_REPOSITORY: multiplatform_backend

jobs:
  build:
    runs-on: self-hosted
    environment: stage
    if: ( github.event_name == 'push' && github.ref == 'refs/heads/staging' )
    steps:
    - name: Checkout Code
      uses: actions/checkout@v2
      with:
        ref: ${{ github.ref }}

    - name: Set up AWS CLI
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}

    - name: Login to AWS ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build and push to AWS ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
      run: |
        aws s3 cp s3://chaintech-env/chaintech/multiplatform-backend/.env .env
        docker build -f Dockerfile -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker build -f Dockerfile -t $ECR_REGISTRY/$ECR_REPOSITORY:stage .
        docker push -a $ECR_REGISTRY/$ECR_REPOSITORY

  deploy:
    needs: build
    environment: stage
    runs-on: self-hosted
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
          
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Login to AWS ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      # - name: Get EC2 key file from secrets
      #   run: |
      #     mkdir $HOME/.ssh
      #     echo "${{ secrets.EC2_KEY_FILE_MAIN }}" > $HOME/.ssh/bcsn.pem
      #     cd $HOME/.ssh
      #     ls -la

      - name: Deploy to EC2
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          # chmod 400 $HOME/.ssh/bcsn.pem
          ssh -o StrictHostKeyChecking=no -i $HOME/.ssh/bcsn.pem ubuntu@${{ secrets.STAGE_SERVER_IP }} 'bash -s' < ./deploy-stage.sh $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY $ECR_REPOSITORY
