const express = require('express');
const router = express.Router();

const { addInterestedUser, getInterestedUsers } = require('../../controller/front/interestedUsers.controller');

const { addUserInterestValidation } = require('../../middlewares/validations/front/auth/authValidation');

router.post('/submit', addUserInterestValidation, addInterestedUser);
router.get('/get-interested-users', getInterestedUsers);

module.exports = router;