const Joi = require('joi');
class SectionValidation {
    createSection(params) {
        const schema = Joi.object({
            title: Joi.string().required(),
            is_active: Joi.boolean().optional(),
            description: Joi.string().optional(),
            editor_type: Joi.string().required(),
            image_url: Joi.string().required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updateSection(params) {
        const schema = Joi.object({
            title: Joi.string().optional(),
            is_active: Joi.boolean().optional(),
            description: Joi.string().optional(),
            editor_type: Joi.string().required(),
            image_url: Joi.string().optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new SectionValidation();