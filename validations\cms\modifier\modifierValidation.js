const Joi = require('joi');
class ModifierValidation {
    createModifier(params) {
        const schema = Joi.object({
            title: Joi.string().required(),
            platform_id: Joi.array().items(Joi.string().trim()).min(1).unique().required(),
            is_active: Joi.boolean().optional(),
            description: Joi.string().required(),
            selected_option: Joi.string().required(),
            options: Joi.array().required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updateModifier(params) {
        const schema = Joi.object({
            title: Joi.string().optional(),
            platform_id: Joi.array().items(Joi.string().trim()).min(1).unique().optional(),
            is_active: Joi.boolean().optional(),
            description: Joi.string().optional(),
            selected_option: Joi.string().optional(),
            options: Joi.array().optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new ModifierValidation();