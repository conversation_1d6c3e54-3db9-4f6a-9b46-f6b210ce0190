const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const SettingsSchema = new Schema({
    setting_name: {
        type: String,
        required: true
    },
    setting_slug: {
        type: String,
        required: true,
        unique: true
    },
    version: {
        type: Number,
        default: 1
    },
    values: {
        type: Object
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const Settings = mongoose.model('settings', SettingsSchema);

module.exports = {
    Settings
};