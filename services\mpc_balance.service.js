const { UserMpcBalance } = require('../models/user_mpc_balance.model');
const { UserCoinsHistory } = require('../models/user_coins_history.model');
const { UserMpnPointLogs } = require('../models/user_mpn_point_logs.model');
const { AdminMpnPointLogs } = require('../models/admin_mpn_point_logs.model');
const { AdminMpnPointWallet } = require('../models/admin_mpn_point_wallet.model');
const { getWelcomeBonusCoinsConfig } = require('../services/settings.service');
const { coinTypes, pointActivity, pointActivityDescription } = require('../config/component.constant');
const moment = require('moment');
const constants = require('../config/constants');
const logger = require('../config/logger');


/**
 * Adds sign-up bonus to the user's wallet and updates default site settings.
 * @param {string} user_id - The ID of the user.
 */
const addSignUpBonusToWallet = async (user_id) => {
    try {
        // Check if bonus already allotted
        const existingBonus = await UserCoinsHistory.findOne({
            user_id: user_id,
            type: coinTypes.BONUS
        });

        if (existingBonus) {
            console.log(`Bonus already allotted for user_id: ${user_id}`);
            return; // Exit early if bonus already given
        }
        // Retrieve welcome bonus coins configuration   
        const settingsConfig = await getWelcomeBonusCoinsConfig();

        // Get the current date
        const currentDate = moment();

        // Calculate the expiry date by adding the configured duration to the current date
        const expiredOn = currentDate.add(settingsConfig.expiry_duration, 'days');

        // Create user coins history object
        const userCoinsHistoryObj = {
            user_id: user_id,
            points: settingsConfig.bonus_coins,
            pending_points: settingsConfig.bonus_coins,
            type: coinTypes.BONUS,
            expired_on: expiredOn,
            is_expired: false
        };

        // Create user coins history record
        await UserCoinsHistory.create(userCoinsHistoryObj);

        const userPointLogsObj = {
            user_id: user_id,
            points: settingsConfig.bonus_coins,
            activity: pointActivity.EARNING,
            description: pointActivityDescription.SIGNUP_BONUS
        };
        // Create user mpn point logs record
        await UserMpnPointLogs.create(userPointLogsObj);
        // Create MPC balance object
        const mpcBalanceObj = {
            user_id: user_id,
            mpn_points: 0,
            mpn_bonus_points: settingsConfig.bonus_coins,
            fiat: 0
        };
        // Create user MPC balance record
        await UserMpcBalance.create(mpcBalanceObj);
    } catch (error) {
        // Log error if any
        console.error('Error from MPC balance service function addSignUpBonusToWallet', error);
        throw error;
    }
};

const manageComponentPurchase = async (purchaserId, component_id, component, creatorId) => {
    try {
        // Find the user's MPC balance
        const userMpcBalance = await UserMpcBalance.findOne({ user_id: purchaserId }).lean();

        // Check if user exists and has enough points
        if (!userMpcBalance) {
            const err = new Error('User not found or has no point balance.');
            err.statusCode = constants.payment_required_code;
            throw err;
        }

        // Prepare wallet balance for response
        const walletBalance = {
            fiat: userMpcBalance?.fiat || 0,
            mpn_points: userMpcBalance?.mpn_points || 0,
            mpn_bonus_points: userMpcBalance?.mpn_bonus_points || 0
        };

        const purchasePrice = component?.purchase_price;
        const itemPrice = component?.item_price;
        const buyerFee = component?.buyer_fee;

        // Calculate total available coins for the user
        const totalAvailablePoints = walletBalance.mpn_bonus_points + walletBalance.mpn_points;

        // Check if user has enough coins to unlock the component
        if (totalAvailablePoints < purchasePrice.mpn_points) {
            const err = new Error('Insufficient points to purchase this item.');
            err.statusCode = constants.payment_required_code;
            throw err;
        }

        // Find user's coins history
        const userCoins = await UserCoinsHistory.find({
            user_id: purchaserId,
            is_expired: false,
            pending_points: { $gt: 0 }
        }).sort({ type: -1, expired_on: 1 }).lean();

        if (!userCoins.length) {
            const err = new Error('User has no available point balance.');
            err.statusCode = constants.payment_required_code;
            throw err;
        }

        let componentCoin = purchasePrice.mpn_points;

        // Iterate through user's coins to deduct the required amount
        for (const userCoin of userCoins) {
            if (componentCoin != 0) {
                let pendingCoins = userCoin.pending_points;
                if (componentCoin > pendingCoins) {
                    // Deduct coins from user's pending balance
                    componentCoin = componentCoin - pendingCoins;
                    await deductPointFromMasterBalance(purchaserId, pendingCoins, userCoin.type);
                    pendingCoins = 0;
                } else {
                    // Deduct coins from user's pending balance
                    const newPendingCoins = pendingCoins - componentCoin;
                    pendingCoins = newPendingCoins;
                    await deductPointFromMasterBalance(purchaserId, componentCoin, userCoin.type);
                    componentCoin = 0;
                }
                // Update user's coins history
                await UserCoinsHistory.updateOne({ _id: userCoin._id }, { $set: { pending_points: pendingCoins } });
            }
        }

        // Log purchaser's expense in UserMpnPointLogs
        await createUserMpnPointLog(purchaserId, purchasePrice.mpn_points, pointActivity.EXPENSE, component_id, pointActivityDescription.UNLOCK_EXPENSE);

        // Credit points to the creator's master balance
        await creditPointIntoMasterBalance(creatorId, itemPrice.mpn_points, coinTypes.NORMAL);

        // Update UserCoinsHistory for the creator
        await createUserCoinHistory(creatorId, itemPrice.mpn_points, coinTypes.NORMAL);

        // Log creator's credit in UserMpnPointLogs
        await createUserMpnPointLog(creatorId, itemPrice.mpn_points, pointActivity.EARNING, component_id, pointActivityDescription.UNLOCK_EARNING);

        // Credit buyer fee points to 
        await creditPointToAdminMasterBalance(purchaserId, buyerFee.mpn_points, component_id);

        return {
            expense: {
                fiat: 0,
                mpn_points: purchasePrice.mpn_points
            },
            earning: {
                fiat: 0,
                mpn_points: itemPrice.mpn_points
            }
        };
    } catch (error) {
        // Propagate any errors
        throw error;
    }
};


/**
 * Deducts points from the master balance of a user.
 * @param {string} user_id - The ID of the user whose balance is to be deducted.
 * @param {number} points - The number of points to deduct.
 * @param {string} type - The type of points being deducted (e.g., 'BONUS', 'NORMAL').
 */
const deductPointFromMasterBalance = async (user_id, points, type) => {
    try {
        // Determine the key for deduction based on the type of points
        const deductionKey = (type == coinTypes.BONUS) ? 'mpn_bonus_points' : 'mpn_points';

        // Prepare update query
        const updateQuery = {
            $inc: {}
        };

        // Specify the deduction amount for the corresponding key
        updateQuery['$inc'][deductionKey] = parseInt(`-${points}`);

        // Update the user's balance
        await UserMpcBalance.updateOne({ user_id: user_id }, updateQuery);
        console.log(`Deducted ${type} ${points} from master balance for user ${user_id} successfully`);
    } catch (error) {
        // Log and rethrow any errors that occur during the process
        logger.error(`Error while deducting points from master balance${error}`);
        throw error;
    }
};

const creditPointIntoMasterBalance = async (user_id, points, type) => {
    try {
        // Determine the key for credit based on the type of points
        const creditKey = (type == coinTypes.BONUS) ? 'mpn_bonus_points' : 'mpn_points';

        // Prepare update query
        const updateQuery = {
            $inc: {}
        };

        // Specify the deduction amount for the corresponding key
        updateQuery['$inc'][creditKey] = parseInt(points);

        // Update the user's balance
        await UserMpcBalance.updateOne({ user_id: user_id }, updateQuery);
        console.log(`Credited ${points} in master balance for user ${user_id} successfully`);
    } catch (error) {
        // Log and rethrow any errors that occur during the process
        logger.error(`Error while credit points into master balance ${error}`);
        throw error;
    }
};

const createUserMpnPointLog = async (userId, points, activity, componentId, description) => {
    try {
        const userPointLogsObj = {
            user_id: userId,
            points,
            activity,
            component_id: componentId,
            description: description
        };
        await UserMpnPointLogs.create(userPointLogsObj);
        console.log(`MPN point log was successfully created for user ${userId} for activity ${activity}.`);
    } catch (error) {
        // Log and rethrow any errors that occur during the process
        logger.error(`Error while create user mpn point log ${error}`);
        throw error;
    }
};

const createUserCoinHistory = async (userId, points, coinType) => {
    try {
        const creatorCoinsHistory = {
            user_id: userId,
            points: points,
            pending_points: points,
            type: coinType,
            is_expired: false
        };
        await UserCoinsHistory.create(creatorCoinsHistory);
        console.log(`MPN coin history created successfully for user ${userId} with points ${points}.`);
    } catch (error) {
        // Log and rethrow any errors that occur during the process
        logger.error(`Error while credit points into master balance ${error}`);
        throw error;
    }
};

const creditPointToAdminMasterBalance = async (userId, points, componentId) => {
    try {
        // Perform both operations in parallel to optimize execution time
        await Promise.all([
            AdminMpnPointWallet.updateOne({},
                { $inc: { points: points } },
                { upsert: true } // Creates the document if it doesn't exist
            ),
            AdminMpnPointLogs.create({
                user_id: userId,
                points,
                component_id: componentId,
                activity: pointActivity.EARNING
            })
        ]);
    } catch (error) {
        // Enhanced error logging with function parameters for better debugging
        logger.error(`Error while crediting points to admin master balance. User: ${userId}, Points: ${points}, Component: ${componentId}, Error: ${error}`);
        throw error;
    }
};


const acceptBonusRequests = async (user_id, mpc_bonus) => {
    try {
        // Retrieve welcome bonus coins configuration from database
        const settingsConfig = await getWelcomeBonusCoinsConfig();

        // Get the current date and time
        const currentDate = moment();

        // Calculate the expiry date for the bonus coins by adding the configured duration to the current date
        const expiredOn = currentDate.add(settingsConfig.expiry_duration, 'days');

        // Prepare an object to store user coins history
        const userCoinsHistoryObj = {
            user_id: user_id,
            points: mpc_bonus,
            pending_points: mpc_bonus,
            type: coinTypes.BONUS,
            expired_on: expiredOn,
            is_expired: false
        };

        // Create a record for user coins history in the database
        await UserCoinsHistory.create(userCoinsHistoryObj);

        // Update the user's MPC balance by adding the bonus coins
        await UserMpcBalance.updateOne({
            user_id: user_id
        }, {
            $inc: {
                mpc_bonus_coins: mpc_bonus
            }
        });
    } catch (error) {
        // If any error occurs during the process, log it and rethrow the error
        logger.error(`Error while accepting bonus requests${error}`);
        throw error;
    }
};

const calculateComponentUnlock = async (purchaserId, component, fiatOnly = false) => {
    try {
        // Fetch the user's MPC balance from the database
        const userMpcBalance = await UserMpcBalance.findOne({ user_id: purchaserId }).lean();

        // Validate user existence and ensure they have an MPC balance
        if (!userMpcBalance) {
            throw Object.assign(new Error('User not found or has no MPC balance.'), {
                statusCode: constants.payment_required_code
            });
        }

        // Prepare wallet balance for response
        const walletBalance = {
            fiat: userMpcBalance?.fiat || 0,
            mpn_points: userMpcBalance?.mpn_points || 0,
            mpn_bonus_points: userMpcBalance?.mpn_bonus_points || 0
        };

        const purchasePrice = component?.purchase_price;
        const parity = component?.mpn_parity;

        // Handle fiat-only transactions
        if (fiatOnly == true || fiatOnly == 'true') {
            const remainingFiat = parseFloat(purchasePrice.fiat) - parseFloat(walletBalance.fiat);
            return {
                project_price: purchasePrice,
                wallet_balance: walletBalance,
                net_payable_fiat: Math.max(remainingFiat, 0),
                processing_fee: 0,
                mpn_points_applied: 0
            };
        }

        // Calculate total available points (bonus + regular)
        const totalPoints = parseFloat(walletBalance.mpn_bonus_points) + parseFloat(walletBalance.mpn_points);

        // Determine the remaining points after the transaction
        const remainingPoints = Math.max(parseFloat(purchasePrice.mpn_points) - parseFloat(totalPoints), 0);

        const appliedPoints = remainingPoints <= 0 ? parseFloat(purchasePrice.mpn_points) : parseFloat(totalPoints);


        // Calculate any remaining fiat required based on the remaining points
        const netPayableFiat = remainingPoints > 0 ? parseFloat(walletBalance.fiat) - (remainingPoints / parity) : parseFloat(walletBalance.fiat);

        // Helper function to format numbers to 2 decimal places
        const formatToTwoDecimals = (num) => parseFloat(num.toFixed(2));

        // Return the transaction response
        return {
            project_price: purchasePrice,
            wallet_balance: walletBalance,
            net_payable_fiat: formatToTwoDecimals(Math.abs(Math.min(netPayableFiat, 0))),
            processing_fee: 0,
            mpn_points_applied: appliedPoints
        };
    } catch (error) {
        // Re-throw errors for handling at a higher level
        throw error;
    }
};


module.exports = {
    addSignUpBonusToWallet,
    manageComponentPurchase,
    deductPointFromMasterBalance,
    acceptBonusRequests,
    creditPointIntoMasterBalance,
    createUserMpnPointLog,
    calculateComponentUnlock,
    createUserCoinHistory,
    creditPointToAdminMasterBalance
};