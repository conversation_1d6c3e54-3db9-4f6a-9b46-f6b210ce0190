const Joi = require('joi');
class TagValidation {
    createTag(value) {
        const schema = Joi.object({
            title: Joi.string().trim().required(),
            description: Joi.string().trim().required(),
            is_active: Joi.boolean().optional()
        });
        return schema.validate(value);
    }

    updateTag(value) {
        const schema = Joi.object({
            title: Joi.string().trim().optional(),
            description: Joi.string().trim().optional(),
            is_active: Joi.boolean().optional()
        });
        return schema.validate(value);
    }

    updateTagStatus(value) {
        const schema = Joi.object({
            is_active: Joi.boolean().required()
        });
        return schema.validate(value);
    }
}

module.exports = new TagValidation();