const express = require('express');
const router = express.Router();

const { createPermissionGroupValidation, updatePermissionGroupStatusValidation, updatePermissionGroupValidation } = require('../../middlewares/validations/cms/permission_groups/permissionGroupsValidation');

const { createPermissionGroup, getAllPermissionGroups, updatePermissionGroupStatus, getAllPermissionGroupsSortList, getPermissionGroupDetails, updatePermissionGroup } = require('../../controller/cms/adminPermissionGroup.controller');

router.post('/create', createPermissionGroupValidation, createPermissionGroup);
router.post('/list', getAllPermissionGroups);
router.put('/update/:id', updatePermissionGroupStatusValidation, updatePermissionGroupStatus);
router.get('/sort-list', getAllPermissionGroupsSortList);
router.get('/details/:id', getPermissionGroupDetails);
router.put('/update/:id', updatePermissionGroupValidation, updatePermissionGroup);

module.exports = router;