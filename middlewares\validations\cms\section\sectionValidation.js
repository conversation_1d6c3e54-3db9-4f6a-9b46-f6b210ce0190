const { createSection, updateSection } = require('../../../../validations/cms/section/sectionValidation');
class ModifierValidationMiddleware {
    createSectionValidation(req, res, next) {
        const { value, error } = createSection(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateSectionValidation(req, res, next) {
        const { value, error } = updateSection(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new ModifierValidationMiddleware();