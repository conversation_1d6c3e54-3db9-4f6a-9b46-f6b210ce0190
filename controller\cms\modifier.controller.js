const constants = require('../../config/constants');
const logger = require('../../config/logger');

const { ReS, generateSlug, escapeRegex, sendError } = require('../../services/general.helper');

const Modifiers = require('../../models/modifier.model').Modifiers;

const mongoose = require('mongoose');


async function createModifier(req, res) {
    try {
        const {
            title,
            platform_id,
            description,
            selected_option,
            options
        } = req.body;

        const modifierSlug = generateSlug(title);

        await Modifiers.create({
            title: title,
            platform_id: platform_id,
            slug: modifierSlug,
            description: description,
            selected_option: selected_option,
            options: options,
            created_by: req.session._id
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createModifier${err}`);
        return sendError(res, err);
    }
}

async function updateModifier(req, res) {
    try {

        const postData = req.body;

        const modifierData = await Modifiers.findOne({
            _id: req.params.id
        });
        if (modifierData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Modifier Not Found.');
        }
        if (postData.title) {
            postData['slug'] = generateSlug(postData.title);
        }
        postData['updated_by'] = req.session._id;
        await Modifiers.updateOne({
            _id: req.params.id
        }, {
            '$set': postData
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateModifier${err}`);
        return sendError(res, err);
    }
}

async function getAllModifier(req, res) {
    try {
        const totalDocuments = await Modifiers.countDocuments();
        // Set default conditions
        const conditions = {};
        // Set default sort
        const sort = {
            'created_at': -1
        };
        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if(req.body.platformIds){
            const arrPlatformIds = req.body.platformIds.map((e) => {return new mongoose.Types.ObjectId(e);});
            conditions['platform_id'] = {
                $in: arrPlatformIds
            };
        }

        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        const filterDocuments = await Modifiers.countDocuments(conditions);
        const query = [{
            $match: conditions
        },{
            $lookup:{
                from: 'supported_platforms',
                localField: 'platform_id',
                foreignField: '_id',
                as: 'platform_id'
            }
        }, {
            $project: {
                title: 1,
                platform_id: 1,
                slug: 1,
                image_url: 1,
                description: 1,
                created_at: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });
        const modifierList = await Modifiers.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: modifierList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllModifier${err}`);
        return sendError(res, err);
    }
}

async function getModifierDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            '_id': req.params.id
        };
        const modifierData = await Modifiers.findOne(conditions)
            .populate({
                'path': 'platform_id',
                'select': 'title slug image_url'
            })
            .populate({
                'path': 'options.section_id',
                'select': 'title slug editor_type'
            }).lean();
        if (!modifierData) {
            return ReS(res, constants.resource_not_found, 'Oops! Modifier Not Found.');
        }
        return ReS(res, constants.success_code, 'Data Fetched', modifierData);
    } catch (err) {
        logger.error(`Error at CMS Controller getModifierDetails${err}`);
        return sendError(res, err);
    }
}

async function getModifierDetailList(req, res) {
    try {
        // Set default conditions
        const conditions = {
            '_id': {
                $in: req.body.modifiers
            }
        };
        const modifierData = await Modifiers.find(conditions)
            .populate({
                'path': 'platform_id',
                'select': 'title slug image_url'
            })
            .populate({
                'path': 'options.section_id',
                'select': 'title slug editor_type'
            }).lean();
        return ReS(res, constants.success_code, 'Data Fetched', modifierData);
    } catch (err) {
        logger.error(`Error at CMS Controller getModifierDetailList${err}`);
        return sendError(res, err);
    }
}

async function getAllModifierSortList(req, res) {
    try {
        // Set default conditions
        const conditions = {};
        // Set default sort
        const sort = {
            'created_at': -1
        };
        if (req.query.platform_id) {
            conditions['platform_id'] = new mongoose.Types.ObjectId(req.query.platform_id);
        }
        if (req.query.title) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.query.title);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                platform_id: 1,
                slug: 1,
                image_url: 1,
                description: 1,
                created_at: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        const modifierList = await Modifiers.aggregate(query);
        return ReS(res, constants.success_code, 'Data Fetched', modifierList);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllModifierSortList${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createModifier,
    updateModifier,
    getAllModifier,
    getModifierDetails,
    getAllModifierSortList,
    getModifierDetailList
};