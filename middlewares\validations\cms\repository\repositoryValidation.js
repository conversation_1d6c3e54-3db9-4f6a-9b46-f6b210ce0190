const { createRepository, createCommit, createCommitWithFiles } = require('../../../../validations/cms/repository/repositoryValidation');

class RepositoryValidationMiddleware {
    createRepositoryValidation(req, res, next) {
        const { value, error } = createRepository(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createCommitValidation(req, res, next) {
        const { value, error } = createCommit(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createCommitWithFilesValidation(req, res, next) {
        const { value, error } = createCommitWithFiles(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new RepositoryValidationMiddleware();