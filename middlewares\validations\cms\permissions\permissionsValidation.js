const { createPermission, updatePermissionStatus } = require('../../../../validations/cms/permissions/permissionsValidation');

class PermissionValidationMiddleware {
    createPermissionValidation(req, res, next) {
        const { value, error } = createPermission(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updatePermissionStatusValidation(req, res, next) {
        const { value, error } = updatePermissionStatus(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new PermissionValidationMiddleware();