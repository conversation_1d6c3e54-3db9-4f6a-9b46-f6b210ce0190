// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, sendError, escapeRegex } = require('../../services/general.helper');
const createSlug = require('../../services/slug.service');

// Models declaration
const StaticPage = require('../../models/static_page.model').StaticPage;

async function createStaticPage(req, res) {
    try {
        const {
            title,
            description
        } = req.body;
        const slug = createSlug(title);
        // Check if the page with provided slug already exists
        const staticPage = await StaticPage.findOne({ slug }).lean();

        if (staticPage) {
            // If slug already exists, return conflict status with user details
            return ReS(res, constants.accepted_code, 'Oops! page already registered', {
                page_id: staticPage._id, slug: staticPage.slug
            });
        }


        await StaticPage.create({
            title: title,
            slug: slug,
            description: description
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createStaticPage${err}`);
        return sendError(res, err);
    }
}

async function getSingleStaticPage(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };

        const pageData = await StaticPage.findOne(filter).lean();

        if (pageData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Page Not Found.');
        }

        return ReS(res, constants.success_code, 'Data Fetched', pageData);
    } catch (err) {
        logger.error(`Error at CMS Controller getSingleStaticPage${err}`);
        return sendError(res, err);
    }
}

async function getStaticPages(req, res) {
    try {
        // Get total count of documents
        const totalDocuments = await StaticPage.countDocuments();
        // Set default conditions
        const conditions = {};
        // Set default sort
        const sort = {
            'created_at': -1
        };
        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        const filterDocuments = await StaticPage.countDocuments(conditions);
        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                slug: 1,
                description: 1,
                created_at: 1,
                is_active: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });
        // Query for a page of data
        const pageList = await StaticPage.aggregate(query);
        return ReS(res, constants.success_code, 'Data Fetched', {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: pageList
        });
    } catch (err) {
        logger.error(`Error at CMS Controller getStaticPages${err}`);
        return sendError(res, err);
    }
}

async function updateStaticPage(req, res) {
    try {
        const { title, description } = req.body;

        let slug;
        if (title) {
            slug = createSlug(title);
        }
        const filter = {
            _id: req.params.id
        };

        const pageData = await StaticPage.findOne(filter).lean();

        if (pageData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Page Not Found.');
        }

        await StaticPage.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                title: title,
                description: description,
                slug: slug
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateStaticPage${err}`);
        return sendError(res, err);
    }
}

async function deleteStaticPage(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };

        const pageData = await StaticPage.findOne(filter).lean();

        if (pageData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Page Not Found.');
        }

        await StaticPage.deleteOne(filter).then((data) => {
            return ReS(res, constants.success_code, 'Success', data);
        }).catch((err) => {
            throw new Error(err);
        });

    } catch (err) {
        logger.error(`Error at CMS Controller deleteStaticPage${err}`);
        return sendError(res, err);
    }
}

async function updateStaticPageStatus(req, res) {
    try {
        const { is_active } = req.body;


        const filter = {
            _id: req.params.id
        };

        const pageData = await StaticPage.findOne(filter).lean();

        if (pageData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Page Not Found.');
        }

        await StaticPage.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateStaticPageStatus${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createStaticPage,
    getSingleStaticPage,
    getStaticPages,
    updateStaticPage,
    deleteStaticPage,
    updateStaticPageStatus
};