/* eslint-disable indent */
// Npm declaration
const mongoose = require('mongoose');
const archiver = require('archiver');

// Models declaration
const DraftComponents = require('../../models/draft_components.model').DraftComponents;
const Components = require('../../models/component.model').Components;
const ComponentDownloadHistory = require('../../models/component_download_history.model').ComponentDownloadHistory;
const Category = require('../../models/category.model').Category;

// Service declaration
const constants = require('../../config/constants');
const { ReS, sendError, generateSlug, uploadBufferToS3 } = require('../../services/general.helper');
const logger = require('../../config/logger');
const { componentType, componentState, elementFileNames, thumbnailStatus, thumbnailSource } = require('../../config/component.constant');
const { checkAndManageComponentState, injectAssetsIntoHTML, updateComponentDownloadCount, fetchDefaultLicense } = require('../../services/component.service');
const { sendNotificationOnPublishElement } = require('../../services/notification.service');
const { html2Screenshot } = require('../../services/elements.service');

// Validations
const { publishElementValidation } = require('../../validations/cms/component/componentValidation');

const { gifQueue } = require('../../queues/gifQueue');


async function createElementPlaceHolder(req, res) {
    try {
        const { title = 'Untitled', component_type = componentType.ELEMENTS, category_id } = req.body;

        const elementSlug = generateSlug(title);

        const element_id = new mongoose.Types.ObjectId();

        const categoryData = await Category.findOne({ _id: category_id }, '_id').lean();

        if (!categoryData) {
            return ReS(res, constants.resource_not_found, 'Oops! Category Not Found.');
        }

        const newElement = await DraftComponents.create({
            _id: element_id,
            title: title,
            slug: elementSlug,
            component_state: componentState.PLACEHOLDER,
            created_by_user: req.session._id,
            component_type: component_type,
            category_id: category_id
        });

        return ReS(res, constants.success_code, 'Success', {
            element_id: newElement._id
        });
    } catch (err) {
        logger.error(`Error at Front Controller createElementPlaceHolder ${err}`);
        return sendError(res, err);
    }
}

async function updateElement(req, res) {
    try {
        const postData = req.body;
        // Fetch the element data based on the component ID from the request parameters
        const element = await DraftComponents.findOne({
            _id: req.params.id,
            component_type: componentType.ELEMENTS
        }, 'component_type component_state').lean();

        // Check if the element exists
        if (!element) {
            return ReS(res, constants.resource_not_found, 'Oops! Element Not Found.');
        }

        // Generate a slug if the title is provided in postData
        if (postData.title) {
            postData.slug = generateSlug(postData.title);
        }

        // No platform data is present, so check and potentially manage the element's state
        const isElementStateChanged = await checkAndManageComponentState(req.params.id, postData);
        // If the element state was changed by the previous operation, update the state to ACTIVE_DRAFT
        if (isElementStateChanged) {
            postData.component_state = componentState.ACTIVE_DRAFT;
        }

        // Set the 'updated_by_user' field to the current session's admin ID
        postData.updated_by_user = req.session._id;

        // Check if "is_paid" property is explicitly set to false
        if (postData?.is_paid === false) {
            // Fetch the default license details from the database
            const defaultLicense = await fetchDefaultLicense();
            // Assign the default license ID to the "license_id" property of postData
            postData.license_id = defaultLicense._id;
        }

        const updateQuery = { '$set': postData };

        // Update the element with the new data
        await DraftComponents.updateOne({ _id: req.params.id }, updateQuery);

        // Respond with success
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at Front Controller updateElement: ${err}`);
        return sendError(res, err);
    }
}

async function getDraftElementDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            '_id': req.params.id,
            component_type: componentType.ELEMENTS,
            created_by_user: req.session._id
        };

        const element = await DraftComponents.findOne(conditions)
            .populate({
                'path': 'category_id',
                'select': 'category_name category_slug image_url'
            }).populate({
                'path': 'created_by_user',
                'select': 'first_name last_name username email avatar'
            }).lean();

        if (!element) {
            return ReS(res, constants.resource_not_found, 'Oops! Element Not Found.');
        }

        const publishedElement = await Components.findOne({
            component_draft_id: element._id,
            'component_state': {
                $in: [componentState.PUBLISHED, componentState.PRIVATE]
            }
        }, '_id title slug component_state').populate({
            'path': 'created_by_user',
            'select': 'first_name last_name username email avatar'
        }).lean();

        // If any version is live of this component attach published component details
        if (publishedElement) {
            element['published_component'] = publishedElement;
        }

        // Check if any version is live of this component or not
        element['is_published'] = (element) ? true : false;

        return ReS(res, constants.success_code, 'Data Fetched', element);
    } catch (err) {
        logger.error(`Error at Front Controller getDraftElementDetails ${err}`);
        return sendError(res, err);
    }
}

async function publishDraftElements(req, res) {
    try {
        const componentId = req.params.id;

        const { publish_reference } = req.body;

        const conditions = {
            _id: componentId,
            created_by_user: req.session._id,
            component_type: componentType.ELEMENTS
        };

        // Fetch the element by ID with selected fields
        const element = await DraftComponents.findOne(conditions).lean();

        // Return an error response if the element is not found
        if (!element) {
            return ReS(res, constants.resource_not_found, 'Oops! Element Not Found.');
        }

        // Validate element data before publishing
        const { error } = publishElementValidation(element);
        if (error) {
            return ReS(res, constants.bad_request_code, error.details[0].message, 'ValidationError');
        }

        // Check for a unique slug across non-placeholder element
        const existingElement = await Components.findOne({
            slug: element.slug,
            component_draft_id: { $ne: element._id }
        }).lean();

        // Return an error if a element with the same slug already exists
        if (existingElement) {
            return ReS(res, constants.conflict_code, `Oops! Element with title ${element.title} already published.`);
        }

        // Ensure that at least one code-files is linked to the element
        if (!element.elements_data || Object.keys(element.elements_data).length == 0) {
            return ReS(res, constants.bad_request_code, 'Oops! No Linked Code files Found.');
        }

        // Prepare element data object that would be transferred from daft to published
        const elementObj = {
            title: element?.title,
            slug: element?.slug,
            short_description: element?.short_description,
            long_description: element?.long_description,
            thumbnail_url: element?.thumbnail_url,
            elements_data: element?.elements_data,
            created_by_user: element?.created_by_user,
            updated_by_user: element?.updated_by_user,
            category_id: element?.category_id,
            component_type: element?.component_type,
            orientation: element?.orientation,
            difficulty_level: element?.difficulty_level,
            identification_tag: element?.identification_tag,
            is_paid: element?.is_paid,
            component_state: componentState.PUBLISHED,
            design_url: element?.design_url,
            currency: element?.currency,
            location: element?.location,
            variant_id: element?.variant_id,
            source_id: element?.source_id,
            publish_reference: publish_reference,
            live_preview: element?.live_preview,
            element_container_meta: element?.element_container_meta,
            license_id: element?.license_id,
            last_published_at: new Date(),
            linked_output: element?.linked_output,
            gif_status: thumbnailStatus.PENDING,
            thumbnail_source: element?.thumbnail_source,
            image_url: element?.image_url
        };
        // Check if combined html found if yes then generate screenshot and save
        if (element?.thumbnail_source === thumbnailSource.AUTO && element?.linked_output) {
            // Convert HTML content to screenshot buffer
            const screenshotBuffer = await html2Screenshot(element?.linked_output);
            // Prepare file details
            const fileExtension = 'png'; const mimeType = 'image/png';
            const filename = `${new Date().getTime()}.${fileExtension}`;
            const documentPath = `assets/${filename}`;
            // Upload the screenshot buffer to S3
            const uploadedFile = await uploadBufferToS3(screenshotBuffer, documentPath, mimeType);
            console.log(`Screenshot captured successfully with path => ${uploadedFile}`);
            elementObj['image_url'] = documentPath;
        }
        // Update the component's state to published
        const publishedElement = await Components.findOneAndUpdate({
            component_draft_id: element._id
        }, {
            $set: elementObj,
            $setOnInsert: {
                createdOn: new Date()
            },
            $inc: {
                published_count: 1
            }
        }, {
            upsert: true,
            new: true
        }).lean();

        // Updated draft component status to published
        await DraftComponents.updateOne({
            _id: element._id
        }, {
            $set: {
                component_state: componentState.PUBLISHED
            }
        });

        await sendNotificationOnPublishElement(publishedElement._id, req.session._id);
        if (element?.thumbnail_source === thumbnailSource.AUTO) {
            try {
                await gifQueue.add('generate-gif', { elementId: publishedElement._id }, {
                    attempts: 3,
                    backoff: { type: 'exponential', delay: 5000 },
                    removeOnComplete: true
                });
            } catch (error) {
                console.log(`Error while adding new job to gifQueue for element => ${publishedElement._id}`);
            }
        }
        // Return a success response
        return ReS(res, constants.success_code, 'Element published successfully', { _id: publishedElement._id });

    } catch (err) {
        // Log and handle any errors
        logger.error(`Error at Front Controller publishDraftElements: ${err}`);
        return sendError(res, err);
    }
}

async function createElementVariant(req, res) {
    try {
        const { element_id } = req.params;

        const { elements_data } = req.body;

        const element = await Components.findOne({
            _id: element_id,
            component_type: componentType.ELEMENTS,
            component_state: componentState.PUBLISHED
        }).lean();

        if (!element) {
            return ReS(res, constants.resource_not_found, 'Oops! Element Not Found.');
        }

        if (element?.created_by_user?.toString() == req.session._id?.toString()) {
            return ReS(res, constants.bad_request_code, 'Oops! As the author of this element, you\'re not allowed to create a variant.');
        }

        const existingElementVariant = await DraftComponents.findOne({
            variant_id: element._id,
            created_by_user: req.session._id
        }).lean();

        if (existingElementVariant) {
            return ReS(res, constants.bad_request_code, 'This variant already exists. You can go to your profile to make changes.');
        }

        const elementVariant = {
            variant_id: (element?.variant_id) ? element?.variant_id : element?._id,
            source_id: element?._id,
            component_state: componentState.ACTIVE_DRAFT,
            title: 'Untitled',
            slug: 'untitled',
            short_description: '',
            long_description: '',
            image_url: element?.image_url,
            thumbnail_url: element?.thumbnail_url,
            elements_data: elements_data, // elements_data overridden by new data
            created_by_user: req.session._id,
            category_id: element?.category_id,
            component_type: element?.component_type,
            orientation: element?.orientation,
            difficulty_level: element?.difficulty_level,
            identification_tag: element?.identification_tag,
            languages: element?.languages,
            live_preview: element?.live_preview,
            element_container_meta: element?.element_container_meta
        };

        const newElementVariant = await DraftComponents.create(elementVariant);

        return ReS(res, constants.success_code, 'Element variant created successfully', { _id: newElementVariant._id });
    } catch (err) {
        // Log and handle any errors
        logger.error(`Error at Front Controller createElementVariant: ${err}`);
        return sendError(res, err);
    }
}

async function downloadElementCode(req, res) {
    try {
        const { element_id } = req.params;

        const element = await Components.findOne({
            _id: element_id,
            component_type: componentType.ELEMENTS,
            component_state: componentState.PUBLISHED
        }).lean();

        if (!element) {
            return ReS(res, constants.resource_not_found, 'Oops! Element Not Found.');
        }

        const folderName = `${element.slug}.zip`;

        if (element && Object.keys(element.elements_data).length) {
            res.setHeader('Content-Disposition', `attachment; filename=${folderName}`);
            res.setHeader('Content-Type', 'application/zip');
            res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');

            const archive = archiver('zip', { zlib: { level: 9 } });
            archive.pipe(res);

            const isCssExists = element.elements_data.hasOwnProperty('css');
            const isJsExists = element.elements_data.hasOwnProperty('javascript');

            for (const section in element.elements_data) {

                switch (section) {
                    case 'css':
                        archive.append(element.elements_data[section], { name: elementFileNames.CSS });
                        break;
                    case 'javascript':
                        archive.append(element.elements_data[section], { name: elementFileNames.JAVASCRIPT });
                        break;
                    case 'html':
                        const htmlContent = await injectAssetsIntoHTML(element.elements_data[section], isCssExists, isJsExists);
                        archive.append(htmlContent, { name: elementFileNames.HTML });
                        break;
                    default:
                        break;
                }

            }
            // Store element download history
            await ComponentDownloadHistory.create({
                component_id: element_id,
                component_type: componentType.ELEMENTS,
                user_id: req.session._id
            });
            // Increment download count in Components collection
            await updateComponentDownloadCount(element_id);
            // Finalize the archive, sending it to the client
            archive.finalize();

            // Handle any errors
            archive.on('error', (err) => {
                console.error('Error while create zip for element', err);
                return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
            });
        } else {
            res.status(400).send('No elements data found to zip.');
        }
    } catch (err) {
        // Log and handle any errors
        logger.error(`Error at Front Controller downloadElementCode: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function makePrivatePublishedElement(req, res) {
    try {
        const elementId = req.params.id;

        const conditions = {
            _id: elementId,
            created_by_user: req.session._id,
            component_type: componentType.ELEMENTS
        };

        // Fetch the element by ID with selected fields
        const draftElement = await DraftComponents.findOne(conditions).select('_id').lean();

        // Return an error response if the element is not found
        if (!draftElement) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Not Found.');
        }

        // Fetch the element by ID with selected fields
        const publishedElement = await Components.findOne({
            component_draft_id: draftElement._id,
            component_state: componentState.PUBLISHED
        }).select('_id').lean();

        if (!publishedElement) {
            return ReS(res, constants.bad_request_code, 'Oops! There is no published version to take down.');
        }

        // Changed element state from publish to private
        await Components.updateOne({
            _id: publishedElement._id
        }, {
            $set: {
                component_state: componentState.PRIVATE
            }
        });
        // Return a success response
        return ReS(res, constants.success_code, 'Element unpublished successfully', { _id: publishedElement._id });

    } catch (err) {
        // Log and handle any errors
        logger.error(`Error at Front Controller makePrivatePublishedElement: ${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createElementPlaceHolder,
    updateElement,
    getDraftElementDetails,
    publishDraftElements,
    createElementVariant,
    downloadElementCode,
    makePrivatePublishedElement
};