const { loginWithEmail, signUpWithEmail, verifyEmailOtp, checkUsernameAvailability, checkEmailAvailability, signUpWithSocial, guestLogin, addUserInterest, verifyLoginOtp, resendSignUpOtp } = require('../../../../validations/front/auth/authValidation');
class AuthValidationMiddleware {
    signUpWithEmailValidation(req, res, next) {
        const { value, error } = signUpWithEmail(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    loginWithEmailValidation(req, res, next) {
        const { value, error } = loginWithEmail(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    verifyEmailOtpValidation(req, res, next) {
        const { value, error } = verifyEmailOtp(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    checkUsernameAvailabilityValidation(req, res, next) {
        const { value, error } = checkUsernameAvailability(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    checkEmailAvailabilityValidation(req, res, next) {
        const { value, error } = checkEmailAvailability(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    signUpWithSocialValidation(req, res, next) {
        const { value, error } = signUpWithSocial(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    guestLoginValidation(req, res, next) {
        const { value, error } = guestLogin(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    addUserInterestValidation(req, res, next) {
        const { value, error } = addUserInterest(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    verifyLoginOtpValidation(req, res, next) {
        const { value, error } = verifyLoginOtp(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    resendSignUpOtpValidation(req, res, next) {
        const { value, error } = resendSignUpOtp(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

}
module.exports = new AuthValidationMiddleware();