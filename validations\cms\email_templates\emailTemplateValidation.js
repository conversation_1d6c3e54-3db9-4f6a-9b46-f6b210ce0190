const Joi = require('joi');

class EmailTemplateValidation {
    addEmailTemplate(params) {
        const schema = Joi.object({
            title: Joi.string().required(),
            subject: Joi.string().required(),
            template_description: Joi.string().required(),
            dynamic_values: Joi.string().required(),
            template_banner: Joi.string().required(),
            language: Joi.string().required(),
            version: Joi.number().required()
        });
        return schema.validate(params);
    }
}

module.exports = new EmailTemplateValidation();