const { createCommit, createCommitWithFiles, createBranch } = require('../../../../validations/front/repository/repositoryValidation');

class RepositoryValidationMiddleware {
    createCommitValidation(req, res, next) {
        const { value, error } = createCommit(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createCommitWithFilesValidation(req, res, next) {
        const { value, error } = createCommitWithFiles(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createBranchValidation(req, res, next) {
        const { value, error } = createBranch(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new RepositoryValidationMiddleware();