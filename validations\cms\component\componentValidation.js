const Joi = require('joi');

const mongoose = require('mongoose');

const { unlockTypes, componentOrientation } = require('../../../config/component.constant');

class ComponentValidation {
    createComponent(params) {
        const schema = Joi.object({
            title: Joi.string().required(),
            image_url: Joi.string().optional(),
            thumbnail_url: Joi.string().optional(),
            is_active: Joi.boolean().optional(),
            description: Joi.string().required(),
            long_description: Joi.string().optional(),
            selected_option: Joi.string().required(),
            platform_data: Joi.array().required(),
            parent_id: Joi.string().optional(),
            difficulty_level: Joi.string().optional().allow(null, ''),
            modifier: Joi.string().optional().allow(null, ''),
            identification_tag: Joi.array().items(Joi.string().trim()).optional(),
            parent_version: Joi.number().when('parent_id', {
                is: Joi.exist(),  // Make parent_version required if parent_id is present   
                then: Joi.required(),
                otherwise: Joi.optional()
            }),
            is_paid: Joi.boolean().required(),
            unlock_points: Joi.number().optional(),
            unlock_type: Joi.string().valid(...Object.values(unlockTypes)).optional(),
            orientation: Joi.string().valid(...Object.values(componentOrientation)).optional(),
            assets: Joi.array().items(Joi.object({
                file_url: Joi.string().required(),
                file_extension: Joi.string().required(),
                file_name: Joi.string().optional().allow(null, ''),
                file_original_name: Joi.string().required(),
                file_mime_type: Joi.string().required(),
                file_notes: Joi.string().optional().allow(null, '') // optional field
            })).optional(),
            unlock_duration: Joi.number().optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    createComponentRepository(params) {
        const schema = Joi.object({
            title: Joi.string().required(),
            category_id: Joi.string().required(),
            short_description: Joi.string().required().allow(null, ''),
            long_description: Joi.string().optional().allow(null, ''),
            description: Joi.string().optional().allow(null, ''),
            difficulty_level: Joi.string().optional().allow(null, ''),
            identification_tag: Joi.array().items(Joi.string().trim()).optional(),
            project_name: Joi.string().optional().allow(null, ''),
            platform_id: Joi.string().optional(),
            repository_id: Joi.string().optional().allow(null, ''),
            component_type: Joi.string().required(),
            is_paid: Joi.boolean().required(),
            unlock_points: Joi.number().optional(),
            unlock_type: Joi.string().valid(...Object.values(unlockTypes)).optional(),
            unlock_duration: Joi.number().optional(),
            initialize_with_readme: Joi.boolean().optional(),
            assets: Joi.array().items(Joi.object({
                file_url: Joi.string().required(),
                file_extension: Joi.string().required(),
                file_name: Joi.string().optional().allow(null, ''),
                file_original_name: Joi.string().required(),
                file_mime_type: Joi.string().required(),
                file_notes: Joi.string().optional().allow(null, '') // optional field
            })).optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updateComponent(params) {
        const schema = Joi.object({
            title: Joi.string().optional(),
            image_url: Joi.string().optional(),
            thumbnail_url: Joi.string().optional(),
            is_active: Joi.boolean().optional(),
            description: Joi.string().optional(),
            long_description: Joi.string().optional(),
            selected_option: Joi.string().optional(),
            platform_data: Joi.array().optional(),
            parent_id: Joi.string().optional(),
            difficulty_level: Joi.string().optional().allow(null, ''),
            modifier: Joi.string().optional().allow(null, ''),
            identification_tag: Joi.array().items(Joi.string().trim()).optional(),
            parent_version: Joi.number().when('parent_id', {
                is: Joi.exist(),  // Make parent_version required if parent_id is present
                then: Joi.required(),
                otherwise: Joi.optional()
            }),
            is_paid: Joi.boolean().optional(),
            unlock_points: Joi.number().optional(),
            unlock_type: Joi.string().valid(...Object.values(unlockTypes)).optional(),
            orientation: Joi.string().valid(...Object.values(componentOrientation)).optional(),
            assets: Joi.array().items(Joi.object({
                file_url: Joi.string().required(),
                file_extension: Joi.string().required(),
                file_name: Joi.string().optional().allow(null, ''),
                file_original_name: Joi.string().required(),
                file_mime_type: Joi.string().required(),
                file_notes: Joi.string().optional().allow(null, '') // optional field
            })).optional(),
            unlock_duration: Joi.number().optional()
        }).options({ allowUnknown: true });;
        return schema.validate(params);
    }

    updateComponentStatus(params) {
        const schema = Joi.object({
            component_state: Joi.string().required()
        }).options({ allowUnknown: true });;
        return schema.validate(params);
    }

    linkCodeSpacesToComponent(params) {
        const schema = Joi.object({
            code_spaces: Joi.array().items(Joi.string().trim()).min(1).unique().required()
        }).options({ allowUnknown: true });;
        return schema.validate(params);
    }

    publishRepositoryComponent(params) {
        const schema = Joi.object({
            title: Joi.string().required().messages({
                'string.base': 'The title must be a text string.',
                'string.empty': 'The title cannot be empty.',
                'any.required': 'Please provide a title for the component.'
            }),
            collection_id: Joi.any().custom((value, helpers) => {
                if (!mongoose.Types.ObjectId.isValid(value)) {
                    return helpers.message('Please select a valid collection.');
                }
                return value;
            }).optional(),
            image_url: Joi.string().required().messages({
                'string.base': 'Please select a valid preview image for your project.',
                'string.empty': 'Please select a valid preview image for your project.',
                'any.required': 'Please select a valid preview image for your project.'
            }),
            thumbnail_url: Joi.string().optional().messages({
                'string.base': 'Please select a valid thumbnail image for your project',
                'string.empty': 'Please select a valid thumbnail image for your project'
            }),
            short_description: Joi.string().required().allow(null, '').messages({
                'string.base': 'The short description must be a text string.',
                'any.required': 'A short description is required. It can be empty, but the field must be present.'
            }),
            long_description: Joi.string().optional().allow(null, '').messages({
                'string.base': 'The long description must be a text string.'
            }),
            difficulty_level: Joi.string().optional().allow(null, '').messages({
                'string.base': 'The difficulty level must be a text string.'
            }),
            component_type: Joi.string().required().messages({
                'string.base': 'The component type must be a valid string.',
                'any.required': 'Please specify the component type.'
            }),
            is_paid: Joi.boolean().required().messages({
                'boolean.base': 'The paid status must be true or false.',
                'any.required': 'Please indicate whether the component is paid or free.'
            }),
            identification_tag: Joi.array().items(Joi.string().trim()).unique().messages({
                'array.base': 'Tags must be an array of strings.',
                'array.unique': 'Tags must be unique.'
            }).optional(),
            mpn_parity: Joi.number().min(0).default(0).required().messages({
                'number.base': 'Parity must be a number.',
                'number.min': 'Parity cannot be less than 0.',
                'any.required': 'Parity is required.'
            }),
            purchase_price: Joi.object({
                fiat: Joi.number().min(0).default(0).required().messages({
                    'number.base': 'Fiat must be a number.',
                    'number.min': 'Fiat cannot be less than 0.',
                    'any.required': 'Fiat is required.'
                }),
                mpn_points: Joi.number().min(0).default(0).required().messages({
                    'number.base': 'MPN Points must be a number.',
                    'number.min': 'MPN Points cannot be less than 0.',
                    'any.required': 'MPN Points are required.'
                })
            }).optional(),
            item_price: Joi.object({
                fiat: Joi.number().min(0).default(0).required().messages({
                    'number.base': 'Fiat must be a number.',
                    'number.min': 'Fiat cannot be less than 0.',
                    'any.required': 'Fiat is required.'
                }),
                mpn_points: Joi.number().min(0).default(0).required().messages({
                    'number.base': 'MPN Points must be a number.',
                    'number.min': 'MPN Points cannot be less than 0.',
                    'any.required': 'MPN Points are required.'
                })
            }).optional(),
            buyer_fee: Joi.object({
                fiat: Joi.number().min(0).default(0).required().messages({
                    'number.base': 'Fiat must be a number.',
                    'number.min': 'Fiat cannot be less than 0.',
                    'any.required': 'Fiat is required.'
                }),
                mpn_points: Joi.number().min(0).default(0).required().messages({
                    'number.base': 'MPN Points must be a number.',
                    'number.min': 'MPN Points cannot be less than 0.',
                    'any.required': 'MPN Points are required.'
                })
            }).optional(),
            assets: Joi.array().optional().messages({
                'array.base': 'Assets must be an array of objects.'
            })
        }).options({ allowUnknown: true });

        return schema.validate(params);
    }

    publishElementValidation(params) {
        const schema = Joi.object({
            title: Joi.string().required().messages({
                'string.base': 'The title must be a text string.',
                'string.empty': 'The title cannot be empty.',
                'any.required': 'Please provide a title for the component.'
            }),
            category_id: Joi.any().custom((value, helpers) => {
                if (!mongoose.Types.ObjectId.isValid(value)) {
                    return helpers.message('Please select a valid category.');
                }
                return value;
            }).required(),
            image_url: Joi.string().optional().allow(null, '').messages({
                'string.base': 'Please select a valid preview image for your project.',
                'string.empty': 'Please select a valid preview image for your project.',
                'any.required': 'Please select a valid preview image for your project.'
            }),
            thumbnail_url: Joi.string().optional().allow(null, '').messages({
                'string.base': 'Please select a valid thumbnail image for your project',
                'string.empty': 'Please select a valid thumbnail image for your project'
            }),
            short_description: Joi.string().required().allow(null, '').messages({
                'string.base': 'The short description must be a text string.',
                'any.required': 'A short description is required. It can be empty, but the field must be present.'
            }),
            long_description: Joi.string().optional().allow(null, '').messages({
                'string.base': 'The long description must be a text string.'
            }),
            difficulty_level: Joi.string().optional().allow(null, '').messages({
                'string.base': 'The difficulty level must be a text string.'
            }),
            component_type: Joi.string().required().messages({
                'string.base': 'The component type must be a valid string.',
                'any.required': 'Please specify the component type.'
            }),
            is_paid: Joi.boolean().required().messages({
                'boolean.base': 'The paid status must be true or false.',
                'any.required': 'Please indicate whether the component is paid or free.'
            }),
            identification_tag: Joi.array().items(Joi.string().trim()).unique().messages({
                'array.base': 'Tags must be an array of strings.',
                'array.unique': 'Tags must be unique.'
            }).optional(),
            assets: Joi.array().optional().messages({
                'array.base': 'Assets must be an array of objects.'
            }),
            location: Joi.object({
                id: Joi.number().integer().required(),
                iso3: Joi.string().length(3).required()
            }).optional()
        }).options({ allowUnknown: true });

        return schema.validate(params);
    }
}

module.exports = new ComponentValidation();