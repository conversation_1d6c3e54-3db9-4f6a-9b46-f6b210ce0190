const { createLicense, updateLicense } = require('../../../../validations/cms/platform_license/platformLicenseValidation');

class LicenseValidationMiddleware {
    createLicenseValidation(req, res, next) {
        const { value, error } = createLicense(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateLicenseValidation(req, res, next) {
        const { value, error } = updateLicense(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new LicenseValidationMiddleware();