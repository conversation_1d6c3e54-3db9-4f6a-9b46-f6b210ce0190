const constants = require('../config/constants');
const Jwt = require('jsonwebtoken');
const logger = require('../config/logger');

const { getAdminPermissions } = require('../services/admin.service');

module.exports = async function (req, res, next) {
    const token = (req.body && req.body.token) || (req.query && req.query.token) || req.headers['token'];
    if (token) {
        try {
            let isError = false;
            let errorMessage = '';
            // check if token is expired or not
            await Jwt.verify(token, process.env.JWT_CMS_SECRET_KEY, async function (err, decoded) {
                if (decoded) {
                    const dbUserId = decoded.data._id;
                    req['session'] = decoded.data;
                    if (decoded && decoded.data && !decoded.data.is_super_admin) {
                        req['session']['permissions'] = await getAdminPermissions(req, res, dbUserId);
                    }
                }
                if (err) {
                    isError = true;
                    errorMessage = 'Sorry! Your token seems to be expired, please request a new one.';
                    return;
                }
            });
            if (isError) {
                return res.status(constants.forbidden_code).json({
                    'status': constants.forbidden_code,
                    'message': errorMessage
                });
            }
            next();
        }
        catch (err) {
            logger.error(`error at validateRequest ${err}`);
            return res.status(constants.server_error_code).json({
                'status': constants.server_error_code,
                'message': 'Oops! Something went wrong.',
                'error': err
            });
        }
    } else {
        return res.status(constants.forbidden_code).json({
            'status': constants.forbidden_code,
            'message': 'Token has not been passed, please check again.'
        });
    }
};