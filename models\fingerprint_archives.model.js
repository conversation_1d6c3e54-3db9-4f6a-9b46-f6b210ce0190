const mongoose = require('mongoose');
const Schema = mongoose.Schema;


// Define sub-schemas
const BrowserDetailsSchema = new Schema({
    browserName: String,
    browserMajorVersion: String,
    browserFullVersion: String,
    os: String,
    osVersion: String,
    device: String,
    userAgent: String
});

const GeolocationSchema = new Schema({
    accuracyRadius: Number,
    latitude: Number,
    longitude: Number,
    postalCode: String,
    timezone: String,
    city: {
        name: String
    },
    country: {
        code: String,
        name: String
    },
    continent: {
        code: String,
        name: String
    },
    subdivisions: [
        {
            isoCode: String,
            name: String
        }
    ]
});

const AsnSchema = new Schema({
    asn: String,
    name: String,
    network: String
});

const DatacenterSchema = new Schema({
    result: Boolean,
    name: String
});

const V4Schema = new Schema({
    address: String,
    geolocation: GeolocationSchema,
    asn: AsnSchema,
    datacenter: DatacenterSchema
});

const MethodsSchema = new Schema({
    timezoneMismatch: Boolean,
    publicVPN: Boolean,
    auxiliaryMobile: Boolean
});

const FingerprintArchivesSchema = new Schema({
    requestId: {
        type: String
    },
    visitorId: {
        type: String
    },
    identification: {
        data: {
            visitorId: String,
            requestId: String,
            browserDetails: BrowserDetailsSchema,
            incognito: Boolean,
            ip: String,
            timestamp: Number,
            time: String,
            url: String,
            tag: {
                displayHeight: Number,
                displayWidth: Number,
                process: String
            },
            confidence: {
                score: Number
            },
            visitorFound: Boolean,
            firstSeenAt: {
                global: String,
                subscription: String
            },
            lastSeenAt: {
                global: String,
                subscription: String
            }
        }
    },
    botd: {
        data: {
            bot: {
                result: String
            },
            meta: {
                displayHeight: Number,
                displayWidth: Number,
                process: String
            },
            url: String,
            ip: String,
            time: String,
            userAgent: String,
            requestId: String
        }
    },
    rootApps: {
        data: {
            result: Boolean
        }
    },
    emulator: {
        data: {
            result: Boolean
        }
    },
    ipInfo: {
        data: {
            v4: V4Schema
        }
    },
    vpn: {
        data: {
            result: Boolean,
            originTimezone: String,
            originCountry: String,
            methods: MethodsSchema
        }
    },
    incognito: {
        data: {
            result: Boolean
        }
    },
    tampering: {
        data: {
            result: Boolean,
            anomalyScore: Number
        }
    },
    clonedApp: {
        data: {
            result: Boolean
        }
    },
    factoryReset: {
        data: {
            time: String,
            timestamp: Number
        }
    },
    jailbroken: {
        data: {
            result: Boolean
        }
    },
    frida: {
        data: {
            result: Boolean
        }
    },
    privacySettings: {
        data: {
            result: Boolean
        }
    },
    virtualMachine: {
        data: {
            result: Boolean
        }
    },
    locationSpoofing: {
        data: {
            result: Boolean
        }
    },
    suspectScore: {
        data: {
            result: Number
        }
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const FingerprintArchives = mongoose.model('fingerprint_archives', FingerprintArchivesSchema);

module.exports = {
    FingerprintArchives
};