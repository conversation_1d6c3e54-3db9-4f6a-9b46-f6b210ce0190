const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const SupportedPlatformsSchema = new Schema({
    title: {
        type: String,
        required: true
    },
    slug: {
        type: String,
        required: true,
        unique: true
    },
    description: {
        type: String
    },
    image_url: {
        type: String,
        require: true
    },
    is_active: {
        type: Boolean,
        default: true
    },
    is_default: {
        type: Boolean,
        default: false
    },
    system_generated: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const SupportedPlatforms = mongoose.model('supported_platforms', SupportedPlatformsSchema);

module.exports = {
    SupportedPlatforms
};