/* eslint-disable no-undef */
const nodemailer = require('nodemailer');
const ejs = require('ejs');
const logger = require('../config/logger');
const fs = require('fs').promises;

// Create a Nodemailer transporter
const transporter = nodemailer.createTransport({
    host: process.env.MAIL_HOST,
    port: process.env.MAIL_PORT,
    secure: true, // true for 465, false for other ports
    auth: {
        user: process.env.MAIL_USERNAME,
        pass: process.env.MAIL_PASSWORD
    },
    logger: false,//turn on if mails are not sending
    debug: false // include SMTP traffic in the logs
});

// Function to send email with EJS template
async function sendEmailWithTemplate(emailData) {
    try {
        // Read the EJS template file
        const template = await fs.readFile(`${__dirname}/../views/${emailData.template}`, 'utf-8');

        // Render the EJS template with provided data
        const renderedTemplate = ejs.render(template, {
            content: emailData.template_description,
            base_image_url: process.env.AWS_STATIC_CONTENT_URL,
            banner_image_url: `${process.env.AWS_STATIC_CONTENT_URL}${emailData.template_banner}`
        });
        // Send mail with defined transport object
        const info = await transporter.sendMail({
            from: `${process.env.MAIL_FROM_NAME}<${process.env.MAIL_FROM_EMAIL}>`,
            to: emailData.email,
            subject: emailData.subject,
            html: renderedTemplate
        });

        logger.info('Email sent: %s', info.messageId);
    } catch (error) {
        logger.error('Error occurred while sending email:', error);
    }
}

module.exports = {
    sendEmailWithTemplate
};
