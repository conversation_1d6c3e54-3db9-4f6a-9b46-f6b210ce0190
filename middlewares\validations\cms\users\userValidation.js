const { updateUserAuthor<PERSON>ees, updateUserBuyerFees } = require('../../../../validations/cms/users/userValidation');

class UserValidationMiddleware {
    updateUserAuthorFeesValidation(req, res, next) {
        const { value, error } = updateUserAuthorFees(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateUserBuyerFeesValidation(req, res, next) {
        const { value, error } = updateUserBuyerFees(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new UserValidationMiddleware();