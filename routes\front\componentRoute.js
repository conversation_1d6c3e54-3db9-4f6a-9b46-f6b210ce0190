const express = require('express');
const router = express.Router();
const authRouter = express.Router();

const { getAllChildComponents, getAllComponents, getComponentDetail, getComponentRecommendation, increaseComponentViews, getAllComponentsForHomePage, likeComponent, bookMarkComponent, getAllBookMarkedComponents, removeBookMarkComponent, getComponentUserMeta, removeComponentLike, unlockComponent, getAllLikedComponents, getComponentPlatformDetail, getComponentDetailPublic, getComponentSearchSuggestions, detachRepositoryFromComponent, createComponentPlaceHolder, updateRepositoryComponent, linkCodeSpacesToComponent, getAllComponentList, getRepoComponentDetails, uploadComponentAssets, publishDraftComponent, getDraftComponentDetails, makePrivatePublishedComponent, getElementsRecommendation, softDeleteDraftComponent, fetchComponentTagSuggestions, getProjectPriceEstimation, fetchProjectUnlockRates, fetchLicenseContent, calculatesMonthlySalesProjections } = require('../../controller/front/component.controller');

const { linkCodeSpacesToComponentValidation, updateComponentRepositoryValidation, calculateSalesProjectionValidation, createComponentPlaceholderValidation } = require('../../middlewares/validations/front/component/componentValidation');


authRouter.get('/get/:slug', getComponentDetail);
router.get('/get/:slug', getComponentDetailPublic);

router.post('/list/', getAllComponents);
router.post('/:slug/child/list', getAllChildComponents);
router.get('/get/:slug/recommended', getComponentRecommendation);
router.get('/elements/get/:slug/recommended', getElementsRecommendation);
router.post('/homepage/list', getAllComponentsForHomePage);

authRouter.put('/:slug/increase/views', increaseComponentViews);

authRouter.post('/:slug/like', likeComponent);
authRouter.delete('/:slug/like/remove', removeComponentLike);
authRouter.post('/list/liked', getAllLikedComponents);

authRouter.post('/:slug/bookmark', bookMarkComponent);
authRouter.delete('/:slug/bookmark/remove', removeBookMarkComponent);
authRouter.post('/list/bookmarked', getAllBookMarkedComponents);

authRouter.get('/:slug/get-meta-data', getComponentUserMeta);

authRouter.post('/:slug/unlock', unlockComponent);

router.get('/get/:slug/platforms', getComponentPlatformDetail);
router.get('/search/suggestions', getComponentSearchSuggestions);

authRouter.post('/create/placeholder', createComponentPlaceholderValidation, createComponentPlaceHolder);
authRouter.put('/:id/detach/:repository_id', detachRepositoryFromComponent);
authRouter.put('/:id/link/code-spaces', linkCodeSpacesToComponentValidation, linkCodeSpacesToComponent);
authRouter.post('/get-all-components/list', getAllComponentList);

authRouter.put('/update/repository/:id', updateComponentRepositoryValidation, updateRepositoryComponent);

authRouter.get('/get-component/:id', getRepoComponentDetails);
authRouter.post('/upload/assets', uploadComponentAssets);

authRouter.post('/:id/publish', publishDraftComponent);

authRouter.get('/get-component/draft/:id', getDraftComponentDetails);

authRouter.post('/:id/unpublish', makePrivatePublishedComponent);

authRouter.delete('/draft/:id/archive', softDeleteDraftComponent);

authRouter.get('/draft/:id/tag-suggestions', fetchComponentTagSuggestions);

authRouter.get('/price/estimation', getProjectPriceEstimation);

authRouter.get('/:slug/unlock/get-rates', fetchProjectUnlockRates);

authRouter.get('/license/:license_id', fetchLicenseContent);

authRouter.post('/sales/projection', calculateSalesProjectionValidation, calculatesMonthlySalesProjections);

module.exports.authRouter = authRouter;
module.exports.router = router;