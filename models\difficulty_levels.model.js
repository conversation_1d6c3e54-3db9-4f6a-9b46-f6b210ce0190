const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const difficultyLevelSchema = new Schema({
    title: {
        type: String,
        require: true
    },
    slug: {
        type: String,
        required: true
    },
    description: {
        type: String
    },
    is_active: {
        type: Boolean,
        default: true
    },
    created_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    updated_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const DifficultyLevels = mongoose.model('difficulty_levels', difficultyLevelSchema);

module.exports = {
    DifficultyLevels
};