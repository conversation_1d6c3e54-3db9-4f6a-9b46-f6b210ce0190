// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, generateSlug, escapeRegex, sendError } = require('../../services/general.helper');

// Models declaration
const TopUpPlans = require('../../models/top_up_plans.model').TopUpPlans;

// This asynchronous function creates a new top-up plan based on the request data
async function createTopUpPlan(req, res) {
    try {
        // Extracting required data from the request body
        const {
            plan_name,
            plan_media,
            plan_coins,
            bonus_coins,
            plan_price,
            is_active
        } = req.body;

        // Calculating bonus percentage based on bonus coins and plan coins
        const bonus_percentage = (bonus_coins / plan_coins) * 100;
        // Calculating total coins by adding plan coins and bonus coins
        const total_coins = plan_coins + bonus_coins;
        const planSlug = generateSlug(plan_name);
        // Creating a new top-up plan record in the database
        const response = await TopUpPlans.create({
            plan_name: plan_name,
            plan_slug: planSlug,
            plan_media: plan_media,
            plan_coins: plan_coins,
            bonus_coins: bonus_coins,
            plan_price: plan_price,
            bonus_percentage: parseFloat(bonus_percentage), // Converting to float
            total_coins: total_coins,
            is_active: is_active
        });

        // Returning success response with the created top-up plan data
        return ReS(res, constants.success_code, 'Success', response);
    } catch (err) {
        // Handling errors
        logger.error(`Error at CMS Controller createTopUpPlan${err}`);
        // Returning error response
        return sendError(res, err);
    }
}

async function updateTopUpPlan(req, res) {
    try {
        const topUpPlan = await TopUpPlans.findOne({
            _id: req.params.id
        });

        if (topUpPlan == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Plan Not Found.');
        }
        // Extracting required data from the request body
        const {
            plan_name,
            plan_media,
            plan_coins,
            bonus_coins,
            plan_price,
            is_active
        } = req.body;

        // Calculating bonus percentage based on bonus coins and plan coins
        const bonus_percentage = (bonus_coins / plan_coins) * 100;
        // Calculating total coins by adding plan coins and bonus coins
        const total_coins = plan_coins + bonus_coins;
        const planSlug = generateSlug(plan_name);

        const postData = {
            plan_coins: plan_coins,
            plan_name: plan_name,
            plan_media: plan_media,
            bonus_coins: bonus_coins,
            plan_price: plan_price,
            plan_slug: planSlug,
            bonus_percentage: parseFloat(bonus_percentage), // Converting to float
            total_coins: total_coins,
            is_active: is_active
        };

        await TopUpPlans.updateOne({
            _id: req.params.id
        }, {
            '$set': postData
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateTopUpPlan${err}`);
        return sendError(res, err);
    }
}

async function updatePlanStatus(req, res) {
    try {
        const { is_active } = req.body;

        const topUpPlan = await TopUpPlans.findOne({
            _id: req.params.id
        });

        if (topUpPlan == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Plan Not Found.');
        }

        await TopUpPlans.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updatePlanStatus${err}`);
        return sendError(res, err);
    }
}

async function getAllPlans(req, res) {
    try {
        const totalDocuments = await TopUpPlans.countDocuments();
        const filter = {
            is_deleted: false
        };
        if (req.body.is_active != undefined) {
            filter.is_active = req.body.is_active;
        }
        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            filter['$or'] = [{
                'plan_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        // Set default sort
        const sort = {
            is_active: -1,
            created_at: -1
        };
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        // Count filtered documents after filter apply
        const filterDocuments = await TopUpPlans.countDocuments(filter);
        const planList = await TopUpPlans.find(filter, {
            'plan_name': 1,
            'plan_slug': 1,
            'plan_media': 1,
            'plan_coins': 1,
            'bonus_coins': 1,
            'bonus_percentage': 1,
            'total_coins': 1,
            'plan_price': 1,
            'is_active': 1,
            'created_at': 1
        }).sort(sort).skip(skip).limit(limit).lean();

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: planList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllPlans${err}`);
        return sendError(res, err);
    }
}

async function getPlanDetails(req, res) {
    try {

        const filter = {
            _id: req.params.id,
            is_deleted: false
        };

        const topUpPlan = await TopUpPlans.findOne(filter, {
            'plan_name': 1,
            'plan_slug': 1,
            'plan_media': 1,
            'plan_coins': 1,
            'bonus_coins': 1,
            'bonus_percentage': 1,
            'total_coins': 1,
            'plan_price': 1,
            'is_active': 1,
            'created_at': 1
        }).lean();

        if (topUpPlan == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Plan Not Found.');
        }

        return ReS(res, constants.success_code, 'Data Fetched', topUpPlan);
    } catch (err) {
        logger.error(`Error at CMS Controller getPlanDetails${err}`);
        return sendError(res, err);
    }
}

async function deletePlan(req, res) {
    try {
        const filter = {
            _id: req.params.id,
            is_deleted: false
        };

        const topUpPlan = await TopUpPlans.findOne(filter);

        if (topUpPlan == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Plan Not Found.');
        }
        await TopUpPlans.updateOne(filter, {
            $set: {
                is_deleted: true
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller deletePlan${err}`);
        return sendError(res, err);
    }
}

async function getAllPlansSortList(req, res) {
    try {
        const filter = {
            is_active: true,
            is_deleted: false
        };
        // Default sort
        const sort = {
            'created_at': 1
        };
        if (req.query.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.query.searchText);
            filter['$or'] = [{
                'plan_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const planList = await TopUpPlans.find(filter, {
            'plan_name': 1,
            'plan_slug': 1,
            'plan_media': 1,
            'plan_coins': 1,
            'bonus_coins': 1,
            'bonus_percentage': 1,
            'total_coins': 1,
            'plan_price': 1,
            'is_active': 1,
            'created_at': 1
        }).sort(sort).lean();
        return ReS(res, constants.success_code, 'Data Fetched', planList);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllPlansSortList${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createTopUpPlan,
    updateTopUpPlan,
    getAllPlans,
    getPlanDetails,
    deletePlan,
    updatePlanStatus,
    getAllPlansSortList
};