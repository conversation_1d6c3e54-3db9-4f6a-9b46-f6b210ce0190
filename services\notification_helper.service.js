// Model declaration

const { Notifications } = require('../models/notification.model');
const { UserNotifications } = require('../models/user_notification.model');
const { sendNotificationToUser, sendNotificationCountToUser, isUserConnected } = require('./socket.service');
const logger = require('../config/logger');

/**
 * Save notification to database and send to users via socket
 * @param {Object} notification - Notification data
 * @param {Array} receivers - Array of user IDs to receive the notification
 */
const saveAndSendNotification = async (notification, receivers) => {
    try {
        // Create a new notification document in the database
        const newNotification = await Notifications.create(notification);

        // Prepare bulk entries for UserNotifications
        const userNotifications = receivers.map((receiver) => ({
            user_id: receiver,
            notification_id: newNotification._id,
            is_read: false
        }));

        // Insert multiple user notifications in a single database call
        await UserNotifications.insertMany(userNotifications);

        // Send real-time notifications to connected users only
        logger.info(`Processing notifications for ${receivers.length} receivers`);

        for (const userId of receivers) {
            try {
                logger.info(`Processing notification for user: ${userId}`);

                // Convert userId to string if it's an ObjectId
                const userIdStr = userId.toString();

                // Check if user is connected via socket
                const isConnected = isUserConnected(userIdStr);
                logger.info(`User ${userIdStr} connection status: ${isConnected ? 'Connected' : 'Not connected'}`);

                // Only send socket events to connected users
                if (isConnected) {
                    logger.info(`Sending real-time notification to connected user ${userIdStr}`);

                    // Send the notification to the user
                    sendNotificationToUser(userIdStr, {
                        _id: newNotification._id,
                        title: notification.title,
                        message: notification.message,
                        type: notification.type,
                        entity: notification.entity,
                        created_at: new Date()
                    });

                    // Get updated unread count for this user
                    const unreadCount = await UserNotifications.countDocuments({
                        user_id: userId,
                        is_read: false
                    });

                    logger.info(`Sending updated unread count to user ${userIdStr}: ${unreadCount}`);

                    // Send the updated count
                    sendNotificationCountToUser(userIdStr, unreadCount);
                } else {
                    logger.info(`User ${userIdStr} is offline - notification saved to database only`);
                }
            } catch (socketError) {
                // Log socket error but continue with other users
                logger.error(`Socket error for user ${userId}: ${socketError}`);
            }
        }
    } catch (error) {
        // Log and rethrow any error encountered during notification creation or user notification insertion
        logger.error(`Error in saveAndSendNotification function: ${error}`);
        throw error;
    }
};

module.exports = {
    saveAndSendNotification
};