const { Tags } = require('../models/tag.model');
const { generateSlug } = require('./general.helper');

const { componentType } = require('../config/component.constant');

const addTagsToCollection = async (tags, type) => {
    try {
        console.log('Add Tags to collection function called', tags);

        const popularityFieldMap = {
            [componentType.ELEMENTS]: 'elements_popularity',
            [componentType.REPOSITORY]: 'project_popularity',
            [componentType.CODESPACE]: 'codespace_popularity'
        };

        const popularityField = popularityFieldMap[type];
        if (!popularityField) {
            throw new Error(`Unsupported component type: ${type}`);
        }

        for (const tag of tags) {
            const tagSlug = generateSlug(tag);
            const existingTag = await Tags.findOne({ slug: tagSlug });

            if (existingTag) {
                existingTag[popularityField] = (existingTag[popularityField] || 0) + 1;
                await existingTag.save();
            } else {
                await Tags.create({
                    title: tag,
                    slug: tagSlug,
                    is_active: true,
                    created_at: new Date(),
                    [popularityField]: 1
                });
            }
        }
    } catch (error) {
        console.error('Error while adding tags to collections:', error);
        throw error;
    }
};


module.exports = {
    addTagsToCollection
};