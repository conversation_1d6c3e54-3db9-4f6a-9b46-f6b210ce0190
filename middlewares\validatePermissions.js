const constants = require('../config/constants');
const logger = require('../config/logger');


async function checkIsAccessible(req, res, next) {
    try {
        // Check if user session exists and if the user is not a super admin
        if (req.session && !req.session.is_super_admin) {

            // Construct API URL based on request base URL and route path
            const apiUrl = `${req.baseUrl.split('v1/')[1]}${req.route.path}`;
            // Check if the user's permissions include the API URL
            if (req.session.permissions.includes(apiUrl)) {
                // User has permission, proceed to the next middleware
                next();
            } else {
                // User doesn't have permission, return forbidden status and message
                return res.status(constants.forbidden_code).json({
                    'missingPermission': true,
                    'status': constants.forbidden_code,
                    'message': 'You are not allowed to access this route'
                });
            }
        } else {
            // User is a super admin or session doesn't exist, proceed to the next middleware
            next();
        }
    } catch (error) {
        // Catch any errors that occur within the try block
        logger.error(`Error in middleware function checkIsAccessible ${error}`);
    }
}


module.exports = {
    checkIsAccessible
};