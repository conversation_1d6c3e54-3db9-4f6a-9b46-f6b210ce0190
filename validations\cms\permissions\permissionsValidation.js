const Joi = require('joi');
class PermissionValidation {
    createPermission(value) {
        const schema = Joi.object({
            display_name: Joi.string().trim().required(),
            module: Joi.string().trim().required(),
            name: Joi.string().trim().required(),
            is_active: Joi.boolean().optional()
        });
        return schema.validate(value);
    }

    updatePermissionStatus(value) {
        const schema = Joi.object({
            is_active: Joi.boolean().required()
        });
        return schema.validate(value);
    }
}

module.exports = new PermissionValidation();