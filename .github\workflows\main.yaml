name: Multiplatform backend to main

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main


env:
  IMAGE_TAG: ${{github.sha}}
  ECR_REPOSITORY: multiplatform_backend_prod

jobs:
  build:
    runs-on: ubuntu-latest
    if: ( github.event_name == 'push' && github.ref == 'refs/heads/main' )
    steps:
    - name: Checkout Code
      uses: actions/checkout@v2
      with:
        ref: ${{ github.ref }}

    - name: Set up AWS CLI
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_MAIN }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_MAIN }}
        aws-region: ${{ secrets.AWS_REGION_MAIN }}

    - name: Login to AWS ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build and push to AWS ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
      run: |
        aws s3 cp s3://multiplatform-env/multiplatform-backend/.env .env
        docker build -f Dockerfile.Production -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker build -f Dockerfile.Production -t $ECR_REGISTRY/$ECR_REPOSITORY:prod .
        docker push -a $ECR_REGISTRY/$ECR_REPOSITORY

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
          
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_MAIN }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_MAIN }}
          aws-region: ${{ secrets.AWS_REGION_MAIN }}
      
      - name: Login to AWS ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Get EC2 key file from secrets
        run: |
          mkdir $HOME/.ssh
          echo "${{ secrets.SSH_MAIN }}" > $HOME/.ssh/multiplatform-prod.pem
          cd $HOME/.ssh
          ls -la

      - name: Deploy to EC2
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          chmod 400 $HOME/.ssh/multiplatform-prod.pem
          ssh -o StrictHostKeyChecking=no -i $HOME/.ssh/multiplatform-prod.pem ubuntu@${{ secrets.MAIN_SERVER_IP }} 'bash -s' < ./deploy-prod.sh $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY $ECR_REPOSITORY
