const express = require('express');
const router = express.Router();

const { createAdmin, getAdminDetails, updateAdmin, updateAdminStatus, deleteAdmin, changePassword, generateGitlabAccessToken, getAdminProfile, downloadGitlabCredentials, checkGitlabUserNameAvailability, createGitlabUser } = require('../../controller/cms/admin.controller');

const { createAdminValidation, updateAdminValidation, updateAdminStatusValidation, changePasswordValidation, createGitlabAccountValidation } = require('../../middlewares/validations/cms/admins/adminValidation');


router.post('/create', createAdminValidation, createAdmin);
router.put('/update/:id', updateAdminValidation, updateAdmin);
router.put('/update/status/:id', updateAdminStatusValidation, updateAdminStatus);
router.get('/details/:id', getAdminDetails);
router.delete('/delete/:id', deleteAdmin);
router.post('/me/change-password', changePasswordValidation, changePassword);
router.post('/me/generate/access-token', generateGitlabAccessToken);
router.get('/me/get-details', getAdminProfile);
router.get('/me/download/gitlab-credentials', downloadGitlabCredentials);
router.get('/check/username-availability', checkGitlabUserNameAvailability);
router.post('/me/creator-account/', createGitlabAccountValidation, createGitlabUser);

module.exports = router;  