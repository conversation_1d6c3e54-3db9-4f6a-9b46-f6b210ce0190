const { createPermissionGroup, updatePermissionGroupStatus, updatePermissionGroup } = require('../../../../validations/cms/permission_groups/permissionGroupsValidation');

class PermissionGroupValidationMiddleware {
    createPermissionGroupValidation(req, res, next) {
        const { value, error } = createPermissionGroup(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updatePermissionGroupValidation(req, res, next) {
        const { value, error } = updatePermissionGroup(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updatePermissionGroupStatusValidation(req, res, next) {
        const { value, error } = updatePermissionGroupStatus(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new PermissionGroupValidationMiddleware();