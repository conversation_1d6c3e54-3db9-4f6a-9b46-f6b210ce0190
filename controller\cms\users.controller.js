// Constants declaration
const constants = require('../../config/constants');
const { componentState, coinTypes } = require('../../config/component.constant');

// Service declaration
const { ReS, escapeRegex, sendError } = require('../../services/general.helper');
const { getFingerprintUserFacets } = require('../../services/fingerprint_helper.service');

// Models declaration
const Users = require('../../models/users.model').Users;
const ComponentUnlockHistory = require('../../models/component_unlock_history.model').ComponentUnlockHistory;
const UserMpcBalance = require('../../models/user_mpc_balance.model').UserMpcBalance;
const UserCoinsHistory = require('../../models/user_coins_history.model').UserCoinsHistory;
const Components = require('../../models/component.model').Components;
const ComponentLikes = require('../../models/component_likes.model').ComponentLikes;
const ComponentBookmarks = require('../../models/component_bookmark.model').ComponentBookmarks;
const Fingerprint = require('../../models/fingerprint.model').Fingerprint;

// Npm declaration
const mongoose = require('mongoose');
const logger = require('../../config/logger');

async function getAllUsers(req, res) {
    try {
        const totalDocuments = await Users.countDocuments();
        // Initialize a default filter object
        const filter = {
            email: {
                $exists: true
            },
            is_deleted: false
        };
        // Check if the request body contains is_guest and set filter accordingly
        if (req.body.is_guest == true) {
            filter['email'] = {
                $exists: false
            };
        }
        // Check if is_active is provided in the request body and set filter accordingly
        if (req.body.is_active != undefined) {
            filter.is_active = req.body.is_active;
        }
        // Check if search query is provided in the request body
        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            filter['$or'] = [{
                'first_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }, {
                'last_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }, {
                'guest_id': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }, {
                'email': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        // Set default sort
        const sort = {
            is_active: -1,
            created_at: -1
        };
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        // Count filtered documents after filter apply
        const filterDocuments = await Users.countDocuments(filter);

        const userList = await Users.find(filter, {
            'first_name': 1,
            'last_name': 1,
            'username': 1,
            'email': 1,
            'mobile_no': 1,
            'mobile_country_code': 1,
            'is_active': 1,
            'is_verified': 1,
            'created_at': 1,
            'guest_id': 1
        }).sort(sort).skip(skip).limit(limit).lean();

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: userList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllUsers${err}`);
        return sendError(res, err);
    }
}

async function updateUserStatus(req, res) {
    try {
        const { is_active } = req.body;

        const userData = await Users.findOne({ _id: req.params.id }).lean();

        if (userData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! User Not Found.');
        }

        await Users.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateUserStatus${err}`);
        return sendError(res, err);
    }
}

async function getUserDetails(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };

        const userData = await Users.findOne(filter, {
            password: 0,
            __v: 0
        }).lean();

        if (userData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! User Not Found.');
        }

        return ReS(res, constants.success_code, 'Data Fetched', userData);
    } catch (err) {
        logger.error(`Error at CMS Controller getUserDetails${err}`);
        return sendError(res, err);
    }
}

async function getUserMpcBalance(req, res) {
    try {
        // Filtering criteria based on user ID from session
        const filter = {
            user_id: req.params.id
        };

        // Fetching user balance from the database
        const userBalance = await UserMpcBalance.findOne(filter, {
            user_id: 1,
            mpn_points: 1,
            mpn_bonus_points: 1,
            fiat: 1
        }).lean();

        // If user balance is not found, return resource not found message
        if (userBalance == null) {
            return ReS(res, constants.resource_not_found, 'Oops! User Balance Not Found.');
        }

        // If user balance is found, return success message along with the balance data
        return ReS(res, constants.success_code, 'Data Fetched', userBalance);
    } catch (err) {
        // If any error occurs, log the error and return server error message
        logger.error(`Error at CMS Controller getUserMpcBalance${err}`);
        return sendError(res, err);
    }
}

async function getUserComponentUnlockHistory(req, res) {
    try {
        const totalDocuments = await UserCoinsHistory.countDocuments();
        // Filtering criteria to find unlock history based on user ID from session
        const filter = {
            unlock_by: req.params.id,
            cost_point: {
                $ne: 0
            }
        };
        // Default sort
        const sort = { created_at: -1 };
        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await ComponentUnlockHistory.countDocuments(filter);
        // Fetching component unlock history from the database based on the filter
        const unlockHistory = await ComponentUnlockHistory.find(filter, {
            component_id: 1,
            component_name: 1,
            component_slug: 1,
            cost_point: 1,
            expired_on: 1,
            is_active: 1,
            created_at: 1
        }).sort(sort).skip(skip).limit(limit).lean();

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: unlockHistory
        };
        // If component unlock history is found, return success message along with the data
        return ReS(res, constants.success_code, 'Component Unlock History Fetched', responseObj);
    } catch (err) {
        // If any error occurs, log the error and return a server error message
        logger.error(`Error at getUserComponentUnlockHistory function:${err}`);
        return sendError(res, err);
    }
}

async function getUserTopUpHistory(req, res) {
    try {
        // Set conditions based on user ID and optional type query
        const conditions = {
            user_id: new mongoose.Types.ObjectId(req.params.id)
        };
        const totalDocuments = await UserCoinsHistory.countDocuments(conditions);
        conditions['type'] = (req.body && req.body.type === coinTypes.BONUS) ? coinTypes.BONUS : coinTypes.NORMAL;
        // Default sort
        const sort = { created_at: -1 };

        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await UserCoinsHistory.countDocuments(conditions);

        // Define aggregation pipeline
        const pipelines = [
            { $match: conditions },
            {
                $project: {
                    user_id: 1,
                    points: 1,
                    pending_points: 1,
                    type: 1,
                    expired_on: 1,
                    is_expired: 1,
                    created_at: 1
                }
            },
            { $sort: sort }
        ];

        pipelines.push({
            '$skip': skip
        });
        pipelines.push({
            '$limit': limit
        });

        // Aggregate user coins history
        const topUpHistory = await UserCoinsHistory.aggregate(pipelines);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: topUpHistory
        };

        // Return successful response with user top-up history
        return ReS(res, constants.success_code, 'User Top-Up History Fetched', responseObj);
    } catch (err) {
        // Log and handle errors
        console.error('Error at getUserTopUpHistory function:', err);
        return sendError(res, err);
    }
}

async function getUserLikedComponents(req, res) {
    try {
        // Set default conditions for likes
        const conditions = {
            liked_by: req.params.id,
            is_active: true
        };
        const componentIds = (await ComponentLikes.distinct('component_id', conditions)).map((id) => new mongoose.Types.ObjectId(id));
        // Set default conditions for components
        const filters = {
            '_id': {
                $in: componentIds
            },
            'component_state': componentState.PUBLISHED
        };
        const totalDocuments = await Components.countDocuments(filters);

        // Set default sort
        const sort = {
            'created_at': -1
        };
        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(filters);

        const query = [{
            $match: filters
        }, {
            $lookup: {
                from: 'categories',
                let: {
                    category_id: '$category_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$category_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        category_name: 1,
                        category_slug: 1
                    }
                }],
                as: 'category'
            }
        }, {
            $lookup: {
                from: 'component_statistics',
                let: {
                    component_id: '$_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$component_id', '$$component_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'likes.total_likes': 1,
                        'views.total_views': 1,
                        'bookmarks.total_bookmarks': 1
                    }
                }],
                as: 'component_statistics'
            }
        }, {
            $unwind: {
                path: '$component_statistics',
                preserveNullAndEmptyArrays: true
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                description: 1,
                long_description: 1,
                created_at: 1,
                version: 1,
                is_paid: 1,
                views: { '$ifNull': ['$component_statistics.views.total_views', 0] },
                likes: { '$ifNull': ['$component_statistics.likes.total_likes', 0] },
                bookmarks: { '$ifNull': ['$component_statistics.bookmarks.total_bookmarks', 0] },
                category: {
                    $arrayElemAt: ['$category', 0]
                }
            }
        }];

        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });

        const componentList = await Components.aggregate(query);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllLikedComponents${err}`);
        return sendError(res, err);
    }
}

async function getUserBookMarkedComponents(req, res) {
    try {
        // Set default conditions for bookmark
        const conditions = {
            bookmarked_by: req.params.id,
            is_active: true
        };
        const componentIds = (await ComponentBookmarks.distinct('component_id', conditions)).map((id) => new mongoose.Types.ObjectId(id));
        // Set default conditions for components
        const filters = {
            '_id': {
                $in: componentIds
            },
            'component_state': componentState.PUBLISHED
        };
        const totalDocuments = await Components.countDocuments(filters);

        // Set default sort
        const sort = {
            'created_at': -1
        };
        const limit = (req.body.limit) ? req.body.limit : 12;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(filters);

        const query = [{
            $match: filters
        }, {
            $lookup: {
                from: 'component_statistics',
                let: {
                    component_id: '$_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$component_id', '$$component_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'likes.total_likes': 1,
                        'views.total_views': 1,
                        'bookmarks.total_bookmarks': 1
                    }
                }],
                as: 'component_statistics'
            }
        }, {
            $unwind: {
                path: '$component_statistics',
                preserveNullAndEmptyArrays: true
            }
        }, {
            $lookup: {
                from: 'categories',
                let: {
                    category_id: '$category_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$category_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        category_name: 1,
                        category_slug: 1
                    }
                }],
                as: 'category'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                description: 1,
                long_description: 1,
                created_at: 1,
                version: 1,
                is_paid: 1,
                views: { '$ifNull': ['$component_statistics.views.total_views', 0] },
                likes: { '$ifNull': ['$component_statistics.likes.total_likes', 0] },
                bookmarks: { '$ifNull': ['$component_statistics.bookmarks.total_bookmarks', 0] },
                category: {
                    $arrayElemAt: ['$category', 0]
                }
            }
        }];

        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });

        const componentList = await Components.aggregate(query);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllBookMarkedComponents${err}`);
        return sendError(res, err);
    }
}

async function getUserFingerPrintFacetData(req, res) {
    try {
        const fingerprintFacet = await getFingerprintUserFacets(req.params.id);

        return ReS(res, constants.success_code, 'Data Fetched', fingerprintFacet);
    } catch (err) {
        logger.error(`Error at CMS Controller getUserFingerPrintFacetData${err}`);
        return sendError(res, err);
    }
}

async function getUserFingerPrintData(req, res) {
    try {
        const aggregate = [
            {
                $match: {
                    userId: new mongoose.Types.ObjectId(req.params.id)
                }
            }
        ];
        // Run above aggregation pipeline
        const fingerprints = await Fingerprint.aggregate(aggregate);
        return ReS(res, constants.success_code, 'Data Fetched', fingerprints);
    } catch (err) {
        logger.error(`Error at CMS Controller getUserFingerPrintData${err}`);
        return sendError(res, err);
    }
}

async function updateUserAuthorFeesConfig(req, res) {
    try {
        const { is_author_fee_overridden, overridden_author_fee } = req.body;

        const userData = await Users.findOne({ _id: req.params.id }).lean();

        if (!userData) {
            return ReS(res, constants.resource_not_found, 'Oops! User Not Found.');
        }

        await Users.updateOne({
            _id: req.params.id
        }, {
            $set: {
                is_author_fee_overridden: is_author_fee_overridden,
                overridden_author_fee: overridden_author_fee
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        console.error(`Error at CMS Controller updateUserAuthorFeesConfig${err}`);
        return sendError(res, err);
    }
}

async function updateUserBuyerFeesConfig(req, res) {
    try {
        const { is_buyer_fee_overridden, overridden_buyer_fee } = req.body;

        const userData = await Users.findOne({ _id: req.params.id }).lean();

        if (!userData) {
            return ReS(res, constants.resource_not_found, 'Oops! User Not Found.');
        }

        await Users.updateOne({
            _id: req.params.id
        }, {
            $set: {
                is_buyer_fee_overridden: is_buyer_fee_overridden,
                overridden_buyer_fee: overridden_buyer_fee
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        console.error(`Error at CMS Controller updateUserBuyerFeesConfig${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    getAllUsers,
    updateUserStatus,
    getUserDetails,
    getUserMpcBalance,
    getUserComponentUnlockHistory,
    getUserTopUpHistory,
    getUserLikedComponents,
    getUserBookMarkedComponents,
    getUserFingerPrintFacetData,
    getUserFingerPrintData,
    updateUserAuthorFeesConfig,
    updateUserBuyerFeesConfig
};