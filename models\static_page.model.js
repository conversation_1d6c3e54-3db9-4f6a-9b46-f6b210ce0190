const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const StaticPageSchema = new Schema({
    title: {
        type: String,
        require: true
    },
    slug: {
        type: String,
        require: true
    },
    description: {
        type: String,
        require: true
    },
    is_active: {
        type: Boolean,
        default: true
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const StaticPage = mongoose.model('static_pages', StaticPageSchema);

module.exports = {
    StaticPage
};