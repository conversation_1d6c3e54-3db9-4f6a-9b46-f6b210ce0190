const Joi = require('joi');

const { gitlabCommitActions } = require('../../../config/gitlab.constant');

class RepositoryValidation {
    createRepository(params) {
        const schema = Joi.object({
            project_name: Joi.string().required(),
            platform_id: Joi.string().required()
        });
        return schema.validate(params);
    }

    createCommit(params) {
        const schema = Joi.object({
            actions: Joi.array().items(
                Joi.object({
                    action: Joi.string().valid(...Object.values(gitlabCommitActions)).required(),
                    file_path: Joi.string().required(),
                    content: Joi.string().optional()
                }).required()
            ).required(),
            commit_message: Joi.string().required()
        });
        return schema.validate(params);
    }

    createCommitWithFiles(params) {
        const schema = Joi.object({
            commit_message: Joi.string().required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new RepositoryValidation();