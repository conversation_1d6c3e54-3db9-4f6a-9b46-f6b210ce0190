const { createCategory, updateCategory, updateCategoryStatus } = require('../../../../validations/cms/category/categoryValidation');
class CategoryValidationMiddleware {
    createCategoryValidation(req, res, next) {
        const { value, error } = createCategory(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateCategoryValidation(req, res, next) {
        const { value, error } = updateCategory(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateCategoryStatusValidation(req, res, next) {
        const { value, error } = updateCategoryStatus(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new CategoryValidationMiddleware();