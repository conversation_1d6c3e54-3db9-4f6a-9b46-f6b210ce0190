const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const userNotificationSchema = new Schema({
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    notification_id: {
        type: mongoose.Types.ObjectId,
        ref: 'notifications'
    },
    is_read: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const UserNotifications = mongoose.model('user_notifications', userNotificationSchema);

module.exports = {
    UserNotifications
};