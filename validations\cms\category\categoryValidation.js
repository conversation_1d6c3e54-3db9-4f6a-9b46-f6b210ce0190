const Joi = require('joi');
const { categoryTypes } = require('../../../config/component.constant');
class CategoryValidation {
    createCategory(params) {
        const schema = Joi.object({
            category_name: Joi.string().required(),
            image_url: Joi.string().optional().allow(null, ''),
            short_description: Joi.string().optional().allow(null, ''),
            is_active: Joi.boolean().optional(),
            category_type: Joi.array().items(Joi.string().trim().valid(...Object.values(categoryTypes))).min(1).unique().required(),
            parent_id: Joi.string()
                .optional()
                .allow(null, '')
                .regex(/^[0-9a-fA-F]{24}$/)
                .message('Invalid parent ID format')
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updateCategory(params) {
        const schema = Joi.object({
            category_name: Joi.string().required(),
            image_url: Joi.string().optional().allow(null, ''),
            short_description: Joi.string().optional().allow(null, ''),
            is_active: Joi.boolean().optional(),
            category_type: Joi.array().items(Joi.string().trim().valid(...Object.values(categoryTypes))).min(1).unique().required(),
            parent_id: Joi.string()
                .optional()
                .allow(null, '')
                .regex(/^[0-9a-fA-F]{24}$/)
                .message('Invalid parent ID format')
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updateCategoryStatus(params) {
        const schema = Joi.object({
            is_active: Joi.boolean().required()
        });
        return schema.validate(params);
    }
}

module.exports = new CategoryValidation();