const Joi = require('joi');

const { gitlabCommitActions } = require('../../../config/gitlab.constant');

class RepositoryValidation {
    createCommit(params) {
        const schema = Joi.object({
            actions: Joi.array().items(
                Joi.object({
                    action: Joi.string().valid(...Object.values(gitlabCommitActions)).required(),
                    file_path: Joi.string().required(),
                    content: Joi.string().optional()
                }).required()
            ).required(),
            commit_message: Joi.string().required()
        });
        return schema.validate(params);
    }

    createCommitWithFiles(params) {
        const schema = Joi.object({
            commit_message: Joi.string().required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    createBranch(params) {
        const schema = Joi.object({
            branch_name: Joi.string()
                .required()
                .pattern(/^(?!\.|.*\.\.|.*@\{|[\x00-\x1F\x7F ~^:?*\[\]])[^\x00-\x1F\x7F ~^:?*\[\]]{1,255}$/)
                .messages({
                    'string.pattern.base': 'Branch name contains invalid characters',
                    'any.required': 'Branch name is required'
                })
                .trim(),
            ref_branch: Joi.string()
                .optional()
                .default('development')
                .pattern(/^[a-zA-Z0-9_\-.\/]+$/)
                .message('Reference branch contains invalid characters')
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new RepositoryValidation();