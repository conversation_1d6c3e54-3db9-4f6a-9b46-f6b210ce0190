const Joi = require('joi');

const { mpcBonusRequestStatus } = require('../../../config/component.constant');
class BonusPintRequestValidation {
    approveOrRejectRequest(params) {
        const schema = Joi.object({
            mpc_bonus: Joi.number().required(),
            status: Joi.string().valid(...Object.values(mpcBonusRequestStatus)).required()
        });
        return schema.validate(params);
    }
}

module.exports = new BonusPintRequestValidation();