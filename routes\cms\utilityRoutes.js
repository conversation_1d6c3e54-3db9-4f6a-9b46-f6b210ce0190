const express = require('express');
const router = express.Router();
const { uploadFile, uploadFileBase64, decryptWithAES, encryptWithAES, loginToGitLab } = require('../../controller/cms/utility.controller');

router.post('/upload-file', uploadFile);
router.post('/upload-file/base64', uploadFileBase64);

router.post('/decrypt/aes', decryptWithAES);
router.post('/encrypt/aes', encryptWithAES);
router.get('/login/gitLab', loginToGitLab);

module.exports = router;