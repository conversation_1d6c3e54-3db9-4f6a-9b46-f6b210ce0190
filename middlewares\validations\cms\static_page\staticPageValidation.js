const { createStaticPage, updateStaticPage, updateStaticPageStatus } = require('../../../../validations/cms/static_pages/staticPageValidation');

class StaticPageValidationMiddleware {
    createStaticPageValidation(req, res, next) {
        const { value, error } = createStaticPage(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateStaticPageValidation(req, res, next) {
        const { value, error } = updateStaticPage(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateStaticPageStatusValidation(req, res, next) {
        const { value, error } = updateStaticPageStatus(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new StaticPageValidationMiddleware(); 