const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { reportTypes, reportStatus } = require('../config/component.constant');

const ComponentReportsSchema = new Schema({
    component_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'components',
        required: true
    },
    reported_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    report_type: {
        type: String,
        enum: Object.values(reportTypes),
        required: true
    },
    additional_info: {
        type: String,
        trim: true
    },
    status: {
        type: String,
        enum: Object.values(reportStatus),
        default: reportStatus.PENDING
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const ComponentReports = mongoose.model('component_reports', ComponentReportsSchema);

module.exports = {
    ComponentReports
};