const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const TopUpPlansSchema = new Schema({
    plan_name: {
        type: String,
        required: true
    },
    plan_slug: {
        type: String,
        required: true
    },
    plan_media: {
        type: String
    },
    plan_coins: {
        type: Number,
        required: true,
        default: 0
    },
    bonus_coins: {
        type: Number,
        required: true,
        default: 0
    },
    bonus_percentage: {
        type: Number,
        required: true,
        default: 0
    },
    total_coins: {
        type: Number,
        required: true,
        default: 0
    },
    plan_price: {
        type: Number,
        required: true,
        default: 0
    },
    is_active: {
        type: Boolean,
        default: true
    },
    is_deleted: {
        type: Boolean,
        default: false
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const TopUpPlans = mongoose.model('top_up_plans', TopUpPlansSchema);

module.exports = {
    TopUpPlans
};