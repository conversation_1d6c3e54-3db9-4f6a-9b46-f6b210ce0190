// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, escapeRegex, sendError } = require('../../services/general.helper');

// Models declaration
const AdminPermissions = require('../../models/admin_permissions.model').AdminPermissions;

async function createPermission(req, res) {
    try {
        const {
            display_name,
            module,
            name,
            is_active
        } = req.body;

        await AdminPermissions.create({
            display_name: display_name,
            name: name,
            module: module,
            is_active: is_active,
            created_by: req.session._id
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createPermission ${err}`);
        return sendError(res, err);
    }
}

async function getAllPermissions(req, res) {
    try {
        const totalDocuments = await AdminPermissions.countDocuments();
        const filter = {};
        if (req.body.is_active != undefined) {
            filter.is_active = req.body.is_active;
        }
        if (req.body.display_name) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.display_name);
            filter['$or'] = [{
                'display_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        // Default sort
        const sort = {
            'created_at': -1
        };
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        // Count filtered documents after filter apply
        const filterDocuments = await AdminPermissions.countDocuments(filter);
        const permissionList = await AdminPermissions.find(filter, {
            'display_name': 1,
            'name': 1,
            'module': 1,
            'is_active': 1,
            'created_at': 1
        }).sort(sort).skip(skip).limit(limit).lean();

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: permissionList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllPermissions ${err}`);
        return sendError(res, err);
    }
}

async function updatePermissionStatus(req, res) {
    try {
        const { is_active } = req.body;

        const permission = await AdminPermissions.findOne({
            _id: req.params.id
        }).lean();

        if (permission == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Admin Permission Not Found.');
        }

        await AdminPermissions.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active,
                updated_by: req.session._id
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updatePermissionStatus ${err}`);
        return sendError(res, err);
    }
}

async function getAllPermissionSortList(req, res) {
    try {
        const filter = {
            is_active: true
        };
        if (req.body.is_active != undefined) {
            filter.is_active = req.body.is_active;
        }
        if (req.body.display_name) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.display_name);
            filter['$or'] = [{
                'display_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        // Default sort
        const sort = {
            'created_at': -1
        };
        const permissionList = await AdminPermissions.find(filter, {
            'display_name': 1,
            'name': 1,
            'module': 1,
            'is_active': 1,
            'created_at': 1
        }).sort(sort).lean();

        return ReS(res, constants.success_code, 'Data Fetched', permissionList);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllPermissionSortList ${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createPermission,
    getAllPermissions,
    getAllPermissionSortList,
    updatePermissionStatus
};