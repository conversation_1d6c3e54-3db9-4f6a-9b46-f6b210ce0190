const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const EmailTemplateSchema = new Schema({
    title: {
        type: String,
        trim: true,
        required: true
    },
    subject: {
        type: String,
        required: true
    },
    template_description: {
        type: String,
        trim: true,
        required: true
    },
    slug: {
        type: String,
        required: true,
        lowercase: true,
        trim: true,
        index: true,
        unique: true
    },
    dynamic_values: {
        type: String,
        required: true
    },
    template_banner: {
        type: String,
        required: true
    },
    language: {
        type: String,
        required: true
    },
    version: {
        type: Number,
        default: 1
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

EmailTemplateSchema.set('autoIndex', true);

const EmailTemplates = mongoose.model('email_templates', EmailTemplateSchema);

module.exports = {
    EmailTemplates
};