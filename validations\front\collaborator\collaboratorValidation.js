const Joi = require('joi');

const { gitlabAccessLevel } = require('../../../config/gitlab.constant');

class CollaboratorValidation {
    addCollaborator(params) {
        const schema = Joi.object({
            user_id: Joi.string().required(),
            repo_id: Joi.string().required(),
            access_level: Joi.string().valid(...Object.keys(gitlabAccessLevel)).required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    removeCollaborator(params) {
        const schema = Joi.object({
            user_id: Joi.string().required(),
            repo_id: Joi.string().required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    sendInvite(params) {
        const schema = Joi.object({
            email: Joi.string().email().required(),
            repo_id: Joi.string().required(),
            access_level: Joi.string().valid(...Object.keys(gitlabAccessLevel)).required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    verifyInvite(params) {
        const schema = Joi.object({
            invite_token: Joi.string().uuid({ version: 'uuidv4' }).required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

}

module.exports = new CollaboratorValidation();