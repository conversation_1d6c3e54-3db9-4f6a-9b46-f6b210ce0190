const { createModifier, updateModifier } = require('../../../../validations/cms/modifier/modifierValidation');
class ModifierValidationMiddleware {
    createModifierValidation(req, res, next) {
        const { value, error } = createModifier(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateModifierValidation(req, res, next) {
        const { value, error } = updateModifier(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new ModifierValidationMiddleware();