const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { gitlabAccessLevel } = require('../config/gitlab.constant');

const CollaboratorSchema = new Schema({
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    },
    gitlab_user_id: {
        type: Number, // This will store the original GitLab user ID as a String
        required: true
    },
    gitlab_user_ref: {
        type: mongoose.Types.ObjectId, // This will reference the _id of the GitlabUsersSchema
        ref: 'gitlab_users',
        required: true
    },
    repo_id: {
        type: mongoose.Types.ObjectId,
        ref: 'gitlab_repository',
        required: true
    },
    access_level: {
        type: Number,
        enum: Object.values(gitlabAccessLevel),
        default: gitlabAccessLevel.DEVELOPER,
        required: true
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const Collaborator = mongoose.model('collaborator', CollaboratorSchema);

module.exports = {
    Collaborator
};
