// Socket.IO service for real-time notifications
const socketIO = require('socket.io');
const logger = require('../config/logger');
const { authenticateSocket } = require('../middlewares/socket.middleware');

// Store active connections with user IDs as keys and socket IDs as values
const activeConnections = new Map();

// Initialize Socket.IO with the HTTP server
let io;

/**
 * Initialize the Socket.IO server
 * @param {Object} server - HTTP server instance
 */
const initializeSocketServer = (server) => {
    try {
        // Create Socket.IO server with CORS configuration
        io = socketIO(server, {
            cors: {
                origin: '*',
                methods: ['GET', 'POST'],
                credentials: true
            }
        });

        logger.info('Socket.IO server created with CORS configuration');

        // Log all middleware errors for debugging
        io.engine.on('connection_error', (err) => {
            logger.error(`Socket.IO connection error: ${err.code} - ${err.message}`);
        });

        // Use authentication middleware
        io.use((socket, next) => {
            logger.info(`Socket authentication attempt: ${socket.id}`);
            authenticateSocket(socket, next);
        });

        // Handle new socket connections
        io.on('connection', async (socket) => {
            try {
                const userId = socket.user._id.toString();
                logger.info(`New authenticated socket connection: ${socket.id} for user ${userId}`);
                logger.info(`Active connections before adding: ${activeConnections.size}`);

                // Store the user's socket connection
                activeConnections.set(userId, socket.id);
                logger.info(`Active connections after adding: ${activeConnections.size}`);
                logger.info(`Current active users: ${Array.from(activeConnections.keys()).join(', ')}`);

                // Join a room specific to this user for targeted events
                socket.join(`user:${userId}`);
                logger.info(`User ${userId} joined room: user:${userId}`);

                // Get current unread notification count for this user
                try {
                    const { UserNotifications } = require('../models/user_notification.model');

                    // Get unread count only
                    const unreadCount = await UserNotifications.countDocuments({
                        user_id: userId,
                        is_read: false
                    });

                    logger.info(`Sending initial notification count to user ${userId}: ${unreadCount}`);

                    // Send initial connection confirmation with unread count only
                    socket.emit('connected', {
                        status: 'success',
                        message: 'Successfully connected to notification service',
                        unread_count: unreadCount
                    });

                    // Also emit the count update event for consistency
                    socket.emit('notification_count_update', { count: unreadCount });
                } catch (countError) {
                    logger.error(`Error getting initial notification count for user ${userId}: ${countError}`);

                    // Send connection confirmation without count
                    socket.emit('connected', {
                        status: 'success',
                        message: 'Successfully connected to notification service'
                    });
                }

                // Handle disconnection
                socket.on('disconnect', () => {
                    logger.info(`Socket disconnected: ${socket.id} for user ${userId}`);
                    logger.info(`Active connections before removing: ${activeConnections.size}`);

                    // Remove the disconnected socket from activeConnections
                    activeConnections.delete(userId);
                    logger.info(`Active connections after removing: ${activeConnections.size}`);
                });
            } catch (err) {
                logger.error(`Error in socket connection handler: ${err}`);
            }
        });

        logger.info('Socket.IO server initialized successfully');
    } catch (error) {
        logger.error(`Error initializing Socket.IO server: ${error}`);
    }
};

/**
 * Send a notification to a specific user
 * @param {String} userId - The ID of the user to send the notification to
 * @param {Object} notification - The notification data
 */
const sendNotificationToUser = (userId, notification) => {
    try {
        logger.info(`Attempting to send notification to user ${userId}`);

        if (!io) {
            logger.error('Socket.IO server not initialized');
            return;
        }

        // Check if user is connected
        const isConnected = activeConnections.has(userId);
        logger.info(`User ${userId} connection status: ${isConnected ? 'Connected' : 'Not connected'}`);

        const socketId = activeConnections.get(userId);
        logger.info(`Socket ID for user ${userId}: ${socketId || 'Not found'}`);

        if (!socketId) {
            logger.error(`Socket not found for user ${userId}`);
            return;
        }

        // Log active connections for debugging
        logger.info(`Current active connections: ${activeConnections.size}`);
        logger.info(`Active users: ${Array.from(activeConnections.keys()).join(', ')}`);

        // Emit to the user's room
        logger.info(`Emitting 'new_notification' event to room user:${userId}`);
        io.to(`user:${userId}`).emit('new_notification', notification);

        // // Also try emitting directly to the socket
        // logger.info(`Also emitting directly to socket ${socketId}`);
        // io.to(socketId).emit('new_notification', notification);

        logger.info(`Notification sent to user ${userId}`);
    } catch (error) {
        logger.error(`Error sending notification to user ${userId}: ${error}`);
    }
};

/**
 * Send notification count update to a specific user
 * @param {String} userId - The ID of the user to send the count to
 * @param {Number} count - The updated notification count
 */
const sendNotificationCountToUser = (userId, count) => {
    try {
        logger.info(`Attempting to send notification count update to user ${userId}: ${count}`);

        if (!io) {
            logger.error('Socket.IO server not initialized');
            return;
        }

        // Check if user is connected
        const isConnected = activeConnections.has(userId);
        logger.info(`User ${userId} connection status: ${isConnected ? 'Connected' : 'Not connected'}`);

        const socketId = activeConnections.get(userId);
        logger.info(`Socket ID for user ${userId}: ${socketId || 'Not found'}`);

        if (!socketId) {
            logger.error(`Socket not found for user ${userId}`);
            return;
        }

        // Log active connections for debugging
        logger.info(`Current active connections: ${activeConnections.size}`);

        // Emit to the user's room
        logger.info(`Emitting 'notification_count_update' event to room user:${userId}`);
        io.to(`user:${userId}`).emit('notification_count_update', { count });

        // // Also try emitting directly to the socket
        // logger.info(`Also emitting directly to socket ${socketId}`);
        // io.to(socketId).emit('notification_count_update', { count });

        logger.info(`Notification count update sent to user ${userId}: ${count}`);
    } catch (error) {
        logger.error(`Error sending notification count to user ${userId}: ${error}`);
    }
};

/**
 * Check if a user is currently connected
 * @param {String} userId - The ID of the user to check
 * @returns {Boolean} - Whether the user is connected
 */
const isUserConnected = (userId) => {
    const isConnected = activeConnections.has(userId);
    logger.info(`Checking if user ${userId} is connected: ${isConnected}`);

    if (!isConnected) {
        logger.info(`User ${userId} not found in active connections. Current active users: ${Array.from(activeConnections.keys()).join(', ')}`);
    }

    return isConnected;
};

/**
 * Get the socket ID for a specific user
 * @param {String} userId - The ID of the user
 * @returns {String|null} - The socket ID or null if not found
 */
const getUserSocketId = (userId) => {
    return activeConnections.get(userId) || null;
};

module.exports = {
    initializeSocketServer,
    sendNotificationToUser,
    sendNotificationCountToUser,
    isUserConnected,
    getUserSocketId
};
