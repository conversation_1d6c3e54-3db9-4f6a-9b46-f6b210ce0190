const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const UserMpcBalanceSchema = new Schema({
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    mpn_points: {
        type: Number,
        required: true,
        default: 0
    },
    mpn_bonus_points: {
        type: Number,
        required: true,
        default: 0
    },
    fiat: {
        type: Number,
        required: true,
        default: 0
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const UserMpcBalance = mongoose.model('user_mpc_balance', UserMpcBalanceSchema);

module.exports = {
    UserMpcBalance
};