const mongoose = require('mongoose');
const getCountryISO3 = require('country-iso-2-to-3');
const logger = require('../config/logger');
const Schema = mongoose.Schema;

const ComponentStatistics = require('./component_statistics.model').ComponentStatistics;
const Components = require('./component.model').Components;
const Users = require('./users.model').Users;

const componentLikeSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    creator: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    liked_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    visitor_id: {
        type: String
    },
    request_id: {
        type: String
    },
    city: {
        name: {
            type: String
        }
    },
    country: {
        code: {
            type: String
        },
        name: {
            type: String
        }
    },
    continent: {
        code: {
            type: String
        },
        name: {
            type: String
        }
    },
    subdivisions: {
        iso_code: {
            type: String
        },
        name: {
            type: String
        }
    },
    is_active: {
        type: Boolean,
        default: true
    },
    is_deleted: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

// Define a post-save hook for the componentLikeSchema
componentLikeSchema.post('save', async function () {
    try {
        // Capture the reference to the current context
        const currentContext = this;

        // Check if the context and component_id exist
        if (currentContext && currentContext.component_id) {
            // Determine the country based on the provided code, defaulting to 'GLOBAL' if not found
            const country = (currentContext.country && currentContext.country.code) ? await getCountryISO3(currentContext.country.code) : 'GLOBAL';

            // Prepare update query to increment total likes and country-specific likes
            const updateQuery = {
                $inc: {
                    'likes.total_likes': 1,
                    [`likes.countries.${country}`]: 1
                }
            };

            // Update or insert the ComponentStatistics document
            await ComponentStatistics.updateOne(
                { component_id: currentContext.component_id }, // Filter by component_id
                updateQuery, // Update query
                { upsert: true } // Create a new document if not found
            );

            // Update likes in component document as well
            await Components.updateOne({
                _id: currentContext.component_id
            }, {
                $inc: {
                    likes: 1
                }
            });

            // Fetch the component by ID and retrieve the 'created_by_user' field
            const { created_by_user } = await Components.findById(
                currentContext.component_id,
                'created_by_user'
            ).lean() || {};

            // If the 'created_by_user' field exists, increment their 'total_likes'
            if (created_by_user) {
                await Users.updateOne({
                    _id: created_by_user
                }, {
                    $inc: {
                        total_likes: 1
                    }
                });
            }
        }
    } catch (error) {
        // Log any errors that occur during the operation
        logger.error(`Error occurred in the post-save hook of componentLikeSchema:${error}`);
    }
});


// Define a post-deleteOne hook for the componentLikeSchema
componentLikeSchema.post('findOneAndDelete', async function (doc) {
    try {
        // Access the deleted document from the 'doc' parameter
        const deletedDocument = doc;
        // Check if the context and component_id exist
        if (deletedDocument && deletedDocument.component_id) {
            // Determine the country based on the provided code, defaulting to 'GLOBAL' if not found
            const country = (deletedDocument.country && deletedDocument.country.code) ? await getCountryISO3(deletedDocument.country.code) : 'GLOBAL';

            // Prepare update query to increment total likes and country-specific likes
            const updateQuery = {
                $inc: {
                    'likes.total_likes': -1,
                    [`likes.countries.${country}`]: -1
                }
            };

            // Update or insert the ComponentStatistics document
            await ComponentStatistics.updateOne(
                { component_id: deletedDocument.component_id }, // Filter by component_id
                updateQuery, // Update query
                { upsert: true } // Create a new document if not found
            );

            // Update likes in component document as well
            await Components.updateOne({
                _id: deletedDocument.component_id
            }, {
                $inc: {
                    likes: -1
                }
            });

            // Fetch the component by ID and retrieve the 'created_by_user' field
            const { created_by_user } = await Components.findById(
                deletedDocument.component_id,
                'created_by_user'
            ).lean() || {};

            // If the 'created_by_user' field exists, decrement their 'total_likes'
            if (created_by_user) {
                await Users.updateOne({
                    _id: created_by_user
                }, {
                    $inc: {
                        total_likes: -1
                    }
                });
            }
        }
    } catch (error) {
        // Log any errors that occur during the operation
        logger.error(`Error occurred in the post-deleteOne hook of componentLikeSchema:${error}`);
    }
});

const ComponentLikes = mongoose.model('component_likes', componentLikeSchema);

module.exports = {
    ComponentLikes
};