const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const AdminRolesSchema = new Schema({
    role: {
        type: String,
        required: true
    },
    slug: {
        type: String,
        required: true,
        unique: true
    },
    permission_groups: [{
        type: mongoose.Types.ObjectId,
        required: true,
        ref: 'admin_permission_groups'
    }],
    is_active: {
        type: Boolean,
        default: false
    },
    created_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    updated_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const AdminRoles = mongoose.model('admin_roles', AdminRolesSchema);

module.exports = {
    AdminRoles
};