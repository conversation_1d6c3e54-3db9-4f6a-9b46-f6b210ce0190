// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, sendError, escapeRegex } = require('../../services/general.helper');

// Models declaration
const Template = require('../../models/template_zip.model').Templates;

async function createTemplate(req, res) {
    try {
        const {
            title,
            description,
            zip_url
        } = req.body;
        // Check if the zip with provided title already exists
        const template = await Template.findOne({ title }).lean();

        if (template) {
            // If template already exists, return conflict status with user details
            return ReS(res, constants.accepted_code, 'Oops! template already registered', {
                template_id: template._id, zip_url: template.zip_url
            });
        }


        await Template.create({
            title: title,
            zip_url: zip_url,
            description: description
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createTemplate${err}`);
        return sendError(res, err);
    }
}

async function getSingleTemplate(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };

        const templateData = await Template.findOne(filter).lean();

        if (templateData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! template Not Found.');
        }

        return ReS(res, constants.success_code, 'Data Fetched', templateData);
    } catch (err) {
        logger.error(`Error at CMS Controller getSingleTemplate${err}`);
        return sendError(res, err);
    }
}

async function getTemplates(req, res) {
    try {
        // Get total count of documents
        const totalDocuments = await Template.countDocuments();
        // Set default conditions
        const conditions = {};
        // Set default sort
        const sort = {
            'created_at': -1
        };
        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        const filterDocuments = await Template.countDocuments(conditions);
        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                zip_url: 1,
                description: 1,
                created_at: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });
        // Query for a template of data
        const templateList = await Template.aggregate(query);
        return ReS(res, constants.success_code, 'Data Fetched', {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: templateList
        });
    } catch (err) {
        logger.error(`Error at CMS Controller getTemplates${err}`);
        return sendError(res, err);
    }
}

async function updateTemplate(req, res) {
    try {

        const filter = {
            _id: req.params.id
        };

        const templateData = await Template.findOne(filter).lean();

        if (templateData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! template Not Found.');
        }

        await Template.updateOne({
            _id: req.params.id
        }, {
            '$set': req.body
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateTemplate${err}`);
        return sendError(res, err);
    }
}

async function deleteTemplate(req, res) {
    try {
        const filter = {
            _id: req.params.id
        };

        const templateData = await Template.findOne(filter).lean();

        if (templateData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! template Not Found.');
        }

        await Template.deleteOne(filter).then((data) => {
            return ReS(res, constants.success_code, 'Success', data);
        }).catch((err) => {
            throw new Error(err);
        });

    } catch (err) {
        logger.error(`Error at CMS Controller deleteTemplate${err}`);
        return sendError(res, err);
    }
}

async function updateTemplateStatus(req, res) {
    try {
        const { is_active } = req.body;


        const filter = {
            _id: req.params.id
        };

        const templateData = await Template.findOne(filter).lean();

        if (templateData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! template Not Found.');
        }

        await Template.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateTemplateStatus${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createTemplate,
    getSingleTemplate,
    getTemplates,
    updateTemplate,
    deleteTemplate,
    updateTemplateStatus
};