const express = require('express');
const router = express.Router();

const { getUserDetails, getUserMpcBalance, getUserComponentUnlockHistory, getUnlockedComponents, getUnlockedComponentSlugs, getUserTopUpHistory, uploadProfilePicture, updateUserProfile, submitBonusRequest, createGitlabUser, downloadGitlabCredentials, checkGitlabUserNameAvailability, getUserGitlabCredentials, getProfileStatistics, getPublishedComponents, generateGitlabAccessToken, getActiveDraftComponents, userLogout, sendOTPForSecurityAction, verifyNewEmail, fetchUserPointHistory, fetchUserFiatHistory, fetchUserSalesHistory, fetchUserCodeSpaceHistory } = require('../../controller/front/users.controller');

const { editProfileValidation, createGitlabAccountValidation, sendOTPForSecurityActionValidation, updateUserEmailValidation } = require('../../middlewares/validations/front/users/userValidation');

router.get('/me/get-details', getUserDetails);
router.get('/me/get-mpc-balance', getUserMpcBalance);

router.post('/me/get-component-unlock-history', getUserComponentUnlockHistory);
router.post('/me/get-unlocked-components', getUnlockedComponents);

router.get('/me/get-unlocked-components/slugs', getUnlockedComponentSlugs);
router.post('/me/get-top-up-history', getUserTopUpHistory);
router.post('/upload/profile-picture', uploadProfilePicture);
router.put('/me/update/profile', editProfileValidation, updateUserProfile);
router.post('/me/mpc-bonus-request/submit', submitBonusRequest);

router.post('/me/creator-account', createGitlabAccountValidation, createGitlabUser);
router.get('/me/download/gitlab-credentials', downloadGitlabCredentials);
router.get('/check/username-availability', checkGitlabUserNameAvailability);
router.get('/get/gitlab-credentials', getUserGitlabCredentials);

router.get('/me/get-profile-statistics', getProfileStatistics);
router.post('/me/get-published-components', getPublishedComponents);

router.post('/me/generate/access-token', generateGitlabAccessToken);
router.post('/me/get-draft-components', getActiveDraftComponents);

router.post('/me/logout', userLogout);

router.post('/me/send-otp-security-methods', sendOTPForSecurityActionValidation, sendOTPForSecurityAction);

router.put('/me/verify-new-email', updateUserEmailValidation, verifyNewEmail);

router.post('/me/point-history', fetchUserPointHistory);

router.post('/me/fiat-history', fetchUserFiatHistory);

router.post('/me/sales-history', fetchUserSalesHistory);

router.post('/me/code-space-history', fetchUserCodeSpaceHistory);

module.exports = router;