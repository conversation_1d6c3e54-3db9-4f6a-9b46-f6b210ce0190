const express = require('express');
const router = express.Router();

const { getSingleStaticPage, fetchRepositoryAssets, fetchRepositoryAssetsDevelopment } = require('../../controller/front/staticPages.controller');

router.get('/details/:slug', getSingleStaticPage);
router.get('/fetch-assets/:id/:path', fetchRepositoryAssets);
router.get('/fetch-assets/development/:id/:path', fetchRepositoryAssetsDevelopment);

module.exports = router;