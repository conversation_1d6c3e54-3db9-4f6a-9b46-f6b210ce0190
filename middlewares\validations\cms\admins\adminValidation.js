const { adminLogin, createAdmin, updateAdminStatus, updateAdmin, changePassword, createGitlabAccount } = require('../../../../validations/cms/admins/adminValidation');
class UserValidationMiddleware {
    adminLoginValidation(req, res, next) {
        const { value, error } = adminLogin(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createAdminValidation(req, res, next) {
        const { value, error } = createAdmin(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateAdminStatusValidation(req, res, next) {
        const { value, error } = updateAdminStatus(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateAdminValidation(req, res, next) {
        const { value, error } = updateAdmin(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    changePasswordValidation(req, res, next) {
        const { value, error } = changePassword(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createGitlabAccountValidation(req, res, next) {
        const { value, error } = createGitlabAccount(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new UserValidationMiddleware(); 