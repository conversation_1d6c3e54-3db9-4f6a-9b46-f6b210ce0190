// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, sendError } = require('../../services/general.helper');
const createSlug = require('../../services/slug.service');

// Models declaration
const EmailTemplates = require('../../models/email_templates.model').EmailTemplates;

async function updateEmailTemplatesFromConfig(req, res) {
    try {
        const emailTemplatesToMigrate = require('../../config/initial_data/email_templates.json');
        const emailAdded = []; const emailUpdated = [];
        for (const emailTemplateToMigrate of emailTemplatesToMigrate) {
            try {

                if ([
                    emailTemplateToMigrate.dynamic_values,
                    emailTemplateToMigrate.language,
                    emailTemplateToMigrate.slug,
                    emailTemplateToMigrate.template_description,
                    emailTemplateToMigrate.title,
                    emailTemplateToMigrate.version, emailTemplateToMigrate.subject,
                    emailTemplateToMigrate.template_banner].includes(undefined)) {
                    return ReS(res, constants.success_code, 'insufficient email data', { insufficient_email_data: emailTemplateToMigrate });
                }

                const existingEmailTemplateData = await EmailTemplates.findOne({
                    'slug': emailTemplateToMigrate.slug,
                    'language': emailTemplateToMigrate.language
                }).lean();

                if (existingEmailTemplateData == null) {
                    await EmailTemplates.create({
                        'slug': emailTemplateToMigrate.slug,
                        'language': emailTemplateToMigrate.language,
                        'template_description': emailTemplateToMigrate.template_description,
                        'title': emailTemplateToMigrate.title,
                        'dynamic_values': emailTemplateToMigrate.dynamic_values,
                        'subject': emailTemplateToMigrate.subject,
                        'template_banner': emailTemplateToMigrate.template_banner,
                        'version': emailTemplateToMigrate.version
                    });
                    emailAdded.push({ 'slug': emailTemplateToMigrate.slug, 'version': emailTemplateToMigrate.version, 'language': emailTemplateToMigrate.language });
                    logger.info(`Email template created successfully for slug: ${emailTemplateToMigrate.slug} with language ${emailTemplateToMigrate.language}`);
                } else if (existingEmailTemplateData && existingEmailTemplateData.version == undefined || (existingEmailTemplateData && existingEmailTemplateData.version < emailTemplateToMigrate.version)) {
                    await EmailTemplates.updateOne({
                        'slug': emailTemplateToMigrate.slug,
                        'language': emailTemplateToMigrate.language
                    }, {
                        '$set': {
                            'template_description': emailTemplateToMigrate.template_description,
                            'title': emailTemplateToMigrate.title,
                            'template_banner': emailTemplateToMigrate.template_banner,
                            'version': emailTemplateToMigrate.version,
                            'subject': emailTemplateToMigrate.subject
                        }
                    });
                    emailUpdated.push({ 'slug': emailTemplateToMigrate.slug, 'version': emailTemplateToMigrate.version, 'language': emailTemplateToMigrate.language });
                    logger.info(`Email template updated successfully for slug: ${emailTemplateToMigrate.slug} with language ${emailTemplateToMigrate.language} with version ${emailTemplateToMigrate.version}`);
                } else {
                    logger.info(`Email template for slug: ${emailTemplateToMigrate.slug} with language ${emailTemplateToMigrate.language} already up to date with version ${emailTemplateToMigrate.version}`);
                }

            } catch (error) {
                logger.error(`Error while updating email template for slug: ${emailTemplateToMigrate.slug}${error}`);
            }
        }
        return ReS(res, constants.success_code, 'Email Migration completed successfully', { emailAdded, emailUpdated });
    } catch (err) {
        logger.error(`Error at CMS updateEmailTemplatesFromConfig${err}`);
        return sendError(res, err);
    }
}

async function getAllEmailTemplates(req, res) {
    try {
        const totalDocuments = await EmailTemplates.countDocuments();
        const sort = {
            created_at: -1
        };
        const conditions = {};

        // Set limit and skip values based on request parameters or default to 10 and 0 respectively
        const limit = (req.body.limit !== undefined) ? req.body.limit : 10;
        const skip = (req.body.skip !== undefined) ? req.body.skip : 0;

        // Count documents matching the conditions
        const filterDocuments = await EmailTemplates.countDocuments(conditions);

        const query = [
            { $match: conditions }
        ];

        // Add sorting, skipping, and limiting stages to the aggregation pipeline
        query.push({ '$sort': sort });
        query.push({ '$skip': skip });
        query.push({ '$limit': limit });

        // Execute the aggregation pipeline
        const totalEmailTemplates = await EmailTemplates.aggregate(query);

        // Prepare response object
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: totalEmailTemplates
        };

        // Respond with success and the fetched data
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        // Log any errors that occur
        logger.error(`Error at CMS Controller getAllEmailTemplates${err}`);

        // Respond with server error message
        return sendError(res, err);
    }
}

async function getEmailTemplateById(req, res) {
    try {
        // Extract email template ID from request parameters
        const emailTemplateId = req.params.id;
        // Find email template data by ID
        const emailTemplateData = await EmailTemplates.findById(emailTemplateId);
        // If no email template found, return a resource not found error
        if (emailTemplateData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! EmailTemplate Not Found.');
        }
        // Return successful response with email template data
        return ReS(res, constants.success_code, 'Request fetched Successfully', emailTemplateData);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllEmailTemplateById${err}`);
        return sendError(res, err);
    }
}


async function addEmailTemplate(req, res) {
    try {
        // Extract email template data from request body
        let emailTemplateData = req.body;
        // Generate slug from email template title
        const slug = createSlug(emailTemplateData.title);
        // Check if email template with the same slug already exists
        const emailTemplate = await EmailTemplates.findOne({ slug }).lean();
        // If email template with same slug found, return a conflict response
        if (emailTemplate) {
            return ReS(res, constants.bad_request_code, 'Oops! Email template already registered', {
                _id: emailTemplate._id, slug: emailTemplate.slug
            });
        }
        // Add generated slug to email template data
        emailTemplateData = { ...emailTemplateData, slug: slug };
        // Create a new email template
        await EmailTemplates.create(emailTemplateData);
        // Return successful response
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller addEmailTemplate${err}`);
        return sendError(res, err);
    }
}


module.exports = {
    updateEmailTemplatesFromConfig,
    getEmailTemplateById,
    getAllEmailTemplates,
    addEmailTemplate
};