const Joi = require('joi');

class CollectionValidation {
    createOrEditCollection(params) {
        const schema = Joi.object({
            title: Joi.string()
                .trim()
                .min(3)
                .max(100)
                .required()
                .messages({
                    'string.base': 'Please enter a valid collection name.',
                    'string.empty': 'Collection name cannot be empty. Please provide a name.',
                    'string.min': 'Collection name must be at least {#limit} characters long.',
                    'string.max': 'Collection name cannot exceed {#limit} characters.',
                    'any.required': 'Collection name is required. Please enter a name.'
                })
        });
        return schema.validate(params);
    }

    addCollectionItems(params) {
        const schema = Joi.object({
            components: Joi.array().items(Joi.string()).min(1).unique().required()
        });
        return schema.validate(params);
    }
}

module.exports = new CollectionValidation();