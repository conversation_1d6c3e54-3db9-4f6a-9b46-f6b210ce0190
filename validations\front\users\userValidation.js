const Joi = require('joi');
const { socialSitePlatforms } = require('../../../config/user.constant');
const { userActions } = require('../../../config/component.constant');

class UserValidation {
    editProfile(params) {
        const schema = Joi.object({
            first_name: Joi.string().trim().required(),
            last_name: Joi.string().trim().required(),
            avatar: Joi.string().optional().allow(null, ''),
            biography: Joi.string().optional().allow(null, ''),
            social_links: Joi.array().items(
                Joi.object({
                    platform: Joi.string().valid(...Object.values(socialSitePlatforms)).required(),
                    username: Joi.string().required()
                })
            ).optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    createGitlabAccount(params) {
        const schema = Joi.object({
            username: Joi.string()
                .min(2)
                .max(255)
                .regex(/^(?!-)[a-zA-Z0-9._-]+(?<!\.(git|atom)|\.)$/)
                .invalid('admin', 'root', 'support', 'git', 'public', 'dashboard')
                .required()
        });
        return schema.validate(params);
    }

    setupCreatorProfile(params) {
        const schema = Joi.object({
            biography: Joi.string().optional().allow(null, ''),
            social_links: Joi.array().items(
                Joi.object({
                    platform: Joi.string().valid(...Object.values(socialSitePlatforms)).required(),
                    username: Joi.string().required()
                })
            ).optional(),
            country: Joi.object({
                id: Joi.number().integer().required(),
                iso3: Joi.string().length(3).required(),
                name: Joi.string().required()
            }).required(),
            technologies: Joi.array().items(Joi.string().trim()).optional(),
            website: Joi.string().uri().optional().allow(null, '')
        });
        return schema.validate(params);
    }

    sendOTPForSecurityAction(params) {
        const schema = Joi.object({
            email: Joi.string().trim().email().required(),
            action: Joi.string().valid(...Object.values(userActions)).required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updateUserEmail(params) {
        const schema = Joi.object({
            new_email: Joi.string().trim().email().required(),
            otp: Joi.string().min(6).max(6).required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new UserValidation();