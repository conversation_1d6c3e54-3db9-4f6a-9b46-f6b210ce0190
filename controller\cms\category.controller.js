// Constants declaration
const constants = require('../../config/constants');

// Service declaration
const logger = require('../../config/logger');
const { ReS, generateSlug, escapeRegex, sendError } = require('../../services/general.helper');

// Models declaration
const Category = require('../../models/category.model').Category;
const Components = require('../../models/component.model').Components;

const { overridingCategorySlug } = require("../../config/component.constant");

async function createCategory(req, res) {
    try {
        const { category_name, image_url, short_description, parent_id, category_type } = req.body;

        let categorySlug = generateSlug(category_name);

        if (parent_id) {
            const parentCategory = await Category.findOne({
                _id: parent_id,
                is_deleted: false
            }, 'category_slug').lean();

            if (!parentCategory) {
                return ReS(res, constants.bad_request_code, 'Parent category not found or deleted');
            }

            if (categorySlug == overridingCategorySlug.OTHER) {
                categorySlug = `${categorySlug}-${parentCategory?.category_slug}`;
            }
        }
        await Category.create({
            category_name: category_name,
            image_url: image_url,
            short_description: short_description,
            category_slug: categorySlug,
            created_by: req.session._id,
            parent_id: parent_id || null,
            category_type: category_type
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createCategory${err}`);
        return sendError(res, err);
    }
}

async function updateCategory(req, res) {
    try {
        const { category_name, image_url, is_active, short_description, parent_id, category_type } = req.body;

        const categoryData = await Category.findOne({
            _id: req.params.id
        });

        if (categoryData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Category Not Found.');
        }

        let categorySlug = generateSlug(category_name);

        if (parent_id) {
            if (parent_id === categoryData._id) {
                return ReS(res, constants.bad_request_code, 'Category cannot be its own parent');
            }

            const parentCategory = await Category.findOne({
                _id: parent_id,
                is_deleted: false
            }, "category_slug").lean();

            if (!parentCategory) {
                return ReS(res, constants.bad_request_code, 'Parent category not found');
            }

            if (categorySlug == overridingCategorySlug.OTHER) {
                categorySlug = `${categorySlug}-${parentCategory?.category_slug}`;
            }
        }

        const postData = {
            category_name: category_name,
            image_url: image_url,
            category_slug: categorySlug,
            short_description: short_description,
            updated_by: req.session._id,
            parent_id: parent_id !== undefined ? parent_id : categoryData.parent_id,
            category_type: category_type
        };

        if (is_active != undefined) {
            postData['is_active'] = is_active;
        }

        await Category.updateOne({
            _id: req.params.id
        }, {
            '$set': postData
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateCategory${err}`);
        return sendError(res, err);
    }
}

async function getAllCategory(req, res) {
    try {
        const totalDocuments = await Category.countDocuments();
        const { is_active, category_name } = req.body;
        // Set default sort
        const sort = {
            'created_at': -1
        };
        // Set default conditions
        const conditions = {
            is_deleted: false
        };
        if (is_active != undefined) {
            conditions['is_active'] = is_active;
        }
        if (category_name) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(category_name);
            conditions['$or'] = [{
                'category_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        const filterDocuments = await Category.countDocuments(conditions);
        const query = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'categories',
                let: {
                    parent_id: '$parent_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$parent_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'category_name': 1,
                        'category_slug': 1,
                        'image_url': 1
                    }
                }],
                as: 'parent_id'
            }
        }, {
            $project: {
                category_name: 1,
                category_slug: 1,
                short_description: 1,
                image_url: 1,
                created_at: 1,
                category_type: 1,
                parent_id: {
                    $arrayElemAt: ['$parent_id', 0]
                },
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });
        const categoryList = await Category.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: categoryList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllCategory${err}`);
        return sendError(res, err);
    }
}

async function getCategoryDetails(req, res) {
    try {

        const categoryData = await Category.findOne({
            _id: req.params.id,
            is_deleted: false
        }).populate({
            path: 'parent_id',
            select: 'category_name category_slug image_url short_description',
            match: { is_deleted: false }
        });

        return ReS(res, constants.success_code, 'Data Fetched', categoryData);
    } catch (err) {
        logger.error(`Error at CMS Controller getCategoryDetails${err}`);
        return sendError(res, err);
    }
}

async function getAllCategorySortList(req, res) {
    try {
        const { is_active, category_name, type } = req.query;
        // Set default sort
        const sort = {
            'created_at': -1
        };
        // Set default conditions
        const conditions = {
            is_deleted: false
        };
        if (type === 'parent') {
            conditions.parent_id = null;
        }
        if (is_active != undefined) {
            conditions['is_active'] = is_active;
        }
        if (category_name) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(category_name);
            conditions['$or'] = [{
                'category_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const categoryList = await Category.find(conditions).sort(sort).lean();
        return ReS(res, constants.success_code, 'Data Fetched', categoryList);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllCategorySortList${err}`);
        return sendError(res, err);
    }
}

async function deleteCategory(req, res) {
    try {

        const categoryData = await Category.findOne({
            _id: req.params.id
        }).lean();

        if (categoryData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Category Not Found.');
        }

        const componentList = await Components.find({
            category_id: req.params.id
        }).lean();

        if (componentList && componentList.length > 0) {
            return ReS(res, constants.bad_request_code, 'Oops! Category deletion is not permitted as this category is associated with certain components.');
        }

        await Category.updateOne({
            _id: req.params.id
        }, {
            $set: {
                is_deleted: true
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller deleteCategory${err}`);
        return sendError(res, err);
    }
}

async function updateCategoryStatus(req, res) {
    try {
        const { is_active } = req.body;

        const categoryData = await Category.findOne({
            _id: req.params.id
        });

        if (categoryData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Category Not Found.');
        }

        await Category.updateOne({
            _id: req.params.id
        }, {
            '$set': {
                is_active: is_active
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateCategoryStatus${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createCategory,
    updateCategory,
    getAllCategory,
    getCategoryDetails,
    getAllCategorySortList,
    deleteCategory,
    updateCategoryStatus
};