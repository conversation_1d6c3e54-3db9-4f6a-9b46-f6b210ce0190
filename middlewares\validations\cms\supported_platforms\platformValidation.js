const { createPlatform, updatePlatform, updatePlatformStatus } = require('../../../../validations/cms/supported_platforms/platformValidation');
class ModifierValidationMiddleware {
    createPlatformValidation(req, res, next) {
        const { value, error } = createPlatform(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updatePlatformValidation(req, res, next) {
        const { value, error } = updatePlatform(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updatePlatformStatusValidation(req, res, next) {
        const { value, error } = updatePlatformStatus(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new ModifierValidationMiddleware();