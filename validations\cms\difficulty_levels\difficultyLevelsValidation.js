const Joi = require('joi');
class DifficultyLevelValidation {
    createDifficultyLevel(params) {
        const schema = Joi.object({
            title: Joi.string().required(),
            is_active: Joi.boolean().optional(),
            description: Joi.string().optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    updateDifficultyLevel(params) {
        const schema = Joi.object({
            title: Joi.string().optional(),
            is_active: Joi.boolean().optional(),
            description: Joi.string().optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new DifficultyLevelValidation();