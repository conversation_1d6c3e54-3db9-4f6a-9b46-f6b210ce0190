const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const logger = require('../config/logger');

const Users = require('./users.model').Users;

const creatorFollowSchema = new Schema({
    creator_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    },
    follower_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

// Post-save hook to update follower counts
creatorFollowSchema.post('save', async function () {
    try {
        // Update creator's follower count
        await Users.updateOne(
            { _id: this.creator_id },
            { $inc: { followers_count: 1 } }
        );

        // Update follower's following count
        await Users.updateOne(
            { _id: this.follower_id },
            { $inc: { following_count: 1 } }
        );
    } catch (error) {
        logger.error(`Error in creatorFollowSchema post-save hook: ${error}`);
    }
});

// Post-delete hook to update follower counts
creatorFollowSchema.post('findOneAndDelete', async function (doc) {
    try {
        if (doc) {
            // Update creator's follower count
            await Users.updateOne(
                { _id: doc.creator_id },
                { $inc: { followers_count: -1 } }
            );

            // Update follower's following count
            await Users.updateOne(
                { _id: doc.follower_id },
                { $inc: { following_count: -1 } }
            );
        }
    } catch (error) {
        logger.error(`Error in creatorFollowSchema post-delete hook: ${error}`);
    }
});

// Compound index for efficient querying and to ensure uniqueness
creatorFollowSchema.index({ creator_id: 1, follower_id: 1 }, { unique: true });

// Single-field indexes for querying followers and following lists
creatorFollowSchema.index({ creator_id: 1 });
creatorFollowSchema.index({ follower_id: 1 });

const CreatorFollow = mongoose.model('creator_follows', creatorFollowSchema);

module.exports = {
    CreatorFollow
};
