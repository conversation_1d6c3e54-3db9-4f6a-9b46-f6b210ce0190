const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const GitlabUserTokensSchema = new Schema({
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'gitlab_users'
    },
    personal_access_tokens: {
        type: String
    },
    personal_access_token_name: {
        type: String
    },
    token_expires_at: {
        type: Date
    },
    scopes: {
        type: Array
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const GitlabUserTokens = mongoose.model('gitlab_user_tokens', GitlabUserTokensSchema);

module.exports = {
    GitlabUserTokens
};