const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const TemplatesZip = new Schema({
    title: {
        type: String,
        required: true
    },
    zip_url: {
        type: String,
        required: true,
        unique: true
    },
    description: {
        type: String,
        required: true
    },
    is_active: {
        type: Boolean,
        default: true
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const Templates = mongoose.model('templates_zip', TemplatesZip);

module.exports = {
    Templates
};