const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { pointActivity } = require('../config/component.constant');

const UserMpnPointLogsSchema = new Schema({
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    },
    points: {
        type: Number,
        required: true,
        default: 0
    },
    activity: {
        type: String,
        enum: Object.values(pointActivity),
        required: true
    },
    description: {
        type: String
    },
    meta_data: {
        type: Object
    },
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const UserMpnPointLogs = mongoose.model('user_mpn_point_logs', UserMpnPointLogsSchema);

module.exports = {
    UserMpnPointLogs
};