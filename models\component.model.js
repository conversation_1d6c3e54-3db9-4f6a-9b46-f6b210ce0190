const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { componentOrientation, componentType, componentState, publishReference, thumbnailStatus, thumbnailSource } = require('../config/component.constant');
const logger = require('../config/logger');

const ComponentUnlockHistory = require('./component_unlock_history.model').ComponentUnlockHistory;

const { addTagsToCollection } = require('./../services/tags.service');

const componentSchema = new Schema({
    title: {
        type: String
    },
    slug: {
        type: String
    },
    short_description: {
        type: String
    },
    long_description: {
        type: String
    },
    image_url: {
        type: String
    },
    gif_url: {
        type: String
    },
    gif_status: {
        type: String,
        default: thumbnailStatus.PENDING,
        enum: Object.values(thumbnailStatus)
    },
    thumbnail_url: {
        type: String
    },
    video_url: {
        type: String
    },
    category_id: {
        type: mongoose.Types.ObjectId,
        ref: 'category'
    },
    platform_data: [{
        platform_id: [{
            type: mongoose.Types.ObjectId,
            ref: 'supported_platforms'
        }],
        repository_id: {
            type: mongoose.Types.ObjectId,
            ref: 'gitlab_repository'
        }
    }],
    created_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    updated_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    created_by_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    updated_by_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    views: {
        type: Number,
        default: 0
    },
    likes: {
        type: Number,
        default: 0
    },
    bookmarks: {
        type: Number,
        default: 0
    },
    downloads: {
        type: Number,
        default: 0
    },
    assets: [{
        file_url: {
            type: String
        },
        file_extension: {
            type: String
        },
        file_name: {
            type: String
        },
        file_original_name: {
            type: String
        },
        file_mime_type: {
            type: String
        },
        file_notes: {
            type: String
        }
    }],
    difficulty_level: {
        type: String
    },
    identification_tag: [{
        type: String
    }],
    is_paid: {
        type: Boolean,
        default: false
    },
    mpn_parity: {
        type: Number,
        default: 0
    },
    purchase_price: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    item_price: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    buyer_fee: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    deleted_at: {
        type: Date
    },
    orientation: {
        type: String,
        enum: Object.values(componentOrientation)
    },
    component_type: {
        type: String,
        default: componentType.MANUAL,
        enum: Object.values(componentType)
    },
    component_state: {
        type: String,
        default: componentState.PLACEHOLDER,
        enum: Object.values(componentState)
    },
    collection_id: {
        type: mongoose.Types.ObjectId,
        ref: 'collections'
    },
    component_draft_id: {
        type: mongoose.Types.ObjectId,
        ref: 'draft_components'
    },
    state_change_history: [{
        old: {
            type: String // Previous state
        },
        new: {
            type: String // New state
        },
        date: {
            type: Date // Date of the change
        },
        changed_by_user: {
            type: mongoose.Types.ObjectId, // User who made the change
            ref: 'users'
        },
        changed_by_admin: {
            type: mongoose.Types.ObjectId, // User who made the change
            ref: 'admins'
        }
    }],
    elements_data: {
        type: mongoose.Schema.Types.Mixed
    },
    linked_output: {
        type: String
    },
    design_url: {
        type: String
    },
    languages: [{
        type: mongoose.Types.ObjectId,
        ref: 'supported_platforms'
    }],
    technologies: [{
        type: mongoose.Types.ObjectId,
        ref: 'supported_platforms'
    }],
    detected_technologies: [{
        type: mongoose.Types.ObjectId,
        ref: 'supported_platforms'
    }],
    location: {
        id: {
            type: Number
        },
        iso3: {
            type: String
        }
    },
    currency: {
        type: String
    },
    is_featured: {
        type: Boolean,
        default: false
    },
    variant_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    source_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    publish_reference: {
        type: String,
        enum: Object.values(publishReference)
    },
    live_preview: {
        default: false,
        type: Boolean
    },
    element_container_meta: {
        width: {
            type: String,
            default: '0'
        },
        height: {
            type: String,
            default: '0'
        },
        unit: {
            type: String,
            default: 'px'
        },
        is_center: {
            type: Boolean,
            default: true
        },
        background_color: {
            type: String,
            default: '#212121'
        }
    },
    published_count: {
        type: Number,
        default: 0
    },
    last_published_at: {
        type: Date
    },
    license_id: {
        type: mongoose.Types.ObjectId,
        ref: 'platform_licenses'
    },
    public_repository_id: {
        type: mongoose.Types.ObjectId,
        ref: 'gitlab_repository'
    },
    thumbnail_source: {
        type: String,
        default: thumbnailSource.AUTO
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});


// Define a post-save hook for the componentSchema
componentSchema.post('findOneAndUpdate', async function () {
    try {
        // Capture the reference to the current context
        const currentContext = this;

        // Check if the current context and $set object exist in the update
        if (currentContext && currentContext.getUpdate().$set) {

            // Check if identification_tag is being updated & add tag to collection
            if (currentContext.getUpdate().$set?.identification_tag?.length && currentContext.getUpdate().$set?.component_type) {
                await addTagsToCollection(currentContext.getUpdate().$set.identification_tag, currentContext.getUpdate().$set.component_type);
            }
        }
    } catch (error) {
        // Log any errors that occur during the operation
        logger.error(`Error occurred in the post-save hook of componentSchema: ${error}`);
    }
});

// Define a post-updateOne hook for the componentSchema
componentSchema.post('updateOne', async function () {
    try {
        // Capture the reference to the current context
        const currentContext = this;

        // Check if the current context and $set object exist in the update
        if (currentContext && currentContext.getUpdate().$set) {

            // Initialize an empty object to hold history changes
            const historyObj = {};

            // Check if title is being updated and update history accordingly
            if (currentContext.getUpdate().$set.title) {
                historyObj['component_name'] = currentContext.getUpdate().$set.title;
            }

            // Check if slug is being updated and update history accordingly
            if (currentContext.getUpdate().$set.slug) {
                historyObj['component_slug'] = currentContext.getUpdate().$set.slug;
            }

            if (currentContext.getUpdate().$set?.identification_tag?.length && currentContext.getUpdate().$set?.component_type) {
                await addTagsToCollection(currentContext.getUpdate().$set?.identification_tag, currentContext.getUpdate().$set?.component_type);
            }

            // Update the corresponding component's unlock history with the changes
            await ComponentUnlockHistory.updateMany(
                { component_id: currentContext.getQuery()._id },
                { $set: historyObj }
            );
        }
    } catch (error) {
        // Log any errors that occur during the operation
        logger.error(`Error occurred in the post-updateOne hook of componentSchema:${error}`);
    }
});

// Pre-hook for updateOne to log state changes
componentSchema.pre('updateOne', async function (next) {
    try {
        // Get the current query filter and update object
        const update = this.getUpdate();

        // Exit early if `component_state` is not being modified
        if (!update.$set?.component_state) return next();

        // Fetch the current document's `component_state`
        const component = await this.model.findOne(this.getQuery()).select('component_state').lean();

        // Exit if no component found or state is unchanged
        if (!component || component.component_state === update.$set.component_state) return next();

        const oldState = component.component_state;
        const newState = update.$set.component_state;

        // Prepare the log object for the state change
        const log = {
            old: oldState,
            new: newState,
            date: new Date()
        };

        // Add `changed_by_admin` and `changed_by_user` if they exist in the update object
        if (update.$set.updated_by) {
            log['changed_by_admin'] = update.$set.updated_by;
        }
        if (update.$set.updated_by_user) {
            log['changed_by_user'] = update.$set.updated_by_user;
        }

        if (update.$push) {
            update.$push.state_change_history = log;
        } else {
            update.$push = { state_change_history: log };
        }

        next();
    } catch (error) {
        logger.error(`Error occurred in the pre-updateOne hook of componentSchema: ${error}`);
        next(error);
    }
});


const Components = mongoose.model('components', componentSchema);

module.exports = {
    Components
};