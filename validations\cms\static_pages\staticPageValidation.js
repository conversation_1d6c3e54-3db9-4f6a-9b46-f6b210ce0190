const Joi = require('joi');

class StaticPageValidation {

    createStaticPage(params) {
        const schema = Joi.object({
            title: Joi.string().trim().required(),
            description: Joi.string().trim().required()
        });
        return schema.validate(params);
    }

    updateStaticPage(params) {
        const schema = Joi.object({
            title: Joi.string().trim().optional(),
            description: Joi.string().trim().optional()
        });
        return schema.validate(params);
    }

    updateStaticPageStatus(params) {
        const schema = Joi.object({
            is_active: Joi.boolean().required()
        });
        return schema.validate(params);
    }
}

module.exports = new StaticPageValidation();
