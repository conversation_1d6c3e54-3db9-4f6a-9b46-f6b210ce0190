// Service declaration
const constants = require('../../config/constants');
const { ReS, generateSlug } = require('../../services/general.helper');
const logger = require('../../config/logger');

const mongoose = require('mongoose');

// Models declaration
const Collection = require('../../models/collection.model').Collection;

async function createCollection(req, res) {
    try {
        const { title } = req.body;
        const adminId = req.session._id;

        // Generate a slug from the title
        const slug = generateSlug(title);

        // Check if a collection with the same slug already exists for the user
        const existingCollection = await Collection.findOne({ slug, created_by_admin: adminId });
        if (existingCollection) {
            return ReS(res, constants.conflict_code, 'A collection with this title already exists.');
        }

        // Create the collection
        const newCollection = await Collection.create({
            title,
            slug,
            created_by_admin: adminId
        });

        return ReS(res, constants.success_code, 'Collection created successfully.', { collection: newCollection });
    } catch (err) {
        logger.error(`Error at CMS Controller createCollection${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function updateCollection(req, res) {
    try {
        const { title } = req.body;
        const collectionId = req.params.id;
        const adminId = req.session._id;

        // Generate a new slug based on the updated title
        const newSlug = generateSlug(title);

        // Check if the collection exists and belongs to the user
        const collection = await Collection.findOne({ _id: collectionId, created_by_admin: adminId }).lean();
        if (!collection) {
            return ReS(res, constants.resource_not_found, 'Collection not found.');
        }

        // Check if any other collection exists with the same slug for the user
        const existingCollection = await Collection.findOne({ slug: newSlug, created_by_admin: adminId, _id: { $ne: collectionId } }).lean();
        if (existingCollection) {
            return ReS(res, constants.conflict_code, 'A collection with this title already exists.');
        }

        // Update the collection's title and slug using updateOne
        await Collection.updateOne(
            { _id: collectionId },
            {
                $set: {
                    title,
                    slug: newSlug
                }
            }
        );

        return ReS(res, constants.success_code, 'Collection updated successfully.');
    } catch (err) {
        logger.error(`Error at CMS Controller updateCollection${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function sortListCollectionAlphabetically(req, res) {
    try {
        const adminId = req.session._id;

        // Fetch all collections sorted alphabetically by title
        const collections = await Collection.aggregate([
            {
                $match: {
                    is_active: true,
                    created_by_admin: new mongoose.Types.ObjectId(adminId)
                }
            }, // Filter only active collections
            { $sort: { title: 1 } } // Sort collections alphabetically by title
        ]);

        return ReS(res, constants.success_code, 'Collections fetched successfully.', collections);

    } catch (err) {
        logger.error(`Error at CMS Controller sortListCollectionAlphabetically${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}


module.exports = {
    createCollection,
    updateCollection,
    sortListCollectionAlphabetically
};