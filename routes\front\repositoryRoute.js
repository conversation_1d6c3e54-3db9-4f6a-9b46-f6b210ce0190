const express = require('express');
const router = express.Router();
const { getRepositoryContent, getRepositoryCommitInfo, getRepositoryFileContent, downloadRepositoryContent, getRepositoryNameAvailability, getRepositoryBranches, createRepositoryCommit, downloadRepositoryFileContent, fetchRepositoryDetails, getRepositoryCommitDiff, getRepositoryContentRecursively, uploadMultipleFilesToRepository, createBranch, createMergeRequest, listMergeRequest, approveMergeRequest, addCommentToMergeRequest, getRepositoryMembers, getMergeRequestDetails, getMergeRequestChanges, getMergeRequestCommits, getMergeRequestDifferences, getBranchCommitsDifferences, getBranchFilesDifferences, closeMergeRequest, getMergeRequestActivity, getMergeRequestDifferencesList, getRepositoryStatistics } = require('../../controller/front/repository.controller');

const { addCommentToMergeRequestValidation } = require('./../../middlewares/validations/front/merge_request/mergeRequestValidation');
const { createCommitValidation, createCommitWithFilesValidation, createBranchValidation } = require('../../middlewares/validations/front/repository/repositoryValidation');

const { checkIsAccessible } = require('../../middlewares/validateCodeSpace');

router.get('/get-content/:id', checkIsAccessible, getRepositoryContent);
router.get('/get-commits/:id', checkIsAccessible, getRepositoryCommitInfo);
router.get('/get-file-content/:id/:path/raw', checkIsAccessible, getRepositoryFileContent);
router.get('/download/:id', checkIsAccessible, downloadRepositoryContent);
router.get('/check-name-availability', checkIsAccessible, getRepositoryNameAvailability);
router.post('/submit-commit/:id', checkIsAccessible, createCommitValidation, createRepositoryCommit);
router.get('/get-details/:id', checkIsAccessible, fetchRepositoryDetails);
router.get('/download/file/:id', checkIsAccessible, downloadRepositoryFileContent);
router.get('/:id/commit/:sha/diff', checkIsAccessible, getRepositoryCommitDiff);
router.get('/get-content/recursive/:id', checkIsAccessible, getRepositoryContentRecursively);
router.post('/upload-files/multiple/:id', checkIsAccessible, createCommitWithFilesValidation, uploadMultipleFilesToRepository);
router.get('/get-members/:id', checkIsAccessible, getRepositoryMembers);
router.get('/get-branches/:id', checkIsAccessible, getRepositoryBranches);
router.post('/create-branch/:id', createBranchValidation, createBranch);
router.post('/:id/merge-request/create', createMergeRequest);
router.get('/:id/merge-request/list', listMergeRequest);
router.get('/:id/merge-request/:merge_request_id', getMergeRequestDetails);
router.post('/:id/merge-request/:merge_request_id/approve', approveMergeRequest);
router.post('/:id/merge-request/:merge_request_id/comments', addCommentToMergeRequestValidation, addCommentToMergeRequest);
router.get('/:id/merge-request/:merge_request_id/changes', getMergeRequestChanges);
router.get('/:id/merge-request/:merge_request_id/commits', getMergeRequestCommits);
router.get('/:id/merge-request/:merge_request_id/diffs', getMergeRequestDifferences);
router.get('/:id/get-branches/commits/diffs', getBranchCommitsDifferences);
router.get('/:id/get-branches/files/diffs', getBranchFilesDifferences);
router.put('/:id/merge-request/:merge_request_id/close', closeMergeRequest);
router.get('/:id/merge-request/:merge_request_id/activity', getMergeRequestActivity);
router.get('/:id/merge-request/:merge_request_id/diffs/list', getMergeRequestDifferencesList);
router.get('/get-statistics/:id', getRepositoryStatistics);

module.exports = router;