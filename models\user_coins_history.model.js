const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { coinTypes } = require('./../config/component.constant');

const UserCoinsHistorySchema = new Schema({
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    points: {
        type: Number,
        required: true,
        default: 0
    },
    pending_points: {
        type: Number,
        required: true,
        default: 0
    },
    type: {
        type: String,
        enum: Object.values(coinTypes)
    },
    transaction_id: {
        type: String
    },
    meta_data: {
        type: Object
    },
    expired_on: {
        type: Date
    },
    is_expired: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const UserCoinsHistory = mongoose.model('user_coins_history', UserCoinsHistorySchema);

module.exports = {
    UserCoinsHistory
};