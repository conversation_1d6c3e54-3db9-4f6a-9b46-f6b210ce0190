// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');
const mongoose = require('mongoose');

// Service declaration
const { ReS } = require('../../services/general.helper');
const { sendNotificationCountToUser, isUserConnected } = require('../../services/socket.service');

// Models declaration
const UserNotifications = require('../../models/user_notification.model').UserNotifications;


async function fetchUnReadNotifications(req, res) {
    try {

        const totalDocuments = await UserNotifications.countDocuments();

        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;

        // Default sort
        const sort = { created_at: -1 };

        // Default filters
        const conditions = {
            user_id: new mongoose.Types.ObjectId(req.session._id),
            ...(req.body.is_read !== undefined && { is_read: req.body.is_read })
        };

        // Count filtered documents after filter apply
        const filterDocuments = await UserNotifications.countDocuments(conditions);

        const pipelines = [{
            $match: conditions
        }, {
            $sort: sort
        }, {
            $skip: skip
        }, {
            $limit: limit
        }, {
            $lookup: {
                from: 'notifications',
                let: {
                    notification_id: '$notification_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$notification_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'type': 1,
                        'message': 1,
                        'title': 1,
                        'entity': 1
                    }
                }],
                as: 'notification'
            }
        }, {
            $project: {
                user_id: 1,
                is_read: 1,
                notification: {
                    $arrayElemAt: ['$notification', 0]
                },
                created_at: 1
            }
        }];

        const notificationList = await UserNotifications.aggregate(pipelines);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: notificationList
        };

        // Return success message
        return ReS(res, constants.success_code, 'Notifications fetched successfully', responseObj);
    } catch (err) {
        // Log any errors that occur and return a server error message
        logger.error(`Error at Front Controller fetchUnReadNotifications ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function notificationMarkAsRead(req, res) {
    try {
        // Attempt to find the notification by ID and user ID
        const notification = await UserNotifications.findOne({
            _id: req.params.id,
            user_id: req.session._id
        }, 'is_read').lean();

        // If notification is not found, return a 404 error response
        if (!notification) {
            return ReS(res, constants.resource_not_found, 'This notification does not exist.');
        }

        // If the notification is already marked as read, return a 400 error response
        if (notification.is_read) {
            return ReS(res, constants.bad_request_code, 'Notification has already been marked as read.');
        }

        // Update the notification's `is_read` field to true
        await UserNotifications.updateOne({
            _id: req.params.id,
            user_id: req.session._id
        }, {
            $set: {
                is_read: true
            }
        });

        // Get updated unread count for this user
        const unreadCount = await UserNotifications.countDocuments({
            user_id: req.session._id,
            is_read: false
        });

        // Send real-time notification count update if user is connected
        if (isUserConnected(req.session._id)) {
            sendNotificationCountToUser(req.session._id, unreadCount);
        }

        // Return success message indicating the notification was marked as read
        return ReS(res, constants.success_code, 'Notification marked as read.', { unread_count: unreadCount });
    } catch (err) {
        // Log the error and return a 500 server error response
        logger.error(`Error at Front Controller notificationMarkAsRead: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function fetchNotificationUnReadCount(req, res) {
    try {

        // Default filters
        const filter = {
            user_id: new mongoose.Types.ObjectId(req.session._id),
            is_read: false
        };

        // Count filtered documents after filter apply
        const notificationCount = await UserNotifications.countDocuments(filter);

        // Return success message
        return ReS(res, constants.success_code, 'Notifications count fetched successfully', notificationCount);
    } catch (err) {
        // Log any errors that occur and return a server error message
        logger.error(`Error at Front Controller fetchNotificationUnReadCount ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function notificationMarkAllAsRead(req, res) {
    try {
        // Attempt to find the all unread notification by user ID
        const notifications = await UserNotifications.countDocuments({
            is_read: false,
            user_id: req.session._id
        }).lean();

        // // If notifications is not found, return a 404 error response
        if (notifications == 0) {
            return ReS(res, constants.bad_request_code, 'Oops! No unread notifications found');
        }

        // Update the notification's `is_read` field to true
        await UserNotifications.updateMany({
            is_read: false,
            user_id: req.session._id
        }, {
            $set: {
                is_read: true
            }
        });

        // Send real-time notification count update if user is connected
        if (isUserConnected(req.session._id)) {
            // After marking all as read, the count is 0
            sendNotificationCountToUser(req.session._id, 0);
        }

        // Return success message indicating the notification was marked as read
        return ReS(res, constants.success_code, 'All Notification marked as read.', { unread_count: 0 });
    } catch (err) {
        // Log the error and return a 500 server error response
        logger.error(`Error at Front Controller notificationMarkAllAsRead: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}


module.exports = {
    fetchUnReadNotifications,
    notificationMarkAsRead,
    fetchNotificationUnReadCount,
    notificationMarkAllAsRead
};