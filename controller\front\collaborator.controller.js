// Constants declaration
const constants = require('../../config/constants');
const { gitlabAccessLevel } = require('../../config/gitlab.constant');
const logger = require('../../config/logger');
const { v4: uuidv4 } = require('uuid');

// Service declaration
const { ReS } = require('../../services/general.helper');
const { addMemberToProject, updateCollaboratorAccess, removeCollaboratorFromGitlab } = require('../../services/gitlab.helper');
const { sendRepoInviteVerificationEmail } = require('../../services/send_email.service');
const { invitationStatus } = require('../../config/gitlab.constant');

// Models declaration
const Users = require('../../models/users.model').Users;
const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;
const GitlabUsers = require('../../models/gitlab_users.model').GitlabUsers;
const Collaborator = require('../../models/collaborator.model').Collaborator;
const RepoInvitation = require('../../models/repo_invitation.model').RepoInvitation;

async function addCollaboratorInRepo(req, res) {
    try {
        const { collaborator_user_id, repo_id, access_level } = req.body;
        const currentUserId = req.session._id;

        const repoDetails = await GitlabRepository.findOne({ _id: repo_id, user_id: currentUserId }, 'project_id').lean();

        // If repo details is not found, return an error response
        if (!repoDetails) {
            return ReS(res, constants.resource_not_found, 'Repo not found.');
        }

        const collaboratorUserData = await Users.findOne({ _id: collaborator_user_id }, 'gitlab_user_exists').lean();

        // If user data is not found, return an error response
        if (!collaboratorUserData) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }
        if (collaboratorUserData && collaboratorUserData.gitlab_user_exists == false) {
            return ReS(res, constants.conflict_code, 'Oops! GitLab user not exists');
        }

        const gitlabUserData = await GitlabUsers.findOne({ user_id: collaboratorUserData._id }, 'gitlab_user_id').lean();

        // If user data is not found, return an error response
        if (!gitlabUserData) {
            return ReS(res, constants.resource_not_found, 'Gitlab User not found.');
        }

        const checkCollaborator = await Collaborator.findOne({ user_id: collaboratorUserData._id, repo_id: repo_id });

        if (checkCollaborator) {
            return ReS(res, constants.accepted_code, 'Already added in a repo.');
        }
        const addCollaborator = await addMemberToProject(repoDetails.project_id, gitlabUserData.gitlab_user_id, gitlabAccessLevel[access_level]);
        if (addCollaborator) {
            await Collaborator.create({
                user_id: collaboratorUserData._id,
                gitlab_user_id: gitlabUserData.gitlab_user_id,
                gitlab_user_ref: gitlabUserData._id,
                repo_id: repo_id,
                access_level: gitlabAccessLevel[access_level]
            });
        }

        return ReS(res, constants.success_code, 'User added successfully.', { result: addCollaborator });
    } catch (err) {
        logger.error(`Error at Front Controller collaborator${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function updateAccess(req, res) {
    try {
        const { user_id, repo_id, access_level } = req.body;
        const currentUserId = req.session._id;

        const repoDetails = await GitlabRepository.findOne({ _id: repo_id, user_id: currentUserId }, 'project_id').lean();

        // If repo details is not found, return an error response
        if (!repoDetails) {
            return ReS(res, constants.resource_not_found, 'Repo not found.');
        }

        const collaboratorUserData = await Users.findOne({ _id: user_id }, 'gitlab_user_exists').lean();
        // If user data is not found, return an error response
        if (!collaboratorUserData) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }
        if (collaboratorUserData && collaboratorUserData.gitlab_user_exists == false) {
            return ReS(res, constants.conflict_code, 'Oops! GitLab user not exists');
        }

        const gitlabUserData = await GitlabUsers.findOne({ user_id: collaboratorUserData._id }, 'gitlab_user_id').lean();

        // If user data is not found, return an error response
        if (!gitlabUserData) {
            return ReS(res, constants.resource_not_found, 'Gitlab User not found.');
        }

        const checkCollaborator = await Collaborator.findOne({ user_id: collaboratorUserData._id, repo_id: repo_id });

        // If user is not added, return an error response
        if (!checkCollaborator) {
            return ReS(res, constants.resource_not_found, 'User not found as a collaborator.');
        }

        const updateCollaborator = await updateCollaboratorAccess(repoDetails.project_id, gitlabUserData.gitlab_user_id, gitlabAccessLevel[access_level]);

        if (updateCollaborator) {
            await Collaborator.updateOne({
                user_id: collaboratorUserData._id,
                repo_id: repo_id
            }, {
                $set: {
                    access_level: gitlabAccessLevel[access_level]
                }
            });
        }

        return ReS(res, constants.success_code, 'Update access level successfully.', { result: updateCollaborator });
    } catch (err) {
        logger.error(`Error at Front Controller collaborator${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function removeAccess(req, res) {
    try {
        const { user_id, repo_id } = req.body;
        const currentUserId = req.session._id;
        const repoDetails = await GitlabRepository.findOne({ _id: repo_id, user_id: currentUserId }, 'project_id').lean();

        // If repo details is not found, return an error response
        if (!repoDetails) {
            return ReS(res, constants.resource_not_found, 'Repo not found.');
        }

        const collaboratorUserData = await Users.findOne({ _id: user_id }, 'gitlab_user_exists').lean();

        // If user data is not found, return an error response
        if (!collaboratorUserData) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }

        if (collaboratorUserData && collaboratorUserData.gitlab_user_exists == false) {
            return ReS(res, constants.conflict_code, 'Oops! GitLab user not exists');
        }

        const gitlabUserData = await GitlabUsers.findOne({ user_id: collaboratorUserData._id }, 'gitlab_user_id').lean();

        // If user data is not found, return an error response
        if (!gitlabUserData) {
            return ReS(res, constants.resource_not_found, 'Gitlab User not found.');
        }

        const checkCollaborator = await Collaborator.findOne({ user_id: collaboratorUserData._id, repo_id: repo_id });

        // If user is not added, return an error response
        if (!checkCollaborator) {
            return ReS(res, constants.resource_not_found, 'User not found as a collaborator.');
        }

        const removeCollaborator = await removeCollaboratorFromGitlab(repoDetails.project_id, gitlabUserData.gitlab_user_id);
        if (removeCollaborator) {
            await Collaborator.deleteOne({ user_id: collaboratorUserData._id, repo_id: repo_id });
        }

        return ReS(res, constants.success_code, 'Remove collaborator successfully.');
    } catch (err) {
        logger.error(`Error at Front Controller collaborator${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function sendInvite(req, res) {
    try {
        const { email, repo_id, access_level } = req.body;
        const currentUserId = req.session._id;

        const repoDetails = await GitlabRepository.findOne({ _id: repo_id, user_id: currentUserId }, 'project_id project_name').lean();

        // If repo details is not found, return an error response
        if (!repoDetails) {
            return ReS(res, constants.resource_not_found, 'Repo not found.');
        }

        const collaboratorDetails = await Collaborator.findOne({ repo_id: repo_id, user_id: currentUserId, access_level: gitlabAccessLevel.OWNER }).lean();

        // If repo details is not found, return an error response
        if (!collaboratorDetails) {
            return ReS(res, constants.resource_not_found, 'You do not have permission to invite to this repository');
        }

        const pendingInvitation = await RepoInvitation.findOne({
            repo_id: repo_id,
            status: invitationStatus.PENDING,
            invited_email: email
        }).lean();

        if (pendingInvitation) {
            return ReS(res, constants.bad_request_code, 'User already has a pending invitation.');
        }

        const findCollaboratorUser = await Users.findOne({ email: email }, 'gitlab_user_exists').lean();

        if (findCollaboratorUser) {
            const existingAccess = await Collaborator.findOne({ repo_id: repo_id, user_id: findCollaboratorUser._id }).lean();
            if (existingAccess) {
                return ReS(res, constants.conflict_code, 'User already has access to this repository');
            }
        }

        const gitlabUserData = await GitlabUsers.findOne({ user_id: currentUserId }, 'gitlab_user_id').lean();

        // If gitlab user data is not found, return an error response
        if (!gitlabUserData) {
            return ReS(res, constants.resource_not_found, 'Gitlab User not found.');
        }

        const inviteToken = await uuidv4();

        const url = `${process.env.SITE_URL}verify-collaboration-invite/${inviteToken}`;

        await sendRepoInviteVerificationEmail(currentUserId, repoDetails, email, url);

        const expiryHours = parseInt(process.env.INVITE_EXPIRY_HOURS) || 168;

        await RepoInvitation.create({
            token: inviteToken,
            invited_email: email,
            repo_id: repo_id,
            user_id: currentUserId,
            gitlab_user_id: gitlabUserData.gitlab_user_id,
            accessLevel: gitlabAccessLevel[access_level],
            expires_at: new Date(Date.now() + expiryHours * 60 * 60 * 1000),
            is_used: false,
            status: invitationStatus.PENDING
        });

        return ReS(res, constants.success_code, 'Invitation sent successfully.');
    } catch (err) {
        logger.error(`Error at Front Controller collaborator ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function acceptInvite(req, res) {
    try {
        const { invite_token } = req.body;

        const invitation = await RepoInvitation.findOne({ token: invite_token }).lean();

        if (!invitation) {
            return ReS(res, constants.resource_not_found, 'Invalid or expired invitation link');
        }

        if (invitation.is_used) {
            return ReS(res, constants.conflict_code, 'This invitation has already been used');
        }

        if (new Date() > invitation.expires_at) {
            return ReS(res, constants.conflict_code, 'This invitation has expired');
        }

        const repoDetails = await GitlabRepository.findOne({ _id: invitation.repo_id, user_id: invitation.user_id }, 'project_id project_name').lean();

        // If repo details is not found, return an error response
        if (!repoDetails) {
            return ReS(res, constants.resource_not_found, 'Repo not found.');
        }

        const findInvitedUser = await Users.findOne({ email: invitation?.invited_email }).lean();

        let flag;
        if (findInvitedUser) {
            const findGitLabUser = await GitlabUsers.findOne({ user_id: findInvitedUser._id }).lean();
            if (findGitLabUser) {
                const addCollaborator = await addMemberToProject(repoDetails.project_id, findGitLabUser.gitlab_user_id, gitlabAccessLevel[invitation.access_level]);
                if (addCollaborator) {
                    await Collaborator.create({
                        user_id: findInvitedUser._id,
                        gitlab_user_id: findGitLabUser.gitlab_user_id,
                        gitlab_user_ref: findGitLabUser._id,
                        repo_id: invitation.repo_id,
                        access_level: invitation.access_level
                    });
                }
                await RepoInvitation.updateOne({
                    token: invite_token
                }, {
                    $set: {
                        status: invitationStatus.ACCEPTED,
                        is_used: true
                    }
                });
                flag = 'INVITATION_SENT';
            } else {
                flag = 'NO_GITLAB_ACCOUNT_FOUND';
            }
        } else {
            flag = 'USER_NOT_FOUND';
        }

        return ReS(res, constants.success_code, 'Verify successfully.', { flag });
    } catch (err) {
        logger.error(`Error at Front Controller collaborator ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getInviteDetails(req, res) {
    try {
        const { invite_token } = req.params;

        const invitation = await RepoInvitation.findOne({ token: invite_token }).populate({
            path: 'user_id',
            select: 'first_name last_name username'
        }).populate({
            'path': 'repo_id',
            'select': 'project_name'
        });

        if (!invitation) {
            return ReS(res, constants.resource_not_found, 'Invalid or expired invitation link');
        }

        return ReS(res, constants.success_code, 'Invite details fetched successfully.', invitation);
    } catch (err) {
        logger.error(`Error at Front Controller collaborator${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function rejectInvite(req, res) {
    try {
        const { invite_token } = req.body;

        const invitation = await RepoInvitation.findOne({
            token: invite_token,
            status: invitationStatus.PENDING
        }).lean();

        if (!invitation) {
            return ReS(res, constants.resource_not_found, 'Invalid or expired invitation link');
        }

        if (invitation.is_used) {
            return ReS(res, constants.conflict_code, 'This invitation has already been used');
        }

        if (new Date() > invitation.expires_at) {
            return ReS(res, constants.conflict_code, 'This invitation has expired');
        }

        await RepoInvitation.updateOne({
            token: invite_token
        }, {
            $set: {
                status: invitationStatus.REJECTED,
                is_used: true
            }
        });

        return ReS(res, constants.success_code, 'Invitation rejected successfully.');
    } catch (err) {
        logger.error(`Error at Front Controller collaborator ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

module.exports = {
    addCollaboratorInRepo,
    updateAccess,
    removeAccess,
    sendInvite,
    acceptInvite,
    getInviteDetails,
    rejectInvite
};
