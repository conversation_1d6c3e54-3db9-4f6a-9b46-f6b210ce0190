// Constants declaration
const constants = require('../../config/constants');
const { componentUserAction } = require('../../config/component.constant');

// Npm declaration
const mongoose = require('mongoose');
const filePath = require('path');

// Service declaration
const { s3UploadStaticContent } = require('../../services/general.helper');
const logger = require('../../config/logger');
const { checkAndManageComponentVersion, checkAndManageComponentState } = require('./../../services/component.service');
const { createNewGitlabRepository, publishComponentRepository } = require('./../../services/repository.service');
const { ReS, generateSlug, sendError, escapeRegex } = require('../../services/general.helper');
const { componentType, componentState } = require('../../config/component.constant');


// Models declaration
const Components = require('../../models/component.model').Components;
const ComponentChangeLog = require('../../models/component_change_logs.model').ComponentChangeLog;
const ComponentVersions = require('../../models/component_versions.model').ComponentVersions;
const GitlabUsers = require('../../models/gitlab_users.model').GitlabUsers;
const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;
const SupportedPlatforms = require('../../models/supported_platforms.model').SupportedPlatforms;

// Validations
const { publishRepositoryComponent } = require('../../validations/cms/component/componentValidation');

async function createComponent(req, res) {
    try {
        const {
            title,
            image_url,
            thumbnail_url,
            description,
            long_description,
            selected_option,
            parent_id,
            parent_version,
            category_id,
            modifier,
            platform_data,
            identification_tag,
            is_paid,
            unlock_points,
            unlock_type,
            unlock_duration,
            difficulty_level,
            orientation,
            component_type,
            assets
        } = req.body;

        const componentSlug = generateSlug(title);

        const componentData = await Components.findOne({
            slug: componentSlug
        }, 'title slug');

        if (componentData) {
            return ReS(res, constants.conflict_code, `Oops! Component with title ${componentData.title} already exists.`);
        }

        const component_id = new mongoose.Types.ObjectId();

        const newComponent = await Components.create({
            _id: component_id,
            title: title,
            slug: componentSlug,
            image_url: image_url,
            thumbnail_url: thumbnail_url,
            short_description: description,
            long_description: long_description,
            selected_option: selected_option,
            parent_id: parent_id,
            parent_version: parent_version,
            category_id: category_id,
            modifier: modifier,
            platform_data: platform_data,
            identification_tag: identification_tag,
            difficulty_level: difficulty_level,
            created_by: req.session._id,
            is_paid: is_paid,
            unlock_points: unlock_points,
            unlock_type: unlock_type,
            unlock_duration: unlock_duration,
            orientation: orientation,
            component_type: component_type,
            assets: assets
        });

        // Create a new entry in the ComponentChangeLog model
        await ComponentChangeLog.create({
            component_id: newComponent._id,
            changed_by: req.session._id,
            type: componentUserAction.ADD_COMPONENT
        });
        return ReS(res, constants.success_code, 'Success', {
            component_id: newComponent._id
        });
    } catch (err) {
        logger.error(`Error at CMS Controller createComponent${err}`);
        return sendError(res, err);
    }
}

async function createRepositoryComponent(req, res) {
    try {
        // Destructure the request body to extract component details
        const {
            title,
            short_description,
            description,
            long_description,
            category_id,
            difficulty_level,
            component_type,
            project_name,
            identification_tag,
            is_paid,
            unlock_points,
            unlock_type,
            unlock_duration,
            assets,
            platform_id,
            repository_id,
            initialize_with_readme
        } = req.body;

        // Generate a slug for the component title
        const componentSlug = generateSlug(title);

        // Check if a component with the same slug already exists
        const existingComponent = await Components.findOne({ slug: componentSlug }, 'title slug');

        // If a component with the same title exists, return a conflict response
        if (existingComponent) {
            return ReS(res, constants.conflict_code, `Oops! Component with title ${existingComponent.title} already exists.`);
        }

        // Generate a unique ObjectId for the new component
        const component_id = new mongoose.Types.ObjectId();

        // Fetch GitLab user information based on the admin ID stored in the session
        const gitlabUser = await GitlabUsers.findOne({ admin_id: req.session._id }).lean();

        // If no GitLab user is found, return an error response
        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        const platformData = await SupportedPlatforms.findOne({ _id: platform_id }).lean();

        // If no platform is found, return an error response
        if (!platformData) {
            return ReS(res, constants.resource_not_found, 'Oops! Invalid platform provide.');
        }

        // Prepare the component object with the necessary details
        const componentObj = {
            _id: component_id,
            title,
            slug: componentSlug,
            short_description,
            long_description,
            category_id,
            identification_tag,
            difficulty_level,
            created_by: req.session._id,
            component_type,
            is_paid,
            unlock_points,
            unlock_type,
            unlock_duration,
            assets
        };

        // Handle project creation based on the presence of a project name
        if (project_name) {
            const projectSlug = generateSlug(project_name);

            // Create a new GitLab repository and associate it with the component
            const newGitlabRepository = await createNewGitlabRepository(component_id, req.session._id, projectSlug, platform_id, initialize_with_readme, description);
            componentObj['platform_data'] = [{
                platform_id,
                repository_id: newGitlabRepository._id
            }];
        }

        // If no project name is provided, associate the existing repository with the component
        if (repository_id) {

            const repositoryData = await GitlabRepository.findOne({ _id: repository_id }, '_id').lean();

            if (!repositoryData) {
                return ReS(res, constants.resource_not_found, 'Oops! Invalid code-space provided for link');
            }

            componentObj['platform_data'] = [{
                platform_id,
                repository_id
            }];
            // Assign component_id to existing repository
            await GitlabRepository.updateOne({
                _id: repository_id
            }, {
                $set: {
                    component_id: component_id
                }
            });
        }

        // Save the new component to the database
        const newComponent = await Components.create(componentObj);

        // Log the component creation in the change log
        await ComponentChangeLog.create({
            component_id: newComponent._id,
            changed_by: req.session._id,
            type: componentUserAction.ADD_COMPONENT
        });

        // Return a success response
        return ReS(res, constants.success_code, 'Success');

    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at CMS Controller createComponent: ${err}`);
        return sendError(res, err);
    }
}

async function updateRepositoryComponent(req, res) {
    try {
        const postData = req.body;

        // Fetch the component data based on the component ID from the request parameters
        const componentData = await Components.findOne(
            { _id: req.params.id },
            'component_type component_state platform_data'
        ).lean();

        // Check if the component exists
        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Not Found.');
        }

        // Validate if the component is of the type REPOSITORY
        if (componentData.component_type !== componentType.REPOSITORY) {
            return ReS(res, constants.bad_request_code, 'Oops! Invalid Component Provided.');
        }

        // Fetch GitLab user information based on the admin ID from the session
        const gitlabUser = await GitlabUsers.findOne({ admin_id: req.session._id }).lean();

        // Check if the GitLab user record exists
        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        // Generate a slug if the title is provided in postData
        if (postData.title) {
            postData.slug = generateSlug(postData.title);
        }

        // Check if the current component state is PLACEHOLDER
        if (componentData.component_state === componentState.PLACEHOLDER) {
            // If the component has platform data, update its state to ACTIVE_DRAFT
            if (componentData.platform_data.length) {
                postData.component_state = componentState.ACTIVE_DRAFT;
            } else {
                // No platform data is present, so check and potentially manage the component's state
                const isComponentStateChanged = await checkAndManageComponentState(req.params.id, postData);

                // If the component state was changed by the previous operation, update the state to ACTIVE_DRAFT
                if (isComponentStateChanged) {
                    postData.component_state = componentState.ACTIVE_DRAFT;
                }
            }
        }

        // Set the 'updated_by' field to the current session's admin ID
        postData.updated_by = req.session._id;

        const updateQuery = { '$set': postData };

        // Update the component with the new data
        await Components.updateOne({ _id: req.params.id }, updateQuery);

        // Log the change in the ComponentChangeLog
        await ComponentChangeLog.create({
            component_id: req.params.id,
            changed_by: req.session._id,
            type: componentUserAction.EDIT_COMPONENT
        });

        // Respond with success
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateRepositoryComponent: ${err}`);
        return sendError(res, err);
    }
}

async function detachRepositoryFromComponent(req, res) {
    try {
        const { id, repository_id } = req.params;
        // Fetch the component data based on the component ID from the request parameters
        const componentData = await Components.findOne(
            { _id: id },
            'component_type'
        ).lean();

        // Check if the component exists
        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Not Found.');
        }

        // Validate if the component is of the type REPOSITORY
        if (componentData.component_type !== componentType.REPOSITORY) {
            return ReS(res, constants.bad_request_code, 'Oops! Invalid Component Provided.');
        }

        const codeSpace = await GitlabRepository.findOne({ _id: repository_id }).lean();

        if (!codeSpace) {
            return ReS(res, constants.bad_request_code, 'Oops! Invalid code-space provided.');
        }

        // Update the component by removing the platform data
        await Components.updateOne(
            { _id: id },
            { $pull: { platform_data: { repository_id: repository_id } } }
        );

        // Update the GitlabRepository by unsetting the component_id
        await GitlabRepository.updateOne(
            { component_id: id, _id: repository_id },
            { $unset: { component_id: 1 } }
        );

        // Log the change in the ComponentChangeLog
        await ComponentChangeLog.create(
            {
                component_id: id,
                changed_by: req.session._id,
                type: componentUserAction.EDIT_COMPONENT
            }
        );
        // Respond with success
        return ReS(res, constants.success_code, 'Platform Unlinked Successfully');
    } catch (err) {
        logger.error(`Error at CMS Controller detachRepositoryFromComponent: ${err}`);
        return sendError(res, err);
    }
}

async function linkCodeSpacesToComponent(req, res) {
    try {
        const { id } = req.params; // Extract the component ID from request parameters
        const { code_spaces } = req.body; // Extract the list of code space IDs from the request body

        // Fetch the component data based on the component ID and ensure it is of type REPOSITORY
        const componentData = await Components.findOne({
            _id: id,
            component_type: {
                $in: [componentType.REPOSITORY, componentType.MOBILE]
            },
        },
            '_id'
        ).lean();

        // If the component doesn't exist or isn't of type REPOSITORY, return an error response
        if (!componentData) {
            return ReS(res, constants.bad_request_code, 'Oops! Invalid or Non-existent Component Provided.');
        }

        // Convert code space IDs from string to ObjectId
        const codeSpaceIds = code_spaces.map((id) => new mongoose.Types.ObjectId(id));

        // Fetch code spaces that match the provided IDs and aren't linked to any component
        const codeSpaceList = await GitlabRepository.find({
            _id: { $in: codeSpaceIds },
            component_id: { $exists: false } // Exclude code spaces that are already linked
        }, 'platform_id').lean();

        // If the number of fetched code spaces doesn't match the number provided, return an error response
        if (codeSpaceList.length !== code_spaces.length) {
            return ReS(res, constants.bad_request_code, 'Oops! One or more code-spaces are invalid or already linked.');
        }

        // Prepare an array of code spaces to be linked to the component, excluding the _id field
        const codeSpaceArray = codeSpaceList.map(({ _id, ...rest }) => ({
            ...rest,
            repository_id: _id
        }));

        // Update the component by adding the valid code spaces to the platform_data array
        await Components.updateOne(
            { _id: id },
            { $push: { platform_data: { $each: codeSpaceArray } } }
        );

        // Link the code spaces to the component by setting the component_id in the GitlabRepository documents
        await GitlabRepository.updateMany(
            { _id: { $in: codeSpaceIds } },
            { $set: { component_id: id } }
        );

        // If all operations succeed, return a success response
        return ReS(res, constants.success_code, 'Code-space linked Successfully', codeSpaceArray);
    } catch (err) {
        // Log the error and return a server error response
        logger.error(`Error at CMS Controller linkCodeSpacesToComponent: ${err}`);
        return sendError(res, err);
    }
}

async function updateComponent(req, res) {
    try {

        const postData = req.body;

        const componentData = await Components.findOne({
            _id: req.params.id
        });

        if (componentData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Not Found.');
        }

        if (postData.title) {
            postData['slug'] = generateSlug(postData.title);
        }

        // Set the 'updated_by' field in postData to the current session's _id
        postData['updated_by'] = req.session._id;

        // Invoke service function for check and manage component versioning
        await checkAndManageComponentVersion(req.params.id, postData, req.session._id);

        await Components.updateOne({
            _id: req.params.id
        }, {
            '$set': postData
        });
        // Create a new entry in the ComponentChangeLog model
        await ComponentChangeLog.create({
            component_id: req.params.id,
            changed_by: req.session._id,
            type: componentUserAction.EDIT_COMPONENT
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateComponent${err}`);
        return sendError(res, err);
    }
}

async function getAllComponents(req, res) {
    try {
        const totalDocuments = await Components.countDocuments();
        // Set default conditions
        const conditions = {
            component_state: {
                $in: [componentState.PUBLISHED]
            },
            component_type: {
                $in: [componentType.REPOSITORY, componentType.MOBILE]
            },
            created_by_user: { $exists: true }
        };

        // Set default sort
        const sort = {
            'created_at': -1
        };

        if (req.body.category_id) {
            conditions['category_id'] = new mongoose.Types.ObjectId(req.body.category_id);
        }

        if (req.body.is_deleted != undefined) {
            conditions['component_state'] = componentState.SOFT_DELETED;
            delete conditions['parent_id'];
            sort['deleted_at'] = -1;
        }

        if (req.body.title) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.title);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const limit = (req.body.limit) ? req.body.limit : 10;
        const skip = (req.body.skip) ? req.body.skip : 0;
        const filterDocuments = await Components.countDocuments(conditions);
        const query = [{
            $match: conditions
        }, {
            '$sort': sort
        }, {
            '$skip': skip
        }, {
            '$limit': limit
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                short_description: 1,
                long_description: 1,
                created_at: 1,
                orientation: 1,
                category_id: 1,
                component_type: 1,
                component_state: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                is_featured: 1
            }
        }];
        const componentList = await Components.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllComponents${err}`);
        return sendError(res, err);
    }
}

async function getComponentsDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            '_id': req.params.id
        };
        const componentData = await Components.findOne(conditions)
            .populate({
                'path': 'platform_data.platform_id',
                'select': 'title slug image_url'
            }).populate({
                'path': 'platform_data.repository_id'
            }).populate({
                'path': 'collection_id',
                'select': 'title slug'
            }).lean();
        if (!componentData) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Not Found.');
        }
        componentData['previousVersions'] = await ComponentVersions.distinct('version', { component_id: req.params.id }).lean();
        return ReS(res, constants.success_code, 'Data Fetched', componentData);
    } catch (err) {
        logger.error(`Error at CMS Controller getComponentsDetails${err}`);
        return sendError(res, err);
    }
}

async function getChildComponentsDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            'parent_id': req.params.parent_id,
            'is_deleted': false
        };
        const componentData = await Components.find(conditions,
            {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                short_description: 1,
                long_description: 1,
                version: 1,
                parent_version: 1,
                parent_id: 1,
                is_active: 1,
                is_deleted: 1,
                orientation: 1
            }).lean();
        return ReS(res, constants.success_code, 'Data Fetched', componentData);
    } catch (err) {
        logger.error(`Error at CMS Controller getChildComponentsDetails${err}`);
        return sendError(res, err);
    }
}

async function deleteComponent(req, res) {
    try {

        const componentData = await Components.findOne({
            _id: req.params.id
        }).lean();

        if (componentData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Not Found.');
        }

        // Fetch child components with the given parent_id
        const childComponents = await Components.find({
            parent_id: req.params.id
        }).lean();

        // If child components exist, return a bad request response
        if (childComponents && childComponents.length) {
            return ReS(res, constants.bad_request_code, 'Oops! Parent component deletion is not permitted');
        }

        // Delete the component
        await Components.updateOne({
            _id: req.params.id
        }, {
            $set: {
                component_state: componentState.SOFT_DELETED,
                deleted_at: new Date()
            }
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller deleteComponent${err}`);
        return sendError(res, err);
    }
}

async function restoreComponent(req, res) {
    try {

        const componentData = await Components.findOne({
            _id: req.params.id
        });

        if (componentData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Not Found.');
        }

        // Delete the component
        await Components.updateOne({
            _id: req.params.id
        }, {
            $set: {
                component_state: componentState.PUBLISHED
            },
            $unset: {
                deleted_at: 1
            }
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller restoreComponent${err}`);
        return sendError(res, err);
    }
}

async function getComponentVersion(req, res) {
    try {
        const componentVersion = await ComponentVersions.findOne({
            component_id: req.params.id,
            version: req.params.version
        }).populate({
            'path': 'options.section_id',
            'select': 'title slug editor_type'
        }).lean();

        if (!componentVersion) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Version Not Found');
        }

        return ReS(res, constants.success_code, 'Data Fetched', componentVersion);
    } catch (err) {
        logger.error(`Error at CMS Controller getComponentVersion${err}`);
        return sendError(res, err);
    }
}

async function uploadComponentAssets(req, res) {
    try {
        // Check if the 'documents' field exists in the 'req.files' object and if it's not an array but an object
        if (req.files && req.files.documents && !Array.isArray(req.files.documents) && typeof req.files.documents == 'object') {
            // Convert the 'documents' object into an array containing a single element
            req.files.documents = [req.files.documents];
        }
        const assets = [];
        const maxSizeAllowed = 5 * 1024 * 1024; // 5 MB in bytes
        if (req && req.files && req.files.documents && req.files.documents.length) {

            const isFileSizeValid = req.files.documents.every((file) => file.size <= maxSizeAllowed);

            if (!isFileSizeValid) {
                return ReS(res, constants.bad_request_code, 'This file exceeds the maximum size. Max file size is 5mb.');
            }
            for (const document of req.files.documents) {
                const fileExtension = filePath.extname(document.name);
                const filename = `${new Date().getTime()}${fileExtension}`;
                const documentPath = `design-kit/${filename}`;
                const metaObj = JSON.parse(req.body[document.name]);
                const uploadRes = await s3UploadStaticContent(document, documentPath);
                if (uploadRes) {
                    assets.push({
                        'file_name': metaObj.name,
                        'file_original_name': document.name,
                        'file_url': documentPath,
                        'file_mime_type': document.mimetype,
                        'file_extension': fileExtension,
                        'file_notes': metaObj.notes
                    });
                } else {
                    return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
                }
            }
        }
        return ReS(res, constants.success_code, 'Data Fetched', assets);
    } catch (err) {
        logger.error(`Error at CMS Controller uploadComponentAssets${err}`);
        return sendError(res, err);
    }
}

async function updateComponentStatus(req, res) {
    try {

        const componentData = await Components.findOne({
            _id: req.params.id
        });

        if (componentData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Not Found.');
        }

        const { component_state } = req.body;

        // Updated the status of component
        await Components.updateOne({
            _id: req.params.id
        }, {
            $set: {
                component_state: component_state
            }
        });
        // Update all components with the specified parent_id
        await Components.updateMany({
            parent_id: req.params.id
        }, {
            $set: {
                component_state: component_state
            }
        });
        // Create a new entry in the ComponentChangeLog model
        await ComponentChangeLog.create({
            component_id: req.params.id,
            changed_by: req.session._id,
            type: componentUserAction.CHANGE_STATUS,
            logs: {
                component_state: component_state
            }
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateComponentStatus${err}`);
        return sendError(res, err);
    }
}

async function getAllComponentList(req, res) {
    try {
        const totalDocuments = await Components.countDocuments();
        // Set default conditions
        const conditions = {
            component_state: {
                $in: [componentState.PUBLISHED, componentState.PRIVATE]
            },
            component_type: {
                $in: [componentType.REPOSITORY, componentType.MOBILE]
            },
            created_by_user: { $exists: true }
        };

        if (req.body.component_state) {
            conditions['component_state'] = req.body.component_state;
        }

        // Set default sort
        const sort = {
            'created_at': -1
        };

        if (req.body.category_id && req.body.category_id.length) {
            conditions['category_id'] = {
                $in: (req.body.category_id).map((id) => new mongoose.Types.ObjectId(id))
            };
        }

        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const limit = (req.body.limit) ? req.body.limit : 10;
        const skip = (req.body.skip) ? req.body.skip : 0;
        const filterDocuments = await Components.countDocuments(conditions);
        const query = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'categories',
                let: {
                    category_id: '$category_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$category_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        category_name: 1,
                        category_slug: 1
                    }
                }],
                as: 'category'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                short_description: 1,
                long_description: 1,
                created_at: 1,
                version: 1,
                is_active: 1,
                category: {
                    $arrayElemAt: ['$category', 0]
                },
                orientation: 1,
                component_type: 1,
                is_featured: 1,
                component_state: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });
        const componentList = await Components.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllComponentList${err}`);
        return sendError(res, err);
    }
}

async function getAllComponentChangeLogs(req, res) {
    try {
        const totalDocuments = await ComponentChangeLog.countDocuments();
        // Set default conditions
        const conditions = {
        };

        // Set default sort
        const sort = {
            'created_at': -1
        };

        // Check if 'component_id' is provided in the request body
        if (req.body.component_id) {
            conditions['component_id'] = new mongoose.Types.ObjectId(req.body.component_id);
        }

        // Check if 'changed_by' is provided in the request body
        if (req.body.changed_by) {
            conditions['changed_by'] = new mongoose.Types.ObjectId(req.body.changed_by);
        }

        // Check if 'type' is provided in the request body
        if (req.body.type) {
            conditions['type'] = req.body.type;
        }

        const limit = (req.body.limit) ? req.body.limit : 10;
        const skip = (req.body.skip) ? req.body.skip : 0;
        const filterDocuments = await ComponentChangeLog.countDocuments(conditions);
        const query = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'components',
                let: {
                    component_id: '$component_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$component_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        title: 1,
                        slug: 1,
                        image_url: 1
                    }
                }],
                as: 'component'
            }
        }, {
            $lookup: {
                from: 'admins',
                let: {
                    changed_by: '$changed_by'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$changed_by']
                            }]
                        }
                    }
                }, {
                    $project: {
                        email: 1,
                        first_name: 1,
                        last_name: 1,
                        is_super_admin: 1
                    }
                }],
                as: 'changed_by'
            }
        }, {
            $project: {
                changed_by: {
                    $arrayElemAt: ['$changed_by', 0]
                },
                type: 1,
                component: {
                    $arrayElemAt: ['$component', 0]
                },
                logs: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });
        const componentList = await ComponentChangeLog.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllComponentChangeLogs${err}`);
        return sendError(res, err);
    }
}

async function createComponentPlaceHolder(req, res) {
    try {
        const { title = 'Untitled', component_type = componentType.REPOSITORY } = req.body;

        // Fetch GitLab user information from the database based on the admin ID stored in the session
        const gitlabUser = await GitlabUsers.findOne({
            admin_id: req.session._id
        }).lean();

        // Check if the GitLab user record was found
        if (!gitlabUser) {
            // If the GitLab user record does not exist, respond with an error message
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exists.');
        }

        const componentSlug = generateSlug(title);

        const component_id = new mongoose.Types.ObjectId();

        const newComponent = await Components.create({
            _id: component_id,
            title: title,
            slug: componentSlug,
            component_state: componentState.PLACEHOLDER,
            created_by: req.session._id,
            component_type: component_type
        });

        // Create a new entry in the ComponentChangeLog model
        await ComponentChangeLog.create({
            component_id: newComponent._id,
            changed_by: req.session._id,
            type: componentUserAction.ADD_COMPONENT
        });
        return ReS(res, constants.success_code, 'Success', {
            component_id: newComponent._id
        });
    } catch (err) {
        logger.error(`Error at CMS Controller createComponentPlaceHolder${err}`);
        return sendError(res, err);
    }
}

async function publishDraftComponent(req, res) {
    try {
        const componentId = req.params.id;

        // Fetch the component by ID with selected fields
        const component = await Components.findById(componentId)
            .select('title slug short_description component_type category_id component_state is_paid platform_data image_url')
            .lean();

        // Return an error response if the component is not found
        if (!component) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Not Found.');
        }

        // Validate that the component is already published
        if (component.component_state == componentState.PUBLISHED) {
            return ReS(res, constants.bad_request_code, 'Oops! Component is already published.');
        }

        // Validate that the component is in an active draft state
        if (component.component_state !== componentState.ACTIVE_DRAFT) {
            return ReS(res, constants.bad_request_code, 'Oops! Component Could Not Be Published.');
        }

        // Validate component data before publishing
        const { error } = publishRepositoryComponent(component);
        if (error) {
            return ReS(res, constants.bad_request_code, error.details[0].message, 'ValidationError');
        }

        // Check for a unique slug across non-placeholder components
        const existingComponent = await Components.findOne({
            slug: component.slug,
            _id: { $ne: component._id },
            component_state: { $ne: componentState.PLACEHOLDER }
        }).lean();

        // Return an error if a component with the same slug already exists
        if (existingComponent) {
            return ReS(res, constants.conflict_code, `Oops! Component with title ${component.title} already exists.`);
        }

        // Ensure that at least one code-space is linked to the component
        if (!component.platform_data?.length) {
            return ReS(res, constants.bad_request_code, 'Oops! No Linked Code-space Found.');
        }

        // Publish the component repositories if it is valid
        await publishComponentRepository(componentId);

        // Update the component's state to published
        await Components.updateOne(
            { _id: component._id },
            {
                $set: {
                    component_state: componentState.PUBLISHED,
                    updated_by: req.session._id
                }
            }
        );

        // Log the component's publication in the change log
        await ComponentChangeLog.create({
            component_id: componentId,
            changed_by: req.session._id,
            type: componentUserAction.PUBLISH_COMPONENT
        });

        // Return a success response
        return ReS(res, constants.success_code, 'Component published successfully');

    } catch (err) {
        // Log and handle any errors
        logger.error(`Error at CMS Controller publishActiveDraftComponent: ${err}`);
        return sendError(res, err);
    }
}

async function markComponentAsFeatured(req, res) {
    try {
        const componentId = req.params.id;

        const { is_featured } = req.body;

        // Fetch the component by ID with selected fields
        const component = await Components.findById(componentId)
            .select('title slug short_description component_type component_state')
            .lean();

        // Return an error response if the component is not found
        if (!component) {
            return ReS(res, constants.resource_not_found, 'Oops! Component Not Found.');
        }

        // Validate that the component is already published
        if (component.component_state !== componentState.PUBLISHED) {
            return ReS(res, constants.bad_request_code, 'Oops! Component is not published.');
        }

        await Components.updateOne({
            _id: componentId
        }, {
            is_featured: is_featured
        });

        // Return a success response
        return ReS(res, constants.success_code, 'Component featured status updated successfully');
    } catch (err) {
        logger.error(`Error at CMS Controller markComponentAsFeatured: ${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createComponent,
    updateComponent,
    getAllComponents,
    getComponentsDetails,
    getChildComponentsDetails,
    deleteComponent,
    restoreComponent,
    getComponentVersion,
    uploadComponentAssets,
    updateComponentStatus,
    getAllComponentList,
    getAllComponentChangeLogs,
    createRepositoryComponent,
    updateRepositoryComponent,
    detachRepositoryFromComponent,
    linkCodeSpacesToComponent,
    createComponentPlaceHolder,
    publishDraftComponent,
    markComponentAsFeatured
};