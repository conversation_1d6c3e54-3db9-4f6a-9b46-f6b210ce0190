const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const categorySchema = new Schema({
    category_name: {
        type: String,
        require: true
    },
    category_slug: {
        type: String,
        require: true,
        unique: true
    },
    category_type: [{
        type: String,
        require: true
    }],
    image_url: {
        type: String
    },
    short_description: {
        type: String
    },
    is_active: {
        type: Boolean,
        default: true
    },
    is_deleted: {
        type: Boolean,
        default: false
    },
    parent_id: {
        type: mongoose.Types.ObjectId,
        ref: 'category',
        default: null     // Null indicates top-level category
    },
    created_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    updated_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

categorySchema.set('autoIndex', true);

const Category = mongoose.model('category', categorySchema);

module.exports = {
    Category
};