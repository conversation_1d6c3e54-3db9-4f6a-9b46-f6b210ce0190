const axios = require('axios');
const redis = require('../config/redis');
const logger = require('../config/logger');

/**
 * Blog service for fetching and caching Medium blog data
 * Uses RSS2JSON service with Redis TTL-based caching
 */

// Cache configuration
const CACHE_EXPIRATION = 3600; // 1 hour in seconds
const CACHE_KEY_PREFIX = 'blogs:medium';

/**
 * Fetches Medium blogs using RSS2JSON service
 * @param {string} profile - Medium profile/publication name
 * @returns {Promise<Array>} Array of blog posts
 */
async function fetchMediumBlogsViaRSS2JSON(profile) {
    try {
        logger.info(`Fetching Medium blogs via RSS2JSON for profile: ${profile}`);
        const mediumUrl = `https://medium.com/feed/${profile}`;
        const rss2jsonUrl = `https://api.rss2json.com/v1/api.json?rss_url=${encodeURIComponent(mediumUrl)}`;

        const response = await axios.get(rss2jsonUrl, {
            timeout: 15000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        if (response.data && response.data.status === 'ok' && response.data.items) {
            return processBlogData(response.data);
        } else {
            throw new Error('Invalid response from RSS2JSON service');
        }
    } catch (error) {
        logger.error(`Error fetching Medium blogs via RSS2JSON for profile ${profile}: ${error.message}`);
        throw error;
    }
}

/**
 * Processes raw blog data from RSS2JSON response
 * @param {Object} rssData - RSS2JSON response data
 * @returns {Array} Processed blog posts array
 */
function processBlogData(rssData) {
    const mediumBlogs = [];

    if (rssData && rssData.items) {
        for (const item of rssData.items) {
            try {
                // Extract thumbnail from content or description
                let thumbnail;
                try {
                    const content = item.content || item.description || '';
                    const thumbnailMatch = content.match(/<img[^>]+src="([^">]+)"/);
                    thumbnail = thumbnailMatch ? thumbnailMatch[1] : undefined;
                } catch (e) {
                    thumbnail = undefined;
                }

                // Extract description and clean HTML
                let description;
                try {
                    const content = item.content || item.description || '';
                    description = content.replace(/<[^>]*>/g, '').substring(0, 200);
                } catch (e) {
                    description = '';
                }

                mediumBlogs.push({
                    id: item.guid || item.link,
                    title: item.title,
                    link: (item.link) ? item.link.replace(/[?&]source=[^&]+/, '') : undefined,
                    author: item.author,
                    published: item.pubDate,
                    created: item.pubDate,
                    category: item.categories || [],
                    media: undefined,
                    thumbnail: (thumbnail && thumbnail.includes('cdn-images-1.medium.com')) ? thumbnail : undefined,
                    description: description
                });
            } catch (error) {
                logger.warn(`Error processing blog item: ${error.message}`);
                // Continue processing other items
            }
        }
    }

    return mediumBlogs;
}

/**
 * Gets Medium blogs from cache or fetches fresh data if cache is empty
 * @param {string} profile - Medium profile/publication name
 * @returns {Promise<Array>} Array of blog posts
 */
async function getMediumBlogs(profile) {
    try {
        const cacheKey = `${CACHE_KEY_PREFIX}:${profile}`;

        // First try to get from cache
        const cachedData = await redis.get(cacheKey);

        if (cachedData) {
            const parsedData = JSON.parse(cachedData);
            logger.info(`Serving ${parsedData.length} blogs from cache for profile: ${profile}`);
            return parsedData;
        }

        // If no cache, fetch fresh data and cache it
        logger.info(`No cache found for profile ${profile}, fetching fresh data`);
        const freshData = await fetchMediumBlogsViaRSS2JSON(profile);

        // Cache the fresh data with TTL
        await redis.set(cacheKey, JSON.stringify(freshData), 'EX', CACHE_EXPIRATION);
        logger.info(`Successfully cached ${freshData.length} blogs for profile: ${profile}`);

        return freshData;
    } catch (error) {
        logger.error(`Error getting Medium blogs for profile ${profile}: ${error.message}`);
        // Return empty array as fallback to prevent API errors
        return [];
    }
}

/**
 * Gets cache status for a profile
 * @param {string} profile - Medium profile/publication name
 * @returns {Promise<Object>} Cache status information
 */
async function getCacheStatus(profile) {
    try {
        const cacheKey = `${CACHE_KEY_PREFIX}:${profile}`;
        const cachedData = await redis.get(cacheKey);

        if (cachedData) {
            const parsedData = JSON.parse(cachedData);
            const ttl = await redis.ttl(cacheKey);

            return {
                profile,
                cached: true,
                cached_at: new Date().toISOString(),
                total_count: parsedData.length,
                ttl_seconds: ttl,
                expires_in_minutes: Math.round(ttl / 60)
            };
        }

        return {
            profile,
            cached: false,
            cached_at: null,
            total_count: 0,
            ttl_seconds: 0,
            expires_in_minutes: 0
        };
    } catch (error) {
        logger.error(`Error getting cache status for profile ${profile}: ${error.message}`);
        return {
            profile,
            cached: false,
            error: error.message
        };
    }
}

module.exports = {
    getMediumBlogs,
    getCacheStatus
};
