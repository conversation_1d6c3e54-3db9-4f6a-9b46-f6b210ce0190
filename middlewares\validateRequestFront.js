const constants = require('../config/constants');
const Jwt = require('jsonwebtoken');
const logger = require('../config/logger');
const redis = require('../config/redis');

module.exports = async function (req, res, next) {
    const token = (req.body && req.body.token) || (req.query && req.query.token) || req.headers['token'];
    if (token) {
        try {
            let isError = false;
            let errorMessage = '';
            // check if token is expired or not
            await Jwt.verify(token, process.env.JWT_FRONT_SECRET_KEY, async function (err, decoded) {
                if (decoded) {
                    req['session'] = decoded.data;
                    // Check if the token exists in Redis for this session
                    const storedToken = await redis.get(`user:${decoded?.data?._id}:session:${decoded?.data?.sessionId}`);
                    if (!storedToken || storedToken !== token) {
                        isError = true;
                        errorMessage = 'Sorry! Your token seems to be expired, please request a new one.';
                        return;
                    }
                }
                if (err) {
                    isError = true;
                    errorMessage = 'Sorry! Your token seems to be expired, please request a new one.';
                    return;
                }
            });
            if (isError) {
                return res.status(constants.forbidden_code).json({
                    'status': constants.forbidden_code,
                    'message': errorMessage
                });
            }
            next();
        }
        catch (err) {
            logger.error(`error at validateRequestFront ${err}`);
            return res.status(constants.server_error_code).json({
                'status': constants.server_error_code,
                'message': 'Oops! Something went wrong.',
                'error': err
            });
        }
    } else {
        return res.status(constants.unauthorized_code).json({
            'status': constants.unauthorized_code,
            'message': 'Token has not been passed, please check again.'
        });
    }
};