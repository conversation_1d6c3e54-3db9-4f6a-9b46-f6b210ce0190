const { getEvent } = require('../services/fingerprint.service');
const { FingerprintArchives } = require('../models/fingerprint_archives.model');
const { Fingerprint } = require('../models/fingerprint.model');
const mongoose = require('mongoose');
const logger = require('../config/logger');

/**
 * Retrieves fingerprint data from the database or generates it if not found.
 * @param {string} visitorId - The unique identifier for the visitor.
 * @param {string} requestId - The unique identifier for the request.
 * @returns {Object} - The fingerprint data.
 */
async function storeAndGetFingerprintData(visitorId, requestId) {
    try {
        // Attempt to find fingerprint data in the database
        const fingerprintData = await FingerprintArchives.findOne({
            'identification.data.visitorId': visitorId,
            'identification.data.requestId': requestId
        }).lean();

        if (!fingerprintData) {
            // If fingerprint data not found, generate it
            const fingerprintResponse = await getEvent(requestId);
            fingerprintResponse['requestId'] = fingerprintResponse.identification.data.requestId;
            fingerprintResponse['visitorId'] = fingerprintResponse.identification.data.visitorId;
            // Store generated fingerprint data in the database
            await FingerprintArchives.create(fingerprintResponse);
            return fingerprintResponse;
        } else {
            fingerprintData['requestId'] = fingerprintData.identification.data.requestId;
            fingerprintData['visitorId'] = fingerprintData.identification.data.visitorId;
            // Return found fingerprint data
            return fingerprintData;
        }
    } catch (error) {
        // Handle errors
        console.error('Error from fingerprint helper function storeAndGetFingerprintData:', error);
        throw error;
    }
}

// Get Facet data for given fingerprint userId, this should return UserFacetResponse
async function getFingerprintUserFacets(userId) {
    try {
        const aggregate = [
            {
                $match:
                {
                    userId: new mongoose.Types.ObjectId(userId)
                }
            },
            {
                $unwind: '$visitorInformation.visits'
            },
            {
                $facet: {
                    incognito: [
                        {
                            $sortByCount:
                                '$visitorInformation.visits.identification.data.incognito'
                        },
                        {
                            $addFields: {
                                _queryField:
                                    'visitorInformation.visits.identification.data.incognito',
                                incognito: '$_id'
                            }
                        },
                        {
                            $unset: '_id'
                        }
                    ],
                    averageConfidenceScore: [
                        {
                            $group: {
                                _id: null,
                                _queryField: {
                                    $first:
                                        'visitorInformation.visits.identification.data.confidence.score'
                                },
                                score: {
                                    $avg: '$visitorInformation.visits.identification.data.confidence.score'
                                }
                            }
                        },
                        {
                            $unset: '_id'
                        }
                    ],
                    country: [
                        {
                            $sortByCount:
                                '$visitorInformation.visits.ipInfo.data.v4.geolocation.country.name'
                        },
                        {
                            $addFields: {
                                _queryField:
                                    'visitorInformation.visits.ipInfo.data.v4.geolocation.country.name',
                                country: '$_id'
                            }
                        },
                        {
                            $unset: '_id'
                        }
                    ],
                    postalCode: [
                        {
                            $sortByCount:
                                '$visitorInformation.visits.ipInfo.data.v4.geolocation.postalCode'
                        },
                        {
                            $addFields: {
                                _queryField:
                                    'visitorInformation.visits.ipInfo.data.v4.geolocation.postalCode',
                                postalCode: {
                                    $ifNull: ['$_id', 'Unavailable']
                                }
                            }
                        },
                        {
                            $unset: '_id'
                        }
                    ],
                    city: [
                        {
                            $sortByCount:
                                '$visitorInformation.visits.ipInfo.data.v4.geolocation.city'
                        },
                        {
                            $addFields: {
                                _queryField:
                                    'visitorInformation.visits.ipInfo.data.v4.geolocation.city',
                                city: {
                                    $ifNull: ['$_id', 'Unavailable']
                                }
                            }
                        },
                        {
                            $unset: '_id'
                        }
                    ],
                    ipAddresses: [
                        {
                            $sortByCount:
                                '$visitorInformation.visits.ipInfo.data.v4.address'
                        },
                        {
                            $addFields: {
                                _queryField:
                                    'visitorInformation.visits.ipInfo.data.v4.address',
                                ipAddress: '$_id'
                            }
                        },
                        {
                            $unset: '_id'
                        }
                    ],
                    device: [
                        {
                            $sortByCount:
                                '$visitorInformation.visits.identification.data.browserDetails'
                        },
                        {
                            $addFields: {
                                _queryField:
                                    'visitorInformation.visits.identification.data.browserDetails',
                                name: {
                                    $concat: [
                                        '$_id.os',
                                        ' ',
                                        '$_id.osVersion',
                                        ' (',
                                        '$_id.browserName',
                                        ' ',
                                        '$_id.browserFullVersion',
                                        ')'
                                    ]
                                },
                                browserName: {
                                    $concat: [
                                        '$_id.browserName',
                                        ' ',
                                        '$_id.browserFullVersion'
                                    ]
                                }
                            }
                        },
                        {
                            $unset: '_id'
                        }
                    ]
                }
            },
            {
                $addFields:
                {
                    averageConfidenceScore: {
                        $first: '$averageConfidenceScore'
                    }
                }
            }

        ];
        // Run above aggregation pipeline
        const result = await Fingerprint.aggregate(aggregate);
        // return the result
        return result[0];
    } catch (error) {
        // Handle errors
        console.error('Error from fingerprint helper function getFingerprintUserFacets:', error);
        throw error;
    }
}

async function addFingerprintInDb(userId, visitorId, requestId) {
    try {
        const fingerprintData = await storeAndGetFingerprintData(visitorId, requestId);

        fingerprintData.createdOn = new Date();
        fingerprintData.updatedOn = new Date();

        const userObjectId = new mongoose.Types.ObjectId(userId);

        const fingerprintDataFromDb = await Fingerprint.findOne({
            userId: userObjectId,
            fingerprintVisitorId: visitorId
        }).lean();

        const visits = [];

        if (fingerprintDataFromDb && fingerprintDataFromDb.visitorInformation && fingerprintDataFromDb.visitorInformation.visits && fingerprintDataFromDb.visitorInformation.visits.length) {
            visits.push(...fingerprintDataFromDb.visitorInformation.visits);
        }
        visits.push(fingerprintData);

        const uniqueVisits = Array.from(visits.reduce((map, obj) => map.set(obj.requestId, obj), new Map()).values());

        const updatedResponse = await Fingerprint.updateOne(
            {
                userId: userObjectId, fingerprintVisitorId: visitorId
            },
            {
                $set: {
                    updatedOn: fingerprintData.updatedOn,
                    'visitorInformation.visits': uniqueVisits
                },
                $setOnInsert: {
                    createdOn: fingerprintData.createdOn
                }
            }, { upsert: true });
        // check updateResponse to see if the user is created or not
        logger.info(updatedResponse);
    } catch (error) {
        logger.error('Error from fingerprint helper function addFingerprintInDb:', error);
        throw error;
    }

}

module.exports = {
    storeAndGetFingerprintData,
    getFingerprintUserFacets,
    addFingerprintInDb
};
