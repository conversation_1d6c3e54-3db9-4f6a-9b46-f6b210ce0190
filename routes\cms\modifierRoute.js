const express = require('express');
const router = express.Router();

const { createModifierValidation, updateModifierValidation } = require('../../middlewares/validations/cms/modifier/modifierValidation');
const { createModifier, getAllModifier, getModifierDetails, updateModifier, getAllModifierSortList, getModifierDetailList } = require('../../controller/cms/modifier.controller');


router.post('/create', createModifierValidation, createModifier);
router.put('/update/:id', updateModifierValidation, updateModifier);
router.get('/sort-list', getAllModifierSortList);
router.post('/list', getAllModifier);
router.get('/details/:id', getModifierDetails);
router.post('/details', getModifierDetailList);


module.exports = router;