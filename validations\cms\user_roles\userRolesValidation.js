const Joi = require('joi');
class UserRoleValidation {
    createUserRole(value) {
        const schema = Joi.object({
            role: Joi.string().trim().required(),
            permission_groups: Joi.array().items(Joi.string().trim()).min(1).unique().required(),
            is_active: Joi.boolean().optional()
        });
        return schema.validate(value);
    }

    updateUserRole(value) {
        const schema = Joi.object({
            role: Joi.string().trim().optional(),
            permission_groups: Joi.array().items(Joi.string().trim()).min(1).unique().optional(),
            is_active: Joi.boolean().optional()
        });
        return schema.validate(value);
    }

    updateUserRoleStatus(value) {
        const schema = Joi.object({
            is_active: Joi.boolean().required()
        });
        return schema.validate(value);
    }
}

module.exports = new UserRoleValidation();