const express = require('express');
const router = express.Router();
const { addCollaboratorInRepo, updateAccess, removeAccess, sendInvite, acceptInvite, getInviteDetails, rejectInvite } = require('../../controller/front/collaborator.controller');
const { addCollaboratorValidation, removeCollaboratorValidation, sendInviteValidation, verifyInviteValidation } = require('./../../middlewares/validations/front/collaborator/collaboratorValidation');

router.post('/add-collaborator', addCollaboratorValidation, addCollaboratorInRepo);
router.post('/update-collaborator-access', addCollaboratorValidation, updateAccess);
router.post('/remove-collaborator-access', removeCollaboratorValidation, removeAccess);
router.post('/send-invite', sendInviteValidation, sendInvite);
router.post('/accept-invite', verifyInviteValidation, acceptInvite);
router.get('/invite/:invite_token', getInviteDetails);
router.post('/reject-invite', verifyInviteValidation, rejectInvite);

module.exports = router;