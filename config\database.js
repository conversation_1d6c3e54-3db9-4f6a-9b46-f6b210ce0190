const mongoose = require('mongoose');
const logger = require('./logger');

const db = () => {
    try {
        const options = {
            useNewUrlParser: true,
            useUnifiedTopology: true,
            autoIndex: false, // Don't build indexes
            maxPoolSize: 100, // Maintain up to 100 socket connections
            serverSelectionTimeoutMS: 5000 // Keep trying to send operations for 5 seconds
            // socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
        };

        if (process.env.MONGOOSE_DEBUG === 'true') {
            mongoose.set('debug', true);
        }

        mongoose.connect(process.env.DB_URI, options)
            .then(() => {
                logger.info(`MongoDB Connected at ${process.env.DB_HOST}`);
            })
            .catch((err) => logger.error(err));
    } catch (err) {
        logger.error('Error at db connection', err);
    }
};

module.exports = db;