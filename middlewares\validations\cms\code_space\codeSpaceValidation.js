const { createRepository } = require('../../../../validations/cms/code_space/codeSpaceValidation');

class CodeSpaceValidationMiddleware {
    createRepositoryValidation(req, res, next) {
        const { value, error } = createRepository(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new CodeSpaceValidationMiddleware();