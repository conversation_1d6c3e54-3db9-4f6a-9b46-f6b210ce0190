const settingsList = require('../config/initial_data/settings.json');
const logger = require('../config/logger');
const gitlabAdminObj = require('../config/initial_data/gitlab_admin.json');
const defaultLicenseObj = require('../config/initial_data/default_license.json');

// Models declaration
const Admins = require('../models/admins.model').Admins;
const Settings = require('../models/settings.model').Settings;
const GitlabUsers = require('../models/gitlab_users.model').GitlabUsers;
const GitlabUserTokens = require('../models/gitlab_user_tokens.model').GitlabUserTokens;
const PlatformLicense = require('../models/platform_licenses.model').PlatformLicense;

const { gitlabAdminScope } = require('../config/gitlab.constant');

const createAdminUser = async () => {
    try {
        const adminUser = await Admins.findOne({ 'email': '<EMAIL>' });
        if (!adminUser) {
            await Admins.create({
                // Password is "123456789"
                'email': '<EMAIL>',
                'password': 'd9e6762dd1c8eaf6d61b3c6192fc408d4d6d5f1176d0c29169bc24e71c3f274ad27fcd5811b313d681f7e55ec02d73d499c95455b6b5bb503acf574fba8ffe85',
                'is_super_admin': true,
                'is_active': true,
                'createdAt': new Date()
            });
            logger.info('Admin user created');
        } else {
            logger.info('Admin user already exists');
        }
    } catch (error) {
        logger.error('Admin User: Error while checking init.', error);
    }
};

// Function to check and update default site settings.
const checkDefaultSiteSettings = async () => {
    try {
        // Iterate through the list of settings
        for (const setting of settingsList) {
            if (setting) {
                // Check if the setting exists in the database
                const foundSetting = await Settings.findOne({
                    setting_slug: setting.setting_slug
                }).lean();

                // If the setting exists
                if (foundSetting) {
                    // Check if the version in the database is different from the static content version
                    if (foundSetting.version !== setting.version) {
                        // Update the setting in the database with static content values
                        await Settings.updateOne({
                            setting_slug: setting.setting_slug
                        }, {
                            '$set': {
                                'version': setting.version,
                                'values': setting.values,
                                'setting_name': setting.setting_name
                            }
                        });
                        logger.info(`Settings version mismatch found updating with static content for ${setting.setting_slug}`);
                    } else {
                        logger.info(`Settings ${setting.setting_slug} already up to date`);
                    }
                } else {
                    // If the setting does not exist, create it with static content values
                    await Settings.create(setting);
                    logger.info(`Settings not found for ${setting.setting_slug} added from static content`);
                }
            }
        }
    } catch (error) {
        logger.error(`Admin User: Error while checking initialization.${error}`);
    }
};

// Function to check and update the default GitLab admin user settings.
const checkDefaultGitlabAdminUser = async () => {
    try {
        // Proceed only if gitlabAdminData is available.
        if (gitlabAdminObj) {
            const gitlabAdminData = { ...gitlabAdminObj, scopes: gitlabAdminScope };
            // Attempt to find the existing GitLab admin user in the database.
            const foundGitlabAdmin = await GitlabUsers.findOne({ is_super_admin: true }).lean();

            if (foundGitlabAdmin) {
                // If the found admin user version differs from the static version, update it.
                if (foundGitlabAdmin.version !== gitlabAdminData.version) {
                    const gitlabAdmin = await GitlabUsers.findOneAndUpdate({ is_super_admin: true }, { '$set': gitlabAdminData });
                    await GitlabUserTokens.updateOne({
                        user_id: gitlabAdmin._id
                    }, {
                        $set: gitlabAdminData,
                        $setOnInsert: {
                            createdOn: new Date()
                        }
                    }, {
                        upsert: true
                    }).lean();
                    logger.info('Gitlab master admin version mismatch found. Updating with static content.');
                } else {
                    // Log that the admin user is already up-to-date.
                    logger.info('Gitlab master admin already up to date.');
                }
            } else {
                // If no admin user is found, create a new entry with the static data.
                const newGitlabUserDB = await GitlabUsers.create(gitlabAdminData);
                await GitlabUserTokens.create({
                    user_id: newGitlabUserDB._id,
                    personal_access_tokens: gitlabAdminData.personal_access_tokens,
                    personal_access_token_name: gitlabAdminData.personal_access_token_name,
                    token_expires_at: gitlabAdminData.token_expires_at,
                    scopes: gitlabAdminData.scopes
                });
                logger.info('Gitlab master admin not found. Added from static content.');
            }
        }
    } catch (error) {
        // Log any errors encountered during the process.
        logger.error(`Gitlab Admin User: Error while checking initialization. ${error}`);
    }
};

const createDefaultLicense = async () => {
    try {
        const defaultLicense = await PlatformLicense.findOne({ is_default: true }).lean();

        if (defaultLicense) {
            if (defaultLicense.version !== defaultLicenseObj.version) {
                await PlatformLicense.findOneAndUpdate({
                    is_default: true
                }, {
                    '$set': defaultLicenseObj
                });
                logger.info('Default license version mismatch found. Updating with static content.');
            } else {
                logger.info('Default license already up to date.');
            }
        } else {
            await PlatformLicense.create(defaultLicenseObj);
            logger.info('Default license not found. Added from static content.');
        }

    } catch (error) {
        // Log any errors encountered during the process.
        logger.error(`Error while checking default license initialization. ${error}`);
    }
};


module.exports = {
    createAdminUser,
    checkDefaultSiteSettings,
    checkDefaultGitlabAdminUser,
    createDefaultLicense
};