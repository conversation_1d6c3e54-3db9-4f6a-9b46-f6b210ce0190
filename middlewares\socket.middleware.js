// Socket middleware for authentication
require('dotenv').config();
const jwt = require('jsonwebtoken');
const logger = require('../config/logger');
const { Users } = require('../models/users.model');

/**
 * Authenticate socket connection using token
 * @param {Object} socket - Socket.IO socket object
 * @param {Function} next - Next function to call
 */
const authenticateSocket = async (socket, next) => {
    try {
        logger.info(`Socket authentication started for socket: ${socket.id}`);

        // Get token from handshake auth or query parameters
        const token = socket.handshake.auth.token || socket.handshake.query.token;

        if (!token) {
            logger.error(`Authentication error: Token not provided for socket ${socket.id}`);
            return next(new Error('Authentication error: Token not provided'));
        }

        logger.info(`Token received for socket ${socket.id}`);

        try {
            // Verify the token
            const decoded = jwt.verify(token, process.env.JWT_FRONT_SECRET_KEY);

            if (!decoded || !decoded.data || !decoded.data._id) {
                logger.error(`Invalid token format for socket ${socket.id}`);
                return next(new Error('Authentication error: Invalid token format'));
            }

            logger.info(`Token verified for user: ${decoded.data._id}`);

            // Check if user exists
            const user = await Users.findOne({
                _id: decoded.data._id,
                is_active: true,
                is_deleted: { $ne: true }
            }).lean();

            if (!user) {
                logger.error(`User not found or inactive: ${decoded.data._id}`);
                return next(new Error('Authentication error: User not found or inactive'));
            }

            logger.info(`User found: ${user._id} (${user.username})`);

            // Attach user data to socket for later use
            socket.user = {
                _id: user._id,
                username: user.username
            };

            logger.info(`Socket authenticated for user: ${user._id}`);
            next();
        } catch (jwtError) {
            logger.error(`JWT verification error: ${jwtError.message}`);
            return next(new Error(`Authentication error: Invalid token - ${jwtError.message}`));
        }
    } catch (error) {
        logger.error(`Socket authentication error: ${error}`);
        next(new Error(`Authentication error: ${error.message}`));
    }
};

module.exports = {
    authenticateSocket
};
