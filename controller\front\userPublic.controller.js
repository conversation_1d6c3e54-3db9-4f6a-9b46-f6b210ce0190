// Constants declaration
const constants = require('../../config/constants');
const { componentState, filterTypes } = require('../../config/component.constant');
const { repositoryState, publishState } = require('../../config/gitlab.constant');

// Service declaration
const { ReS, escapeRegex } = require('../../services/general.helper');

// Models declaration
const Components = require('../../models/component.model').Components;
const Users = require('../../models/users.model').Users;
const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;
const UserOTPs = require('../../models/user_otps.model').UserOTPs;

// Npm declaration
const mongoose = require('mongoose');
const logger = require('../../config/logger');

async function getUserDetails(req, res) {
    try {
        const username = req.params.username;

        const pipelines = [
            {
                $match: {
                    username: username // Match user by username
                }
            },
            {
                $lookup: {
                    from: 'gitlab_users', // Join with gitlab_users collection
                    let: { user_id: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$user_id', '$$user_id'] }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                email: 1,
                                username: 1,
                                extern_uid: 1,
                                personal_access_tokens: 1,
                                personal_access_token_name: 1,
                                token_expires_at: 1 // Project required fields
                            }
                        }
                    ],
                    as: 'gitlab_user' // Output array field for the joined data
                }
            },
            {
                $lookup: {
                    from: 'components', // Join with components collection
                    let: { creatorId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$created_by_user', '$$creatorId'] },
                                        { $eq: ['$component_state', componentState.PUBLISHED] }
                                    ]
                                }
                            }
                        },
                        {
                            $group: {
                                _id: null,
                                total_posts: { $sum: 1 }
                            }
                        }
                    ],
                    as: 'components_count'
                }
            },
            {
                $lookup: {
                    from: 'supported_platforms', // Name of the collection
                    let: { techIds: '$technologies' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $in: ['$_id', '$$techIds']
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                title: 1,
                                slug: 1,
                                image_url: 1
                            }
                        }
                    ],
                    as: 'technologies'
                }
            },
            {
                $project: {
                    avatar: 1,
                    email: 1,
                    first_name: 1,
                    last_name: 1,
                    social_links: 1,
                    biography: 1,
                    gitlab_user_exists: 1,
                    username: 1,
                    country: 1,
                    followers_count: 1,
                    following_count: 1,
                    total_likes: 1,
                    total_views: 1,
                    total_bookmarks: 1,
                    gitlab_user: {
                        $arrayElemAt: ['$gitlab_user', 0]
                    },
                    published_files: {
                        $ifNull: [{ $arrayElemAt: ['$components_count.total_posts', 0] }, 0]
                    },
                    technologies: 1
                }
            }
        ];

        // Execute the aggregation pipeline
        const userProfile = await Users.aggregate(pipelines);

        if (!userProfile.length) {
            return ReS(res, constants.resource_not_found, 'Oops! User Not Found.');
        }

        // Get the first result from the aggregation
        const userProfileObj = (userProfile.length) ? userProfile[0] : null;

        // Return the user details
        return ReS(res, constants.success_code, 'Data Fetched', userProfileObj);
    } catch (err) {
        logger.error(`Error at Front Controller getUserDetails ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getProfileStatistics(req, res) {
    try {
        // Extract GitLab username from params
        const username = req.params.username;

        // Fetch user data from username
        const user = await Users.findOne({
            username: username
        }, '_id total_views total_likes').populate({
            'path': 'technologies',
            'select': 'title slug image_url'
        }).lean();

        // If GitLab user is not found, return an error response
        if (!user) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }

        // Aggregation pipeline to calculate total likes and views for the user's components
        const pipelines = [
            {
                $match: {
                    component_state: componentState.PUBLISHED,
                    created_by_user: new mongoose.Types.ObjectId(user._id)
                }
            },
            {
                $group: {
                    _id: null,
                    totalPosts: {
                        $sum: 1
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalPosts: { $ifNull: ['$totalPosts', 0] }
                }
            }
        ];

        // Execute the aggregation pipeline
        const [result] = await Components.aggregate(pipelines);

        // Extract the statistics from the aggregation result
        const statistics = {
            totalViews: (user?.total_views) ? user?.total_views : 0,
            totalLikes: (user?.total_likes) ? user?.total_likes : 0,
            totalPosts: (result?.totalPosts) ? result?.totalPosts : 0,
            technologies: (user?.technologies) ? user?.technologies : []
        };

        // Return the statistics as a success response
        return ReS(res, constants.success_code, 'Creator profile statistics fetch successfully.', statistics);

    } catch (err) {
        console.log(err);
        // Log the error and return error response
        logger.error('Error in Front Controller getProfileStatistics:', err);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getCreatedComponents(req, res) {
    try {
        // Extract GitLab username from params
        const username = req.params.username;

        // Fetch user data from username
        const user = await Users.findOne({ username: username }, '_id').lean();

        // If GitLab user is not found, return an error response
        if (!user) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }

        // Create filter for components based on user
        const filter = {
            component_state: {
                $in: [componentState.PUBLISHED]
            },
            created_by_user: new mongoose.Types.ObjectId(user._id),
            component_type: { $exists: true }
        };

        const totalDocuments = await Components.countDocuments(filter);

        // Check if 'component_type' is provided in the request body
        if (req.body.component_type) {
            filter['component_type'] = req.body.component_type;
        }

        const filterDocuments = await Components.countDocuments(filter);

        const limit = parseInt(req.body.limit) || 12;
        const skip = parseInt(req.body.skip) || 0;

        // Aggregation pipeline
        const pipelines = [
            {
                $match: filter // Match components created by the user
            },
            {
                $sort: { created_at: -1 }
            },
            {
                $skip: skip
            },
            {
                $limit: limit
            },
            {
                $lookup: {
                    from: 'users',
                    let: {
                        user_id: '$created_by_user'
                    },
                    pipeline: [{
                        $match: {
                            $expr: {
                                $and: [{
                                    $eq: ['$_id', '$$user_id']
                                }]
                            }
                        }
                    }, {
                        $project: {
                            'email': 1,
                            'username': 1,
                            'first_name': 1,
                            'last_name': 1,
                            'avatar': 1,
                            'biography': 1
                        }
                    }],
                    as: 'created_by_user'
                }
            },
            {
                $lookup: {
                    from: 'gitlab_repositories',
                    let: {
                        public_repository_id: '$public_repository_id'
                    },
                    pipeline: [{
                        $match: {
                            $expr: {
                                $and: [{
                                    $eq: ['$_id', '$$public_repository_id']
                                }]
                            }
                        }
                    }, {
                        $project: {
                            stars: 1,
                            forks: 1,
                            gitlab_languages: 1,
                            last_pushed_at: 1
                        }
                    }],
                    as: 'public_repository_id'
                }
            },
            {
                $lookup: {
                    from: 'categories', // Join with categories collection
                    let: { categoryId: '$category_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$categoryId'] }
                            }
                        },
                        {
                            $project: {
                                category_name: 1,
                                category_slug: 1
                            }
                        }
                    ],
                    as: 'category_id'
                }
            },
            {
                $project: {
                    title: 1,
                    slug: 1,
                    image_url: 1,
                    thumbnail_url: 1,
                    video_url: 1,
                    short_description: 1,
                    long_description: 1,
                    orientation: 1,
                    views: 1,
                    likes: 1,
                    bookmarks: 1,
                    downloads: 1,
                    component_state: 1,
                    component_type: 1,
                    created_at: 1,
                    live_preview: 1,
                    gif_status: 1,
                    linked_output: 1,
                    gif_url: 1,
                    elements_data: 1,
                    element_container_meta: 1,
                    is_paid: 1,
                    category_id: {
                        $arrayElemAt: ['$category_id', 0]
                    },
                    created_by_user: {
                        $arrayElemAt: ['$created_by_user', 0]
                    },
                    public_repository_id: {
                        $arrayElemAt: ['$public_repository_id', 0]
                    }
                }
            }
        ];

        // Perform the aggregation directly on the `components` collection
        const components = await Components.aggregate(pipelines);

        // Prepare the response object
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: components
        };

        // Send response with the filtered data
        return ReS(res, constants.success_code, 'Components Fetched', responseObj);
    } catch (error) {
        // Log the error and return a server error message
        logger.error(`Error at getCreatedComponents function: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getPublicCodeSpaces(req, res) {
    try {

        // Extract GitLab username from params
        const username = req.params.username;

        // Fetch user data from username
        const user = await Users.findOne({ username: username }, '_id').lean();

        // If GitLab user is not found, return an error response
        if (!user) {
            return ReS(res, constants.resource_not_found, 'User not found.');
        }
        // Set default conditions
        const conditions = {
            user_id: new mongoose.Types.ObjectId(user?._id),
            state: repositoryState.PUBLIC,
            published_state: publishState.PUBLISHED
        };

        // Set default sort
        let sort = {
            last_pushed_at: -1
        };

        const limit = (req.body.limit) ? req.body.limit : 10;
        const skip = (req.body.skip) ? req.body.skip : 0;

        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'project_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by forks in descending order
                sort = {
                    forks: -1,
                    last_pushed_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_UPDATES) {
                // Sort by last_pushed_at in descending order
                sort = {
                    last_pushed_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            }
        }

        const totalDocuments = await GitlabRepository.countDocuments(conditions);

        const filterDocuments = await GitlabRepository.countDocuments(conditions);

        const pipelines = [
            {
                $match: conditions // Apply restrictive filters upfront
            },
            {
                $sort: sort // Sort early in the pipeline
            },
            {
                $skip: skip
            },
            {
                $limit: limit
            },
            {
                $lookup: {
                    from: 'users',
                    let: {
                        user_id: '$user_id'
                    },
                    pipeline: [{
                        $match: {
                            $expr: {
                                $and: [{
                                    $eq: ['$_id', '$$user_id']
                                }]
                            }
                        }
                    }, {
                        $project: {
                            'first_name': 1,
                            'last_name': 1,
                            'email': 1,
                            'username': 1,
                            'avatar': 1,
                            'biography': 1,
                            'country': 1
                        }
                    }],
                    as: 'user_id'
                }
            },
            // Join with components collection
            {
                $lookup: {
                    from: 'components',
                    let: { component_id: '$component_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$component_id'] }
                            }
                        },
                        {
                            $project: {
                                views: 1,
                                likes: 1,
                                bookmarks: 1
                            }
                        }
                    ],
                    as: 'component_id'
                }
            },
            {
                $project: {
                    project_name: 1,
                    description: 1,
                    state: 1,
                    forks: 1,
                    http_url_to_repo: 1,
                    user_id: 1,
                    gitlab_languages: 1,
                    created_at: 1,
                    last_pushed_at: 1,
                    published_state: 1,
                    user_id: {
                        $arrayElemAt: ['$user_id', 0]
                    },
                    detected_platforms: 1,
                    platform_id: 1,
                    component_id: {
                        $arrayElemAt: ['$component_id', 0]
                    }
                }
            }
        ];

        const codeSpaces = await GitlabRepository.aggregate(pipelines);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: codeSpaces
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller getPublicCodeSpaces: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getUserOTP(req, res) {
    try {
        const { email } = req.query;
        const user = await UserOTPs.findOne({ email }).sort({ created_at: -1 }).lean();

        if (user?.otp) {
            return ReS(res, constants.success_code, 'Fetched', { otp: user.otp });
        }

        return ReS(res, constants.resource_not_found, 'Oops! OTP not found');
    } catch (error) {
        console.error(`getUserOTP error: ${error.message || error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

module.exports = {
    getUserDetails,
    getProfileStatistics,
    getCreatedComponents,
    getPublicCodeSpaces,
    getUserOTP
};