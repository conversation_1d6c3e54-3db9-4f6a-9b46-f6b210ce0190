const express = require('express');
const router = express.Router();

const { createTemplate, getSingleTemplate, getTemplates, updateTemplate, deleteTemplate, updateTemplateStatus } = require('../../controller/cms/templates.controller');
const { createTemplateValidation, updateTemplateValidation, updateTemplateStatusValidation } = require('../../middlewares/validations/cms/template/templateValidation');


router.post('/create', createTemplateValidation, createTemplate);
router.get('/details/:id', getSingleTemplate);
router.post('/list', getTemplates);
router.put('/update/:id', updateTemplateValidation, updateTemplate);
router.delete('/delete/:id', deleteTemplate);
router.put('/update/status/:id', updateTemplateStatusValidation, updateTemplateStatus);

module.exports = router;