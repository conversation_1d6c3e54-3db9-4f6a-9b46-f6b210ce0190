const { approveOrRejectRequest } = require('../../../../validations/cms/mpc_bonus_point_request/bonusPointRequestValidation');
class BonusPintRequestValidationMiddleware {
    approveOrRejectRequestValidation(req, res, next) {
        const { value, error } = approveOrRejectRequest(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new BonusPintRequestValidationMiddleware(); 