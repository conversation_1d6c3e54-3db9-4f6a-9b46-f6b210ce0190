const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { collectionState } = require('../config/component.constant');

const collectionSchema = new Schema({
    title: {
        type: String,
        required: true
    },
    slug: {
        type: String,
        required: true,
        unique: true
    },
    description: {
        type: String
    },
    background: {
        type: String
    },
    views: {
        type: Number,
        default: 0
    },
    tags: [{
        type: String
    }],
    created_by_user: {
        type: Schema.Types.ObjectId,
        ref: 'users'
    },
    state: {
        type: String,
        default: collectionState.ACTIVE_DRAFT,
        enum: Object.values(collectionState)
    },
    last_published_at: {
        type: Date
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

collectionSchema.set('autoIndex', true);

const Collection = mongoose.model('collections', collectionSchema);

module.exports = {
    Collection
};
