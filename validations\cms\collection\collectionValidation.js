const Joi = require('joi');

class CollectionValidation {
    createOrEditCollection(params) {
        const schema = Joi.object({
            title: Joi.string()
                .trim()
                .min(3)
                .max(100)
                .required()
                .messages({
                    'string.base': '"title" should be a type of string',
                    'string.empty': '"title" cannot be an empty field',
                    'string.min': '"title" should have a minimum length of {#limit}',
                    'string.max': '"title" should have a maximum length of {#limit}',
                    'any.required': '"title" is a required field'
                })
        });
        return schema.validate(params);
    }
}

module.exports = new CollectionValidation();