const { Worker } = require('bullmq');
const redis = require('../config/redis');
const { Components } = require('../models/component.model');
const { DraftComponents } = require('../models/draft_components.model');
const { html2gifWithInteractions } = require('../services/elements.service');
const { thumbnailStatus } = require('../config/component.constant');
const { uploadBufferToS3 } = require('./../services/general.helper');

const setupGifWorker = () => {
    const worker = new Worker('gif-queue', async (job) => {

        console.log(`GIF job ${job.id} started`);

        const { elementId } = job.data;
        const element = await Components.findOne({ _id: elementId }, 'linked_output component_draft_id');

        if (!element) throw new Error('Element not found');

        if (!element?.linked_output) throw new Error('Element HTML not found');

        await Components.updateOne({
            _id: elementId
        }, {
            $set: { gif_status: thumbnailStatus.PROCESSING }
        });

        try {
            // Convert HTML content to GIF buffer with interactions
            const gifBuffer = await html2gifWithInteractions(element?.linked_output);

            // Prepare file details
            const timestamp = Date.now();
            const filename = `${timestamp}.gif`;
            const documentPath = `assets/${filename}`;
            const mimeType = 'image/gif';

            // Upload the GIF buffer to S3
            const uploadedFile = await uploadBufferToS3(gifBuffer, documentPath, mimeType);

            console.log(`GIF generated successfully with path => ${uploadedFile} for element => ${elementId}`);

            // Update GIF path in database in published elements
            await Components.updateOne({
                _id: elementId
            }, {
                $set: {
                    gif_url: documentPath,
                    gif_status: thumbnailStatus.DONE
                }
            });
            // Update GIF path in database in draft elements
            await DraftComponents.updateOne({
                _id: element?.component_draft_id
            }, {
                $set: {
                    gif_url: documentPath,
                    gif_status: thumbnailStatus.DONE
                }
            });
        } catch (err) {
            console.error('GIF generation failed:', err);
            await Components.updateOne({
                _id: elementId
            }, {
                $set: { gif_status: thumbnailStatus.FAILED }
            });
        }
    }, {
        connection: redis
    });

    worker.on('completed', (job) => {
        console.log(`GIF job ${job.id} completed`);
    });

    worker.on('failed', (job, err) => {
        console.error(`GIF job ${job.id} failed:`, err.message);
    });
};

module.exports = { setupGifWorker };
