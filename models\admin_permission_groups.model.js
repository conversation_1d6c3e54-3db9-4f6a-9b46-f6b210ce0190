const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const AdminPermissionGroupSchema = new Schema({
    group_name: {
        type: String,
        trim: true,
        required: true
    },
    module_name: {
        type: String,
        trim: true,
        required: false
    },
    permission_list: [{
        type: mongoose.Types.ObjectId,
        required: true,
        ref: 'admin_permissions',
        trim: true
    }],
    is_active: {
        type: Boolean,
        required: true,
        default: true
    },
    created_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    updated_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const AdminPermissionGroups = mongoose.model('admin_permission_groups', AdminPermissionGroupSchema);

module.exports = {
    AdminPermissionGroups
};