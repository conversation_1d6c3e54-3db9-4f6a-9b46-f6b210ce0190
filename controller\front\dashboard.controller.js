// Constants declaration
const constants = require('../../config/constants');

// Service declaration
const { ReS } = require('../../services/general.helper');

// Models declaration
const ComponentLikes = require('../../models/component_likes.model').ComponentLikes;

// Npm declaration
const mongoose = require('mongoose');
const logger = require('../../config/logger');
const moment = require('moment');

async function fetchUserAppreciationStatistics(req, res) {
    try {

        const userId = req.session._id;

        const pipelines = [];

        const pointHistory = await ComponentLikes.aggregate(pipelines);

        return ReS(res, constants.success_code, 'Point History Fetched', pointHistory);
    } catch (error) {
        // Log the error and send a generic server error response
        logger.error(`Error in fetchUserAppreciationStatistics: ${error}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

module.exports = {
    fetchUserAppreciationStatistics
};
