const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const AdminPermissionsSchema = new Schema({
    display_name: {
        type: String,
        required: true
    },
    name: {
        type: String,
        required: true,
        unique: true
    },
    module: {
        type: String,
        required: true
    },
    is_active: {
        type: Boolean,
        default: true
    }
},
{
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
}
);

const AdminPermissions = mongoose.model('admin_permissions', AdminPermissionsSchema);

module.exports = {
    AdminPermissions
};